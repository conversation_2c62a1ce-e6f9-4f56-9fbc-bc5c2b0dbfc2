# 版路分析管理員API文檔

## 概述

版路分析管理員API提供完整的版路分析計算任務管理、結果查詢、系統監控和Excel匯出功能。所有API都需要管理員權限才能存取。

## 認證

所有API請求都需要在Header中包含有效的JWT token：

```
Authorization: Bearer <your_jwt_token>
```

用戶必須具有管理員權限 (`isAdmin: true`) 才能存取管理員API。

## API端點

### 基礎URL
- 管理員API: `/api/admin/ball-follow`
- 用戶API: `/api/ball-follow`

---

## 任務管理

### 1. 取得任務列表

**GET** `/api/admin/ball-follow/tasks`

查詢所有計算任務的列表，支援分頁和篩選。

**查詢參數:**
- `page` (int, 可選): 頁碼，預設為1
- `limit` (int, 可選): 每頁筆數，預設為20，最大100
- `status` (string, 可選): 任務狀態篩選
  - `pending`: 待處理
  - `running`: 執行中
  - `completed`: 已完成
  - `failed`: 失敗
  - `stopped`: 已停止
- `lotto_type` (string, 可選): 彩種篩選
  - `daily539`: 今彩539
  - `lotto649`: 大樂透
  - `super_lotto638`: 威力彩
  - `lotto_hk`: 六合彩

**回應範例:**
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "id": 1,
        "lotto_type": "daily539",
        "period": 113001,
        "task_status": "completed",
        "total_combinations": 1350,
        "completed_combinations": 1350,
        "failed_combinations": 0,
        "progress_percentage": "100.00",
        "start_time": "2024-01-15T10:30:00Z",
        "end_time": "2024-01-15T11:45:00Z",
        "actual_duration": 4500,
        "priority": 1,
        "triggered_by": "scheduler",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "total_pages": 3
    }
  }
}
```

### 2. 取得任務詳情

**GET** `/api/admin/ball-follow/tasks/{id}`

取得特定任務的詳細資訊，包含子任務統計。

**路徑參數:**
- `id` (int): 任務ID

**回應範例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "lotto_type": "daily539",
    "period": 113001,
    "task_status": "completed",
    "total_combinations": 1350,
    "completed_combinations": 1350,
    "failed_combinations": 0,
    "remaining_combinations": 0,
    "progress_percentage": "100.00",
    "start_time": "2024-01-15T10:30:00Z",
    "end_time": "2024-01-15T11:45:00Z",
    "estimated_duration": 7200,
    "actual_duration": 4500,
    "config_snapshot": {
      "max_concurrent_calculations": 3,
      "memory_threshold_percent": 80
    },
    "subtask_stats": [
      {
        "status": "completed",
        "count": 1350
      }
    ]
  }
}
```

### 3. 手動觸發計算

**POST** `/api/admin/ball-follow/trigger`

手動觸發特定期數的版路分析計算。

**請求體:**
```json
{
  "lotto_type": "daily539",
  "period": 113001,
  "force_recalculate": false
}
```

**參數說明:**
- `lotto_type` (string, 必填): 彩種
- `period` (int, 必填): 期數
- `force_recalculate` (bool, 可選): 是否強制重新計算，預設為false

**回應範例:**
```json
{
  "success": true,
  "data": {
    "task_id": 123,
    "lotto_type": "daily539",
    "period": 113001,
    "status": "pending",
    "message": "計算任務已建立"
  }
}
```

### 4. 停止任務

**POST** `/api/admin/ball-follow/tasks/{id}/stop`

停止正在執行的計算任務。

**路徑參數:**
- `id` (int): 任務ID

**回應範例:**
```json
{
  "success": true,
  "data": {
    "task_id": 123,
    "status": "stopped",
    "message": "任務已停止"
  }
}
```

---

## 結果查詢

### 5. 查詢計算結果

**GET** `/api/admin/ball-follow/results`

取得特定期數的所有計算結果。

**查詢參數:**
- `lotto_type` (string, 必填): 彩種
- `period` (int, 必填): 期數

**回應範例:**
```json
{
  "success": true,
  "data": {
    "lotto_type": "daily539",
    "period": 113001,
    "total_results": 1350,
    "grouped_results": {
      "(1,1,1)-P30-R10": [
        {
          "id": 1,
          "comb1": 1,
          "comb2": 1,
          "comb3": 1,
          "period_num": 30,
          "max_range": 10,
          "predict_numbers": {
            "numbers": [1, 5, 12, 23, 35],
            "appearances": {"1": 15, "5": 12},
            "probabilities": {"1": 0.3, "5": 0.24}
          },
          "calculation_duration": 3,
          "created_at": "2024-01-15T11:45:00Z"
        }
      ]
    }
  }
}
```

### 6. 查詢結果摘要

**GET** `/api/admin/ball-follow/summary`

取得計算結果的統計摘要。

**查詢參數:**
- `lotto_type` (string, 必填): 彩種
- `period` (int, 必填): 期數

**回應範例:**
```json
{
  "success": true,
  "data": {
    "total_combinations": 1350,
    "successful_combinations": 1350,
    "failed_combinations": 0,
    "average_calculation_time": 2.67,
    "top_predict_numbers": {
      "1": 45,
      "5": 38,
      "12": 32
    },
    "calculation_completion_time": "2024-01-15T11:45:00Z"
  }
}
```

### 7. 批量查詢結果

**POST** `/api/admin/ball-follow/batch-results`

批量查詢多個期數的計算結果。

**請求體:**
```json
{
  "lotto_type": "daily539",
  "periods": [113001, 113002, 113003],
  "parameters": {
    "comb1": 1,
    "comb2": 1,
    "comb3": 1,
    "period_num": 120,
    "max_range": 15
  }
}
```

**參數說明:**
- `lotto_type` (string, 必填): 彩種
- `periods` (array, 必填): 期數列表，最多50個
- `parameters` (object, 可選): 特定參數組合篩選

**回應範例:**
```json
{
  "success": true,
  "data": {
    "lotto_type": "daily539",
    "total_periods": 3,
    "results": [
      {
        "period": 113001,
        "status": "success",
        "result": { /* 計算結果 */ }
      },
      {
        "period": 113002,
        "status": "not_found",
        "error": "找不到計算結果"
      }
    ]
  }
}
```

---

## 系統監控

### 8. 取得系統狀態

**GET** `/api/admin/ball-follow/system-status`

取得系統資源使用狀況和運行狀態。

**回應範例:**
```json
{
  "success": true,
  "data": {
    "memory_usage_mb": 512,
    "memory_usage_percent": 65,
    "active_tasks": 2,
    "concurrent_calculations": 3,
    "queue_length": 5,
    "last_gc_time": "2024-01-15T12:00:00Z",
    "uptime_seconds": 3600,
    "status": "running"
  }
}
```

### 9. 更新系統配置

**PUT** `/api/admin/ball-follow/config`

更新系統運行配置。

**請求體:**
```json
{
  "max_concurrent_calculations": 3,
  "memory_threshold_percent": 80,
  "auto_gc_enabled": true
}
```

**回應範例:**
```json
{
  "success": true,
  "data": {
    "message": "配置已更新",
    "config": {
      "max_concurrent_calculations": 3,
      "memory_threshold_percent": 80,
      "auto_gc_enabled": true
    }
  }
}
```

### 10. 查詢歷史記錄

**GET** `/api/admin/ball-follow/history`

取得計算歷史記錄。

**查詢參數:**
- `lotto_type` (string, 必填): 彩種
- `limit` (int, 可選): 顯示筆數，預設為10，最大100

**回應範例:**
```json
{
  "success": true,
  "data": {
    "lotto_type": "daily539",
    "history": [
      {
        "id": 123,
        "period": 113001,
        "task_status": "completed",
        "success_rate": "100.00",
        "start_time": "2024-01-15T10:30:00Z",
        "end_time": "2024-01-15T11:45:00Z",
        "duration": 4500,
        "triggered_by": "scheduler",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 1
  }
}
```

---

## 匯出功能

### 11. 下載Excel報表

**GET** `/api/admin/ball-follow/export`

下載特定期數的Excel分析報表。

**查詢參數:**
- `lotto_type` (string, 必填): 彩種
- `period` (int, 必填): 期數
- `format` (string, 可選): 匯出格式，目前只支援"excel"

**回應:**
- 成功時返回Excel檔案 (application/vnd.openxmlformats-officedocument.spreadsheetml.sheet)
- 計算未完成時返回HTTP 202和進度資訊

**Excel報表包含以下工作表:**
1. **摘要** - 計算結果總覽
2. **預測號碼統計** - 各參數組合的預測號碼和出現次數
3. **未出現號碼** - 各參數組合的未出現號碼列表
4. **尾數統計** - 尾數出現次數和機率統計
5. **參數組合列表** - 所有1350種參數組合的計算狀態

---

## 用戶API (一般用戶可用)

### 12. 查詢任務狀態

**GET** `/api/ball-follow/task-status`

查詢特定期數的計算狀態 (不需要管理員權限)。

**查詢參數:**
- `lotto_type` (string, 必填): 彩種
- `period` (int, 必填): 期數

### 13. 查詢結果 (限制版)

**GET** `/api/ball-follow/results`

查詢計算結果 (一般用戶有時間限制)。

### 14. 下載Excel (限制版)

**GET** `/api/ball-follow/export`

下載Excel報表 (一般用戶有下載次數限制)。

---

## 健康檢查

### 15. 健康檢查

**GET** `/api/ball-follow/health`

檢查服務運行狀態 (無需認證)。

### 16. 版本資訊

**GET** `/api/ball-follow/version`

取得API版本和功能資訊 (無需認證)。

---

## 錯誤處理

所有API都遵循統一的錯誤回應格式：

```json
{
  "error": "錯誤類型",
  "message": "詳細錯誤訊息"
}
```

**常見HTTP狀態碼:**
- `200` - 成功
- `201` - 建立成功
- `202` - 已接受 (計算進行中)
- `400` - 請求參數錯誤
- `401` - 未授權
- `403` - 權限不足
- `404` - 資源不存在
- `409` - 衝突 (如重複計算)
- `500` - 伺服器內部錯誤

---

## 使用範例

### 完整的計算流程

1. **觸發計算**
```bash
curl -X POST "http://localhost:8080/api/admin/ball-follow/trigger" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"lotto_type": "daily539", "period": 113001}'
```

2. **監控進度**
```bash
curl "http://localhost:8080/api/admin/ball-follow/tasks/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

3. **查詢結果**
```bash
curl "http://localhost:8080/api/admin/ball-follow/results?lotto_type=daily539&period=113001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

4. **下載報表**
```bash
curl "http://localhost:8080/api/admin/ball-follow/export?lotto_type=daily539&period=113001" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o "analysis_report.xlsx"
```

---

## 注意事項

1. **權限要求**: 管理員API需要 `isAdmin: true` 的用戶權限
2. **計算時間**: 完整的1350種參數組合計算可能需要數小時
3. **記憶體使用**: 大量計算會消耗較多記憶體，系統會自動監控和管理
4. **並行限制**: 系統限制同時進行的計算數量以確保穩定性
5. **資料保留**: 計算結果會永久保存，可重複查詢和下載
