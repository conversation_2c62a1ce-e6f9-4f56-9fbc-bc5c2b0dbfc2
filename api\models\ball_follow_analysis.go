package models

import (
	"crypto/sha256"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// BallFollowJSON 自定義類型，用於儲存JSON資料
type BallFollowJSON map[string]interface{}

// Scan 實現 sql.Scanner 接口
func (bfj *BallFollowJSON) Scan(value interface{}) error {
	if value == nil {
		*bfj = make(BallFollowJSON)
		return nil
	}
	return json.Unmarshal(value.([]byte), bfj)
}

// Value 實現 driver.Valuer 接口
func (bfj BallFollowJSON) Value() (driver.Value, error) {
	if bfj == nil {
		return nil, nil
	}
	return json.Marshal(bfj)
}

// PredictNumbersData 預測號碼統計資料結構
type PredictNumbersData struct {
	Numbers          []int            `json:"numbers"`           // 預測號碼列表
	Appearances      map[int]int      `json:"appearances"`       // 號碼出現次數
	Probabilities    map[int]float64  `json:"probabilities"`     // 號碼出現機率
	SortedByFreq     []int            `json:"sorted_by_freq"`    // 按頻率排序的號碼
	TotalPredictions int              `json:"total_predictions"` // 總預測次數
}

// TailStatisticsData 尾數統計資料結構
type TailStatisticsData struct {
	TailAppearances  map[int]int     `json:"tail_appearances"`  // 尾數出現次數
	TailProbabilities map[int]float64 `json:"tail_probabilities"` // 尾數出現機率
	SortedTails      []int           `json:"sorted_tails"`       // 按頻率排序的尾數
	TotalTails       int             `json:"total_tails"`        // 總尾數統計次數
}

// TargetAppearancesData 目標號碼出現次數資料結構
type TargetAppearancesData struct {
	NumberAppearances map[int]int `json:"number_appearances"` // 號碼出現次數
	TotalTargets      int         `json:"total_targets"`      // 總目標數量
	MaxAppearances    int         `json:"max_appearances"`    // 最大出現次數
	MinAppearances    int         `json:"min_appearances"`    // 最小出現次數
}

// BallFollowAnalysisResult 版路分析結果模型
type BallFollowAnalysisResult struct {
	ID                     uint64        `gorm:"primaryKey;autoIncrement" json:"id"`
	LottoType              LottoTypeStr  `gorm:"type:varchar(20);not null" json:"lotto_type"`
	Period                 int           `gorm:"not null" json:"period"`
	AnalysisDate           time.Time     `gorm:"type:date;not null" json:"analysis_date"`
	Comb1                  uint8         `gorm:"type:tinyint unsigned;not null" json:"comb1"`
	Comb2                  uint8         `gorm:"type:tinyint unsigned;not null" json:"comb2"`
	Comb3                  uint8         `gorm:"type:tinyint unsigned;not null" json:"comb3"`
	PeriodNum              uint16        `gorm:"type:smallint unsigned;not null" json:"period_num"`
	MaxRange               uint8         `gorm:"type:tinyint unsigned;not null" json:"max_range"`
	AnalysisPeriods        uint8         `gorm:"type:tinyint unsigned;not null;default:50" json:"analysis_periods"`
	PredictNumbers         BallFollowJSON `gorm:"type:json;not null" json:"predict_numbers"`
	NonAppearedNumbers     BallFollowJSON `gorm:"type:json;not null" json:"non_appeared_numbers"`
	TailStatistics         BallFollowJSON `gorm:"type:json;not null" json:"tail_statistics"`
	TargetAppearances      BallFollowJSON `gorm:"type:json;not null" json:"target_appearances"`
	NonAppearedByFrequency BallFollowJSON `gorm:"type:json" json:"non_appeared_by_frequency"`
	TailNumAppearances     BallFollowJSON `gorm:"type:json" json:"tail_num_appearances"`
	CalculationHash        string        `gorm:"type:varchar(64);not null;uniqueIndex" json:"calculation_hash"`
	CalculationDuration    *uint32       `gorm:"type:int unsigned" json:"calculation_duration"`
	DataSourcePeriods      BallFollowJSON `gorm:"type:json" json:"data_source_periods"`
	CreatedAt              time.Time     `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt              time.Time     `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (BallFollowAnalysisResult) TableName() string {
	return "ball_follow_analysis_results"
}

// GenerateCalculationHash 生成計算參數的雜湊值
func (r *BallFollowAnalysisResult) GenerateCalculationHash() string {
	data := fmt.Sprintf("%s-%d-%d-%d-%d-%d-%d",
		r.LottoType, r.Period, r.Comb1, r.Comb2, r.Comb3, r.PeriodNum, r.MaxRange)
	hash := sha256.Sum256([]byte(data))
	return fmt.Sprintf("%x", hash)
}

// BeforeCreate GORM hook，在建立記錄前自動生成雜湊值
func (r *BallFollowAnalysisResult) BeforeCreate() error {
	if r.CalculationHash == "" {
		r.CalculationHash = r.GenerateCalculationHash()
	}
	if r.AnalysisDate.IsZero() {
		r.AnalysisDate = time.Now()
	}
	return nil
}

// TaskStatus 任務狀態枚舉
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusStopped   TaskStatus = "stopped"
)

// SubtaskStatus 子任務狀態枚舉
type SubtaskStatus string

const (
	SubtaskStatusPending   SubtaskStatus = "pending"
	SubtaskStatusRunning   SubtaskStatus = "running"
	SubtaskStatusCompleted SubtaskStatus = "completed"
	SubtaskStatusFailed    SubtaskStatus = "failed"
)

// TriggerType 觸發方式枚舉
type TriggerType string

const (
	TriggerTypeScheduler TriggerType = "scheduler"
	TriggerTypeManual    TriggerType = "manual"
	TriggerTypeAPI       TriggerType = "api"
)

// ConfigSnapshot 配置快照資料結構
type ConfigSnapshot struct {
	MaxConcurrentCalculations int  `json:"max_concurrent_calculations"`
	MemoryThresholdPercent     int  `json:"memory_threshold_percent"`
	AutoGCEnabled              bool `json:"auto_gc_enabled"`
	RetryLimit                 int  `json:"retry_limit"`
	CalculationTimeoutMinutes  int  `json:"calculation_timeout_minutes"`
}

// ProgressDetails 進度詳細資訊資料結構
type ProgressDetails struct {
	CurrentCombination    string    `json:"current_combination"`
	EstimatedRemainingTime int      `json:"estimated_remaining_time"`
	AverageCalculationTime float64  `json:"average_calculation_time"`
	LastUpdateTime         time.Time `json:"last_update_time"`
	MemoryUsageMB          int       `json:"memory_usage_mb"`
	ErrorCount             int       `json:"error_count"`
}

// BallFollowCalculationTask 版路分析計算任務模型
type BallFollowCalculationTask struct {
	ID                    uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	LottoType             LottoTypeStr   `gorm:"type:varchar(20);not null" json:"lotto_type"`
	Period                int            `gorm:"not null" json:"period"`
	TaskStatus            TaskStatus     `gorm:"type:varchar(20);not null;default:'pending'" json:"task_status"`
	TotalCombinations     uint16         `gorm:"type:smallint unsigned;not null;default:1350" json:"total_combinations"`
	CompletedCombinations uint16         `gorm:"type:smallint unsigned;not null;default:0" json:"completed_combinations"`
	FailedCombinations    uint16         `gorm:"type:smallint unsigned;not null;default:0" json:"failed_combinations"`
	StartTime             *time.Time     `gorm:"type:timestamp" json:"start_time"`
	EndTime               *time.Time     `gorm:"type:timestamp" json:"end_time"`
	EstimatedDuration     *uint32        `gorm:"type:int unsigned" json:"estimated_duration"`
	ActualDuration        *uint32        `gorm:"type:int unsigned" json:"actual_duration"`
	ErrorMessage          *string        `gorm:"type:text" json:"error_message"`
	RetryCount            uint8          `gorm:"type:tinyint unsigned;not null;default:0" json:"retry_count"`
	Priority              uint8          `gorm:"type:tinyint unsigned;not null;default:5" json:"priority"`
	TriggeredBy           TriggerType    `gorm:"type:varchar(50);not null;default:'scheduler'" json:"triggered_by"`
	ConfigSnapshot        BallFollowJSON `gorm:"type:json" json:"config_snapshot"`
	ProgressDetails       BallFollowJSON `gorm:"type:json" json:"progress_details"`
	CreatedAt             time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt             time.Time      `gorm:"autoUpdateTime" json:"updated_at"`

	// 關聯
	Subtasks []BallFollowSubtask `gorm:"foreignKey:TaskID" json:"subtasks,omitempty"`
}

// TableName 指定表名
func (BallFollowCalculationTask) TableName() string {
	return "ball_follow_calculation_tasks"
}

// GetProgressPercentage 計算進度百分比
func (t *BallFollowCalculationTask) GetProgressPercentage() float64 {
	if t.TotalCombinations == 0 {
		return 0
	}
	return float64(t.CompletedCombinations) / float64(t.TotalCombinations) * 100
}

// GetRemainingCombinations 取得剩餘組合數
func (t *BallFollowCalculationTask) GetRemainingCombinations() uint16 {
	return t.TotalCombinations - t.CompletedCombinations - t.FailedCombinations
}

// IsCompleted 檢查任務是否完成
func (t *BallFollowCalculationTask) IsCompleted() bool {
	return t.TaskStatus == TaskStatusCompleted
}

// IsFailed 檢查任務是否失敗
func (t *BallFollowCalculationTask) IsFailed() bool {
	return t.TaskStatus == TaskStatusFailed
}

// IsRunning 檢查任務是否正在執行
func (t *BallFollowCalculationTask) IsRunning() bool {
	return t.TaskStatus == TaskStatusRunning
}

// BallFollowSubtask 版路分析參數組合子任務模型
type BallFollowSubtask struct {
	ID               uint64        `gorm:"primaryKey;autoIncrement" json:"id"`
	TaskID           uint64        `gorm:"not null" json:"task_id"`
	Comb1            uint8         `gorm:"type:tinyint unsigned;not null" json:"comb1"`
	Comb2            uint8         `gorm:"type:tinyint unsigned;not null" json:"comb2"`
	Comb3            uint8         `gorm:"type:tinyint unsigned;not null" json:"comb3"`
	PeriodNum        uint16        `gorm:"type:smallint unsigned;not null" json:"period_num"`
	MaxRange         uint8         `gorm:"type:tinyint unsigned;not null" json:"max_range"`
	SubtaskStatus    SubtaskStatus `gorm:"type:varchar(20);not null;default:'pending'" json:"subtask_status"`
	StartTime        *time.Time    `gorm:"type:timestamp" json:"start_time"`
	EndTime          *time.Time    `gorm:"type:timestamp" json:"end_time"`
	Duration         *uint32       `gorm:"type:int unsigned" json:"duration"`
	ErrorMessage     *string       `gorm:"type:text" json:"error_message"`
	RetryCount       uint8         `gorm:"type:tinyint unsigned;not null;default:0" json:"retry_count"`
	ResultID         *uint64       `gorm:"type:bigint unsigned" json:"result_id"`
	MemoryUsageMB    *uint32       `gorm:"type:int unsigned" json:"memory_usage_mb"`
	CalculationHash  *string       `gorm:"type:varchar(64)" json:"calculation_hash"`
	WorkerID         *string       `gorm:"type:varchar(50)" json:"worker_id"`
	CreatedAt        time.Time     `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time     `gorm:"autoUpdateTime" json:"updated_at"`

	// 關聯
	Task   BallFollowCalculationTask  `gorm:"foreignKey:TaskID" json:"task,omitempty"`
	Result *BallFollowAnalysisResult `gorm:"foreignKey:ResultID" json:"result,omitempty"`
}

// TableName 指定表名
func (BallFollowSubtask) TableName() string {
	return "ball_follow_subtasks"
}

// GetParameterString 取得參數組合字串表示
func (s *BallFollowSubtask) GetParameterString() string {
	return fmt.Sprintf("(%d,%d,%d)-P%d-R%d", s.Comb1, s.Comb2, s.Comb3, s.PeriodNum, s.MaxRange)
}

// GenerateCalculationHash 生成計算參數的雜湊值
func (s *BallFollowSubtask) GenerateCalculationHash(lottoType LottoTypeStr, period int) string {
	data := fmt.Sprintf("%s-%d-%d-%d-%d-%d-%d",
		lottoType, period, s.Comb1, s.Comb2, s.Comb3, s.PeriodNum, s.MaxRange)
	hash := sha256.Sum256([]byte(data))
	return fmt.Sprintf("%x", hash)
}

// IsCompleted 檢查子任務是否完成
func (s *BallFollowSubtask) IsCompleted() bool {
	return s.SubtaskStatus == SubtaskStatusCompleted
}

// IsFailed 檢查子任務是否失敗
func (s *BallFollowSubtask) IsFailed() bool {
	return s.SubtaskStatus == SubtaskStatusFailed
}

// IsRunning 檢查子任務是否正在執行
func (s *BallFollowSubtask) IsRunning() bool {
	return s.SubtaskStatus == SubtaskStatusRunning
}

// GetDurationSeconds 取得執行時間（秒）
func (s *BallFollowSubtask) GetDurationSeconds() uint32 {
	if s.Duration != nil {
		return *s.Duration
	}
	if s.StartTime != nil && s.EndTime != nil {
		return uint32(s.EndTime.Sub(*s.StartTime).Seconds())
	}
	return 0
}
