<template>
  <q-page class="justify-center">
    <q-card class="q-mx-auto q-py-sm q-px-sm">
      <q-card-section class="q-gutter-md">
        <div class="row items-center">
          <div class="col-12 col-sm-6 q-pa-sm">
            <label class="text-h6 text-weight-bold">請選擇彩種</label>
            <q-select
              outlined
              dense
              v-model="drawType"
              @update:model-value="getLottoList"
              :options="lottoTypeOptions"
              class="text-h6"
              emit-value
              map-options
            />
          </div>

          <div class="col-12 col-sm-6 q-pa-sm">
            <label class="text-h6 text-weight-bold">請選擇開獎年月</label>
            <div class="year-month-selector" @click="qDateProxy?.show">
              <q-input
                outlined
                dense
                v-model="formatDate"
                class="text-h6"
                readonly
              >
                <template v-slot:append>
                  <q-icon id="qDate" name="event" class="cursor-pointer">
                    <q-popup-proxy
                      ref="qDateProxy"
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-card>
                        <q-card-section>
                          <div class="row items-center justify-center">
                            <q-btn
                              icon="keyboard_arrow_left"
                              flat
                              round
                              dense
                              @click="changeYear(-1)"
                              v-if="yearSelector > 2007"
                            />
                            <q-btn
                              :label="`民國${(
                                yearSelector - 1911
                              ).toString()} 年`"
                              flat
                              no-caps
                              class="text-h6"
                            />
                            <q-btn
                              icon="keyboard_arrow_right"
                              flat
                              round
                              dense
                              @click="changeYear(1)"
                              v-if="yearSelector < currentYear"
                            />
                          </div>
                        </q-card-section>
                        <q-card-section>
                          <div class="row q-col-gutter-md justify-center">
                            <template
                              v-for="month in months"
                              :key="month.value"
                            >
                              <div class="col-4">
                                <q-btn
                                  :label="month.label"
                                  :text-color="
                                    yearSelector === currentYear &&
                                    month.value === currentMonth
                                      ? 'orange'
                                      : 'black'
                                  "
                                  :color="
                                    yearSelector === selectedYear &&
                                    month.value === selectedMonth
                                      ? 'yellow'
                                      : ''
                                  "
                                  class="full-width text-subtitle1 text-weight-bold"
                                  @click="setDate(yearSelector, month.value)"
                                  v-if="
                                    !(
                                      yearSelector == currentYear &&
                                      month.value > currentMonth
                                    )
                                  "
                                />
                              </div>
                            </template>
                          </div>
                        </q-card-section>
                      </q-card>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <div class="row q-mt-xl" v-if="items.length == 0">
      <q-card bordered class="ball-card full-width q-mb-md">
        <q-card-section class="text-center q-py-md">
          <div class="text-h6">查無開獎資料</div>
        </q-card-section>
      </q-card>
    </div>

    <div class="row q-mt-xl">
      <q-card
        bordered
        v-for="item in items"
        :key="item.period"
        class="ball-card full-width q-mb-md"
      >
        <q-card-section class="q-py-md">
          <div class="row q-gutter-y-md">
            <div class="col-12 col-sm-4 draw-title text-center">
              <div class="text-period">第 {{ item.period }} 期</div>
              <div class="text-draw-date">{{ item.draw_date }}</div>
            </div>

            <div class="col-12 col-sm-6 self-center">
              <div class="row justify-center">
                <template v-for="number in item.draw_number_size" :key="number">
                  <div class="col-auto">
                    <div class="ball" :key="number">
                      {{ paddingZero(number) }}
                    </div>
                  </div>
                </template>
                <div class="col-auto" v-if="item.special_number">
                  <div class="ball special-number" :key="item.special_number">
                    {{ paddingZero(item.special_number) }}
                  </div>
                </div>
              </div>
            </div>

            <div
              class="col-12 col-sm-2 self-center text-center"
              v-if="isSelectRef"
            >
              <q-btn
                label="進入"
                class="text-subtitle1 lotto-btn q-mx-auto"
                @click="goRd1(item)"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { QPopupProxy } from 'quasar';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { lottoTypeOptions } from '@/constants/lottoConstants';
import { useLottoStore } from '@/stores/lotto';
import { paddingZero } from '@/utils';

defineOptions({
  name: 'IndexPage',
});

const props = defineProps<{
  drawTypeQuery: string;
  dateQuery: string;
  isSelectRef: boolean;
}>();

const emit = defineEmits(['selectRef']);

const router = useRouter();

const drawType = ref('super_lotto638');
const current = new Date(
  new Date().toLocaleString('en-US', { timeZone: 'Asia/Taipei' })
);
const currentYear = current.getFullYear();
const currentMonth = current.getMonth() + 1;
const yearSelector = ref(currentYear);
const selectedYear = ref(currentYear);
const selectedMonth = ref(currentMonth);
const date = ref('');

const qDateProxy = ref<QPopupProxy | null>(null);

const months = [
  { label: '一月', value: 1 },
  { label: '二月', value: 2 },
  { label: '三月', value: 3 },
  { label: '四月', value: 4 },
  { label: '五月', value: 5 },
  { label: '六月', value: 6 },
  { label: '七月', value: 7 },
  { label: '八月', value: 8 },
  { label: '九月', value: 9 },
  { label: '十月', value: 10 },
  { label: '十一月', value: 11 },
  { label: '十二月', value: 12 },
];

const items = ref([] as LottoItem[]);

const formatDate = computed(() => {
  const [year, month] = date.value.split('/');
  return `民國${parseInt(year) - 1911}年${month.toString().padStart(2, '0')}月`;
});

const getLottoList = async () => {
  const { data } = await LOTTO_API.getLottoList({
    draw_type: drawType.value,
    draw_date: date.value,
  });
  items.value = data;
};

const changeYear = (delta: number) => {
  yearSelector.value += delta;
};

const setDate = (year: number, month: number) => {
  yearSelector.value = year;
  selectedYear.value = year;
  selectedMonth.value = month;
  date.value = `${year}/${month.toString().padStart(2, '0')}`;
  qDateProxy.value?.hide();
  getLottoList();
};

if (props.drawTypeQuery) {
  drawType.value = props.drawTypeQuery;
}

if (props.dateQuery) {
  let dateParts = props.dateQuery.split('/'),
    year = parseInt(dateParts[0]),
    month = parseInt(dateParts[1]);

  if (year && month) {
    setDate(year, month);
  } else {
    setDate(currentYear, currentMonth);
  }
} else {
  setDate(currentYear, currentMonth);
}

// 拖牌
const lottoStore = useLottoStore();

const goRd1 = (item: LottoItem) => {
  lottoStore.setDrawType(drawType.value);
  lottoStore.setLotto(item);
  if (props.isSelectRef) {
    emit('selectRef');
  } else {
    router.push('/rd1');
  }
};
</script>

<style lang="scss">
.draw-title {
  .text-period {
    font-size: 1.5rem;
    font-weight: bold;
  }

  .text-draw-date {
    color: #666;
    font-size: 1.3rem;
    font-weight: 500;
  }
}

.ball-card {
  background-color: #fff;

  &::before {
    content: '';
    width: 5px;
    height: 101%;
    position: absolute;
    top: 0;
    left: -1px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    background-color: #da8359;
  }
}

.search-btn {
  font-size: 1.2rem;
  font-weight: 600;
  background-color: #ffbf61;
  color: #605678;
}

.lotto-btn {
  font-size: 1rem;
  font-weight: bold;
  background-color: #ffe6a5;
  color: #605678;
}

@media (max-width: 768px) {
  .draw-title {
    margin-bottom: 1rem;
  }
}
</style>
