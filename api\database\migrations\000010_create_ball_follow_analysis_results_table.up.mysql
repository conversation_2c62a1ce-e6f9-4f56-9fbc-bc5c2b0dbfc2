CREATE TABLE IF NOT EXISTS `ball_follow_analysis_results` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `lotto_type` VARCHAR(20) NOT NULL COMMENT '彩種類型(daily539, lotto649, super_lotto638等)',
    `period` INT NOT NULL COMMENT '期數',
    `analysis_date` DATE NOT NULL COMMENT '分析日期',
    `comb1` TINYINT UNSIGNED NOT NULL COMMENT '拖牌組合第一組數量',
    `comb2` TINYINT UNSIGNED NOT NULL COMMENT '拖牌組合第二組數量',
    `comb3` TINYINT UNSIGNED NOT NULL COMMENT '拖牌組合第三組數量',
    `period_num` SMALLINT UNSIGNED NOT NULL COMMENT '推算期數',
    `max_range` TINYINT UNSIGNED NOT NULL COMMENT '拖牌區間',
    `analysis_periods` TINYINT UNSIGNED NOT NULL DEFAULT 50 COMMENT '分析期數(固定50)',
    `predict_numbers` JSON NOT NULL COMMENT '預測號碼統計結果',
    `non_appeared_numbers` JSON NOT NULL COMMENT '未出現號碼統計',
    `tail_statistics` JSON NOT NULL COMMENT '尾數統計結果',
    `target_appearances` JSON NOT NULL COMMENT '目標號碼出現次數',
    `non_appeared_by_frequency` JSON NULL COMMENT '未出現號碼按頻率排序',
    `tail_num_appearances` JSON NULL COMMENT '尾數號碼出現次數',
    `calculation_hash` VARCHAR(64) NOT NULL COMMENT '計算參數的雜湊值',
    `calculation_duration` INT UNSIGNED NULL COMMENT '計算執行時間(秒)',
    `data_source_periods` JSON NULL COMMENT '資料來源期數列表',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX `idx_lotto_period` (`lotto_type`, `period`),
    UNIQUE INDEX `idx_calculation_hash` (`calculation_hash`),
    INDEX `idx_analysis_date` (`analysis_date`),
    INDEX `idx_parameters` (`comb1`, `comb2`, `comb3`, `period_num`, `max_range`),
    INDEX `idx_lotto_type_analysis_date` (`lotto_type`, `analysis_date`),
    INDEX `idx_period_parameters` (`period`, `comb1`, `comb2`, `comb3`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='版路分析計算結果表';
