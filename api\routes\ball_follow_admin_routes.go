package routes

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"lottery/controllers"
	"lottery/middleware"
	. "lottery/models"
)

// SetupBallFollowAdminRoutes 設定版路分析管理員路由
func SetupBallFollowAdminRoutes(router *gin.Engine, db *gorm.DB) {
	// 建立控制器
	repository := NewBallFollowRepository(db)
	adminController := controllers.NewBallFollowAdminController(repository)
	exportController := controllers.NewBallFollowExportController(repository)

	// 管理員API群組
	adminAPI := router.Group("/api/admin/ball-follow")

	// 應用中介軟體
	adminAPI.Use(middleware.TokenAuth())        // JWT 驗證
	adminAPI.Use(controllers.AdminMiddleware()) // 管理員權限檢查

	// 任務管理路由
	{
		// GET /api/admin/ball-follow/tasks - 取得所有計算任務列表
		adminAPI.GET("/tasks", adminController.GetTasks)

		// GET /api/admin/ball-follow/tasks/:id - 取得特定任務的詳細資訊
		adminAPI.GET("/tasks/:id", adminController.GetTaskDetail)

		// POST /api/admin/ball-follow/tasks/:id/stop - 停止正在執行的計算任務
		adminAPI.POST("/tasks/:id/stop", adminController.StopTask)

		// POST /api/admin/ball-follow/trigger - 手動觸發特定期數的計算
		adminAPI.POST("/trigger", adminController.TriggerCalculation)
	}

	// 結果查詢路由
	{
		// GET /api/admin/ball-follow/results - 取得特定期數的計算結果
		adminAPI.GET("/results", adminController.GetResults)

		// GET /api/admin/ball-follow/summary - 取得計算結果的統計摘要
		adminAPI.GET("/summary", adminController.GetSummary)

		// POST /api/admin/ball-follow/batch-results - 批量查詢多個期數的結果
		adminAPI.POST("/batch-results", adminController.BatchQueryResults)
	}

	// 系統監控路由
	{
		// GET /api/admin/ball-follow/system-status - 取得系統資源使用狀況
		adminAPI.GET("/system-status", adminController.GetSystemStatus)

		// PUT /api/admin/ball-follow/config - 配置並行計算數量等設定
		adminAPI.PUT("/config", adminController.UpdateConfig)

		// GET /api/admin/ball-follow/history - 取得計算歷史記錄
		adminAPI.GET("/history", adminController.GetHistory)
	}

	// 匯出功能路由
	{
		// GET /api/admin/ball-follow/export - 下載特定期數的Excel報表
		adminAPI.GET("/export", exportController.ExportResults)
	}

	// 健康檢查路由（不需要管理員權限）
	healthAPI := router.Group("/api/ball-follow")
	{
		// GET /api/ball-follow/health - 健康檢查
		healthAPI.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status":  "ok",
				"service": "ball-follow-admin",
				"timestamp": gin.H{
					"unix": gin.H{
						"seconds": gin.H{
							"value": "current_timestamp",
						},
					},
				},
			})
		})

		// GET /api/ball-follow/version - 版本資訊
		healthAPI.GET("/version", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"version":     "1.0.0",
				"service":     "ball-follow-admin",
				"description": "版路分析管理員API",
				"features": []string{
					"任務管理",
					"結果查詢",
					"系統監控",
					"Excel匯出",
					"批量查詢",
				},
			})
		})
	}
}

// SetupBallFollowUserRoutes 設定版路分析用戶路由（供前端BatchAnalysisPage使用）
func SetupBallFollowUserRoutes(router *gin.Engine, db *gorm.DB) {
	// 建立控制器
	repository := NewBallFollowRepository(db)

	// 用戶API群組
	userAPI := router.Group("/api/ball-follow")

	// 應用中介軟體
	userAPI.Use(middleware.TokenAuth()) // JWT 驗證

	// 查詢功能路由（一般用戶可用）
	{
		// GET /api/ball-follow/results - 取得計算結果（限制查詢範圍）
		userAPI.GET("/results", func(c *gin.Context) {
			// 檢查用戶權限，一般用戶只能查詢有限的結果
			userInterface, exists := c.Get("user")
			if !exists {
				c.JSON(401, gin.H{"error": "未授權"})
				return
			}

			user, ok := userInterface.(*User)
			if !ok {
				c.JSON(401, gin.H{"error": "用戶資訊錯誤"})
				return
			}

			lottoType := c.Query("lotto_type")
			periodStr := c.Query("period")

			if lottoType == "" || periodStr == "" {
				c.JSON(400, gin.H{
					"error":   "參數錯誤",
					"message": "需要提供 lotto_type 和 period 參數",
				})
				return
			}

			// 一般用戶只能查詢最近30天的結果
			if !user.IsAdmin {
				// 這裡可以添加時間限制邏輯
			}

			// 調用相同的查詢邏輯
			adminController := controllers.NewBallFollowAdminController(repository)
			adminController.GetResults(c)
		})

		// GET /api/ball-follow/summary - 取得統計摘要（一般用戶可用）
		userAPI.GET("/summary", func(c *gin.Context) {
			adminController := controllers.NewBallFollowAdminController(repository)
			adminController.GetSummary(c)
		})

		// GET /api/ball-follow/export - 下載Excel報表（一般用戶可用，但有限制）
		userAPI.GET("/export", func(c *gin.Context) {
			// 檢查用戶權限
			userInterface, exists := c.Get("user")
			if !exists {
				c.JSON(401, gin.H{"error": "未授權"})
				return
			}

			user, ok := userInterface.(*User)
			if !ok {
				c.JSON(401, gin.H{"error": "用戶資訊錯誤"})
				return
			}

			// 一般用戶每天最多下載5次
			if !user.IsAdmin {
				// 這裡可以添加下載次數限制邏輯
			}

			exportController := controllers.NewBallFollowExportController(repository)
			exportController.ExportResults(c)
		})
	}

	// 任務狀態查詢（一般用戶可用，但只能查看狀態）
	{
		// GET /api/ball-follow/task-status - 查詢特定期數的計算狀態
		userAPI.GET("/task-status", func(c *gin.Context) {
			lottoType := c.Query("lotto_type")
			periodStr := c.Query("period")

			if lottoType == "" || periodStr == "" {
				c.JSON(400, gin.H{
					"error":   "參數錯誤",
					"message": "需要提供 lotto_type 和 period 參數",
				})
				return
			}

			period, err := strconv.Atoi(periodStr)
			if err != nil {
				c.JSON(400, gin.H{
					"error":   "參數錯誤",
					"message": "無效的期數",
				})
				return
			}

			// 查詢任務狀態
			task, err := repository.GetCalculationTaskByPeriod(LottoTypeStr(lottoType), period)
			if err != nil {
				c.JSON(404, gin.H{
					"error":   "任務不存在",
					"message": "找不到指定期數的計算任務",
				})
				return
			}

			// 只返回基本狀態資訊
			c.JSON(200, gin.H{
				"success": true,
				"data": gin.H{
					"lotto_type":             task.LottoType,
					"period":                 task.Period,
					"task_status":            task.TaskStatus,
					"progress_percentage":    fmt.Sprintf("%.2f", task.GetProgressPercentage()),
					"completed_combinations": task.CompletedCombinations,
					"total_combinations":     task.TotalCombinations,
					"start_time":             task.StartTime,
					"end_time":               task.EndTime,
				},
			})
		})
	}
}
