#!/bin/bash

# Ball Follow Scheduler 啟動腳本

set -e

# 設定變數
APP_NAME="ball_follow_scheduler"
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${APP_DIR}/config/ball_follow_scheduler.json"
LOG_DIR="${APP_DIR}/logs"
PID_FILE="${APP_DIR}/${APP_NAME}.pid"

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 檢查是否已經在運行
check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 正在運行
        else
            rm -f "$PID_FILE"
            return 1  # 不在運行
        fi
    fi
    return 1  # 不在運行
}

# 啟動服務
start_service() {
    log_info "Starting Ball Follow Scheduler..."
    
    # 檢查是否已經在運行
    if check_running; then
        log_warn "Ball Follow Scheduler is already running (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    # 建立日誌目錄
    mkdir -p "$LOG_DIR"
    
    # 檢查配置檔案
    if [ ! -f "$CONFIG_FILE" ]; then
        log_warn "Configuration file not found: $CONFIG_FILE"
        log_info "Creating default configuration..."
        
        # 建立配置目錄
        mkdir -p "$(dirname "$CONFIG_FILE")"
        
        # 這裡可以建立預設配置或提示用戶
        log_error "Please create configuration file: $CONFIG_FILE"
        return 1
    fi
    
    # 編譯程序（如果需要）
    if [ ! -f "$APP_DIR/$APP_NAME" ] || [ "$APP_DIR/main.go" -nt "$APP_DIR/$APP_NAME" ]; then
        log_info "Building application..."
        cd "$APP_DIR"
        go build -o "$APP_NAME" main.go
        if [ $? -ne 0 ]; then
            log_error "Failed to build application"
            return 1
        fi
    fi
    
    # 啟動服務
    cd "$APP_DIR"
    nohup ./"$APP_NAME" -config "$CONFIG_FILE" > "$LOG_DIR/startup.log" 2>&1 &
    local pid=$!
    
    # 儲存PID
    echo "$pid" > "$PID_FILE"
    
    # 等待一下確認啟動成功
    sleep 2
    if ps -p "$pid" > /dev/null 2>&1; then
        log_info "Ball Follow Scheduler started successfully (PID: $pid)"
        return 0
    else
        log_error "Failed to start Ball Follow Scheduler"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服務
stop_service() {
    log_info "Stopping Ball Follow Scheduler..."
    
    if ! check_running; then
        log_warn "Ball Follow Scheduler is not running"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    
    # 發送TERM信號
    kill -TERM "$pid" 2>/dev/null
    
    # 等待程序結束
    local count=0
    while ps -p "$pid" > /dev/null 2>&1; do
        sleep 1
        count=$((count + 1))
        
        if [ $count -ge 30 ]; then
            log_warn "Process did not stop gracefully, forcing kill..."
            kill -KILL "$pid" 2>/dev/null
            break
        fi
    done
    
    # 清理PID檔案
    rm -f "$PID_FILE"
    
    log_info "Ball Follow Scheduler stopped"
    return 0
}

# 重啟服務
restart_service() {
    log_info "Restarting Ball Follow Scheduler..."
    stop_service
    sleep 2
    start_service
}

# 檢查狀態
check_status() {
    if check_running; then
        local pid=$(cat "$PID_FILE")
        log_info "Ball Follow Scheduler is running (PID: $pid)"
        
        # 顯示記憶體使用情況
        local memory=$(ps -o rss= -p "$pid" 2>/dev/null | awk '{print int($1/1024)}')
        if [ -n "$memory" ]; then
            log_info "Memory usage: ${memory}MB"
        fi
        
        return 0
    else
        log_info "Ball Follow Scheduler is not running"
        return 1
    fi
}

# 查看日誌
view_logs() {
    local log_file="$LOG_DIR/ball_follow_scheduler.log"
    
    if [ ! -f "$log_file" ]; then
        log_warn "Log file not found: $log_file"
        return 1
    fi
    
    if command -v tail >/dev/null 2>&1; then
        log_info "Showing last 50 lines of log file..."
        tail -n 50 "$log_file"
    else
        log_info "Showing log file content..."
        cat "$log_file"
    fi
}

# 測試模式
test_mode() {
    log_info "Running Ball Follow Scheduler in test mode..."
    
    cd "$APP_DIR"
    
    # 編譯程序（如果需要）
    if [ ! -f "$APP_DIR/$APP_NAME" ] || [ "$APP_DIR/main.go" -nt "$APP_DIR/$APP_NAME" ]; then
        log_info "Building application..."
        go build -o "$APP_NAME" main.go
        if [ $? -ne 0 ]; then
            log_error "Failed to build application"
            return 1
        fi
    fi
    
    # 運行測試
    ./"$APP_NAME" -test -config "$CONFIG_FILE"
}

# 顯示幫助
show_help() {
    echo "Ball Follow Scheduler 管理腳本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|test|help}"
    echo ""
    echo "命令:"
    echo "  start   - 啟動服務"
    echo "  stop    - 停止服務"
    echo "  restart - 重啟服務"
    echo "  status  - 檢查服務狀態"
    echo "  logs    - 查看日誌"
    echo "  test    - 運行測試模式"
    echo "  help    - 顯示此幫助訊息"
    echo ""
    echo "檔案位置:"
    echo "  配置檔案: $CONFIG_FILE"
    echo "  日誌目錄: $LOG_DIR"
    echo "  PID檔案: $PID_FILE"
    echo ""
}

# 主程序
main() {
    case "${1:-}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            check_status
            ;;
        logs)
            view_logs
            ;;
        test)
            test_mode
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: ${1:-}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 執行主程序
main "$@"
