# language: zh-TW
功能: 版路分析資料庫設計
  作為系統開發者
  我需要設計適當的資料庫結構
  來存儲版路分析的計算結果和任務狀態
  以支援高效的查詢和資料重複使用

  背景:
    假設 資料庫連接正常
    而且 已執行相關的migration文件

  場景: 版路分析結果表結構驗證
    當 查詢版路分析結果表結構
    那麼 表格應該包含以下欄位:
      | 欄位名稱              | 資料類型    | 說明                    |
      | id                   | BIGINT      | 主鍵，自動遞增           |
      | lotto_type           | VARCHAR(20) | 彩種類型(daily539等)     |
      | period               | INT         | 期數                    |
      | analysis_date        | DATE        | 分析日期                |
      | comb1                | TINYINT     | 拖牌組合第一組數量       |
      | comb2                | TINYINT     | 拖牌組合第二組數量       |
      | comb3                | TINYINT     | 拖牌組合第三組數量       |
      | period_num           | SMALLINT    | 推算期數                |
      | max_range            | TINYINT     | 拖牌區間                |
      | analysis_periods     | TINYINT     | 分析期數(固定50)         |
      | predict_numbers      | JSON        | 預測號碼統計結果         |
      | non_appeared_numbers | JSON        | 未出現號碼統計           |
      | tail_statistics      | JSON        | 尾數統計結果            |
      | target_appearances   | JSON        | 目標號碼出現次數         |
      | calculation_hash     | VARCHAR(64) | 計算參數的雜湊值         |
      | created_at           | TIMESTAMP   | 建立時間                |
      | updated_at           | TIMESTAMP   | 更新時間                |

  場景: 計算任務狀態表結構驗證
    當 查詢計算任務狀態表結構
    那麼 表格應該包含以下欄位:
      | 欄位名稱           | 資料類型     | 說明                           |
      | id                | BIGINT       | 主鍵，自動遞增                  |
      | lotto_type        | VARCHAR(20)  | 彩種類型                       |
      | period            | INT          | 期數                          |
      | task_status       | VARCHAR(20)  | 任務狀態(pending/running/completed/failed) |
      | total_combinations| SMALLINT     | 總參數組合數                    |
      | completed_combinations | SMALLINT | 已完成組合數                   |
      | failed_combinations   | SMALLINT  | 失敗組合數                     |
      | start_time        | TIMESTAMP    | 開始時間                       |
      | end_time          | TIMESTAMP    | 結束時間                       |
      | estimated_duration| INT          | 預估執行時間(秒)                |
      | error_message     | TEXT         | 錯誤訊息                       |
      | retry_count       | TINYINT      | 重試次數                       |
      | created_at        | TIMESTAMP    | 建立時間                       |
      | updated_at        | TIMESTAMP    | 更新時間                       |

  場景: 參數組合子任務表結構驗證
    當 查詢參數組合子任務表結構
    那麼 表格應該包含以下欄位:
      | 欄位名稱        | 資料類型     | 說明                                    |
      | id             | BIGINT       | 主鍵，自動遞增                          |
      | task_id        | BIGINT       | 關聯主任務ID                           |
      | comb1          | TINYINT      | 拖牌組合第一組數量                      |
      | comb2          | TINYINT      | 拖牌組合第二組數量                      |
      | comb3          | TINYINT      | 拖牌組合第三組數量                      |
      | period_num     | SMALLINT     | 推算期數                               |
      | max_range      | TINYINT      | 拖牌區間                               |
      | subtask_status | VARCHAR(20)  | 子任務狀態(pending/running/completed/failed) |
      | start_time     | TIMESTAMP    | 開始時間                               |
      | end_time       | TIMESTAMP    | 結束時間                               |
      | duration       | INT          | 執行時間(秒)                           |
      | error_message  | TEXT         | 錯誤訊息                               |
      | retry_count    | TINYINT      | 重試次數                               |
      | result_id      | BIGINT       | 關聯結果表ID                           |
      | created_at     | TIMESTAMP    | 建立時間                               |
      | updated_at     | TIMESTAMP    | 更新時間                               |

  場景: 建立版路分析結果記錄
    假設 今彩539期數 "113001" 的版路分析計算完成
    而且 使用參數組合(1,1,1)、推算期數120、拖牌區間15
    當 儲存計算結果到資料庫
    那麼 版路分析結果表應該新增一筆記錄
    而且 記錄的lotto_type應該為 "daily539"
    而且 記錄的period應該為 113001
    而且 記錄的comb1應該為 1, comb2應該為 1, comb3應該為 1
    而且 記錄的period_num應該為 120
    而且 記錄的max_range應該為 15
    而且 記錄的analysis_periods應該為 50
    而且 predict_numbers欄位應該包含JSON格式的預測號碼統計
    而且 calculation_hash應該為該參數組合的唯一雜湊值

  場景: 查詢特定參數組合的計算結果
    假設 資料庫中已有多筆版路分析結果
    當 查詢今彩539期數113001、參數組合(2,1,3)、推算期數180、拖牌區間20的結果
    那麼 系統應該返回對應的計算結果
    而且 結果應該包含完整的預測號碼統計
    而且 結果應該包含未出現號碼統計
    而且 結果應該包含尾數統計

  場景: 檢查計算結果是否已存在
    假設 準備計算今彩539期數113001的版路分析
    而且 參數組合為(1,2,1)、推算期數90、拖牌區間25
    當 檢查該參數組合是否已計算
    那麼 系統應該根據calculation_hash查詢資料庫
    而且 如果已存在相同雜湊值的記錄，應該返回true
    而且 如果不存在，應該返回false

  場景: 更新計算任務進度
    假設 今彩539期數113001的計算任務正在進行
    而且 總共有1350個參數組合
    當 完成第100個參數組合的計算
    那麼 計算任務狀態表的completed_combinations應該更新為100
    而且 任務狀態應該保持為 "running"
    而且 updated_at應該更新為當前時間

  場景: 標記計算任務完成
    假設 今彩539期數113001的計算任務正在進行
    而且 所有1350個參數組合都已完成
    當 更新任務狀態
    那麼 task_status應該更新為 "completed"
    而且 completed_combinations應該為1350
    而且 end_time應該設定為當前時間
    而且 estimated_duration應該計算實際執行時間

  場景: 記錄計算錯誤資訊
    假設 今彩539期數113001的某個參數組合計算失敗
    而且 已重試3次仍然失敗
    當 記錄錯誤資訊
    那麼 參數組合子任務表的subtask_status應該更新為 "failed"
    而且 error_message應該記錄詳細錯誤訊息
    而且 retry_count應該為3
    而且 主任務的failed_combinations應該增加1

  場景: 資料庫索引優化驗證
    當 檢查版路分析結果表的索引
    那麼 應該在以下欄位建立索引:
      | 索引名稱                    | 欄位組合                                    | 索引類型 |
      | idx_lotto_period           | lotto_type, period                         | 複合索引 |
      | idx_calculation_hash       | calculation_hash                           | 唯一索引 |
      | idx_analysis_date          | analysis_date                              | 單一索引 |
      | idx_parameters             | comb1, comb2, comb3, period_num, max_range | 複合索引 |

  場景: 資料清理和歸檔策略
    假設 版路分析結果表中有超過6個月的舊資料
    當 執行資料清理作業
    那麼 系統應該保留最近6個月的資料
    而且 應該將6個月前的資料歸檔到歷史表
    而且 應該保持資料的完整性和可追溯性

  場景: 資料重複使用的雜湊計算
    假設 需要計算參數組合(1,1,1)、推算期數120、拖牌區間15的雜湊值
    當 生成calculation_hash
    那麼 雜湊值應該基於lotto_type、comb1、comb2、comb3、period_num、max_range
    而且 相同參數組合應該產生相同的雜湊值
    而且 不同參數組合應該產生不同的雜湊值
    而且 雜湊值應該為64字元的十六進位字串
