# Occurrence 統計邏輯分析與修復

## 🔍 問題描述

用戶報告：優化後的 `occurrence` 中的 `period` 都沒有 `firstNumbers` 或 `secondNumbers` 的號碼，卻被記錄起來，可能導致部分組合的 key 會錯亂。

## 📊 Occurrence 統計邏輯分析

### 正確的設計理念

`Occurrence` 的設計邏輯是：
1. **Key**: 使用 hash 來唯一標識特定的號碼組合模式
2. **Value**: 記錄這個組合模式在歷史數據中的出現情況

```typescript
// Hash Key 包含：
// - firstGroup: 具體的第一組號碼 (例如: [1, 5, 12])
// - secondGroup: 具體的第二組號碼 (例如: [8, 15, 23])  
// - gap: 第一組和第二組之間的期數間隔
// - targetGap: 第二組和目標組之間的期數間隔

const hashKey = CombinationHasher.hashCombination(
  firstGroup,    // [1, 5, 12]
  secondGroup,   // [8, 15, 23]
  gap,           // 3
  targetGap      // 2
);

// Occurrence 記錄：
{
  count: 5,      // 這個組合模式出現了5次
  periods: [     // 每次出現的期號記錄
    { firstPeriod: "113001", secondPeriod: "113004", targetPeriod: "113006" },
    { firstPeriod: "113010", secondPeriod: "113013", targetPeriod: "113015" },
    // ...
  ],
  isPredict: true
}
```

### 為什麼 periods 中不包含號碼？

這是**正確的設計**，原因：

1. **避免數據重複**: 號碼組合已經通過 hash key 唯一標識
2. **節省記憶體**: 不需要在每個 period 中重複儲存相同的號碼
3. **邏輯清晰**: Key 負責標識組合，Value 負責記錄出現時間

## 🔧 發現的實際問題

### 問題 1: 類型不一致

在 `processBatch` 函數中：

```typescript
// ❌ 錯誤：類型不匹配
const occurrence: Occurrence = occurrenceResults.get(hashKey) || {
  count: 0,
  periods: [],
  isPredict: false,
};

// ✅ 修復：使用正確的類型
const occurrence: OptimizedOccurrence = occurrenceResults.get(hashKey) || {
  count: 0,
  periods: [],
  periodsCompressed: new Uint16Array(0),
  isPredict: false,
};
```

### 問題 2: 調試信息不足

原有的調試信息無法幫助診斷 occurrence 數據的正確性。

## ✅ 修復內容

### 修復 1: 類型一致性

```typescript
// 確保所有地方都使用 OptimizedOccurrence 類型
const occurrence: OptimizedOccurrence = occurrenceResults.get(hashKey) || {
  count: 0,
  periods: [],
  periodsCompressed: new Uint16Array(0),
  isPredict: false,
};
```

### 修復 2: 增強調試信息

```typescript
// 添加詳細的調試信息
console.log('Debug consecutive hits and occurrence:', {
  firstNumbers: TypedArrayUtils.fromTypedArray(optimizedStat.firstNumbers),
  secondNumbers: TypedArrayUtils.fromTypedArray(optimizedStat.secondNumbers),
  targetNumbers: TypedArrayUtils.fromTypedArray(optimizedStat.targetNumbers),
  gap: optimizedStat.gap,
  targetGap: optimizedStat.targetGap,
  targetMatches: optimizedStat.targetMatches,
  consecutiveHits: optimizedStat.consecutiveHits,
  hitSetSize: hitSet?.size || 0,
  occurrenceCount: occurrence.count,
  periodsLength: occurrence.periods.length,
  validTargetPeriods: validTargetPeriods.slice(0, 5),
  hashKey: hashKey,
  fullHashKey: fullHashKey
});
```

## 🧪 驗證方法

### 1. Hash Key 唯一性驗證

```typescript
// 測試不同組合是否產生不同的 hash
const hash1 = CombinationHasher.hashCombination([1, 5, 12], [8, 15, 23], 3, 2);
const hash2 = CombinationHasher.hashCombination([1, 5, 12], [8, 15, 24], 3, 2);
const hash3 = CombinationHasher.hashCombination([1, 5, 13], [8, 15, 23], 3, 2);

console.log('Hash uniqueness test:', {
  hash1, hash2, hash3,
  different: hash1 !== hash2 && hash2 !== hash3 && hash1 !== hash3
});
```

### 2. Occurrence 數據完整性驗證

```typescript
// 檢查 occurrence 數據是否合理
for (const [key, occurrence] of occurrenceResults.entries()) {
  if (occurrence.count !== occurrence.periods.filter(p => p.targetPeriod).length) {
    console.warn('Occurrence count mismatch:', {
      key,
      count: occurrence.count,
      actualPeriods: occurrence.periods.filter(p => p.targetPeriod).length
    });
  }
}
```

### 3. 期號邏輯驗證

```typescript
// 檢查期號的邏輯關係
for (const period of occurrence.periods) {
  if (period.targetPeriod) {
    const first = parseInt(period.firstPeriod);
    const second = parseInt(period.secondPeriod);
    const target = parseInt(period.targetPeriod);
    
    if (second - first !== gap || target - second !== targetGap) {
      console.warn('Period gap mismatch:', {
        period,
        expectedGap: gap,
        actualGap: second - first,
        expectedTargetGap: targetGap,
        actualTargetGap: target - second
      });
    }
  }
}
```

## 📊 預期結果

### 正常情況下的數據結構

```typescript
// 例如：組合 [1,5,12] -> [8,15,23] (gap=3, targetGap=2)
{
  hashKey: 1234567890, // 唯一標識這個組合
  occurrence: {
    count: 3,
    periods: [
      { firstPeriod: "113001", secondPeriod: "113004", targetPeriod: "113006" },
      { firstPeriod: "113010", secondPeriod: "113013", targetPeriod: "113015" },
      { firstPeriod: "113020", secondPeriod: "113023", targetPeriod: "113025" }
    ],
    periodsCompressed: Uint16Array([113006, 9, 10]), // 壓縮的期號
    isPredict: true
  }
}
```

### 異常情況的識別

1. **Count 不匹配**: `occurrence.count` 與實際有 `targetPeriod` 的 periods 數量不符
2. **期號間隔錯誤**: 期號之間的間隔與 `gap`、`targetGap` 不符
3. **Hash 衝突**: 不同組合產生相同的 hash（極低概率）

## 🔄 後續行動

### 短期
1. 執行測試，檢查調試信息
2. 驗證 occurrence 數據的正確性
3. 確認連續拖出次數計算正確

### 長期
1. 添加更完善的數據驗證機制
2. 考慮添加 hash 衝突檢測
3. 優化調試信息的輸出格式

## 🚨 發現的關鍵問題

### 問題 3: Occurrence 統計邏輯錯誤

**根本問題**: 通過對比原始版本 `old_worker.md`，發現我們的 `calculateOccurrences` 邏輯有嚴重錯誤。

**原始版本分析**:
- `calculateOccurrences()` 函數是空的
- 在 `processBatch` 中有 `if (!occurrence.isPredict) continue;`
- 但沒有看到設置 `isPredict = true` 的邏輯

**我們的錯誤實現**:
```typescript
// ❌ 錯誤：混合了歷史統計和預測標記
for (let k = j + 1; k - j <= config.maxRange; k++) {
  if (k > predictIndex) break;

  if (k < results.length) {
    // 歷史數據統計
    occurrence.count++;
    occurrence.periods.push({...});
  } else if (k === predictIndex) {
    // 預測標記
    occurrence.isPredict = true;
  }
}
```

**問題**:
1. 邏輯混亂：在同一個迴圈中處理歷史統計和預測標記
2. 條件錯誤：`k === predictIndex` 的條件很少滿足
3. 統計不完整：沒有正確統計所有歷史組合

## ✅ 正確的修復方案

### 修復 3: 重新設計 calculateOccurrences 邏輯

```typescript
// ✅ 正確：分兩階段處理
function calculateOccurrences() {
  // 第一階段：統計所有歷史組合的出現次數
  for (let i = 0; i < results.length - config.lookAheadCount; i++) {
    for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
      const gap = j - i;

      for (let k = j + 1; k < results.length && k - j <= config.maxRange; k++) {
        const targetGap = k - j;

        // 統計歷史組合
        occurrence.count++;
        occurrence.periods.push({
          firstPeriod: results[i].period,
          secondPeriod: results[j].period,
          targetPeriod: results[k].period,
        });
      }
    }
  }

  // 第二階段：標記可以用於預測的組合
  for (let i = 0; i < results.length - config.lookAheadCount; i++) {
    for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
      const gap = j - i;
      const k = predictIndex; // 預測期
      const targetGap = k - j;

      if (targetGap > 0 && targetGap <= config.maxRange) {
        const occurrence = occurrenceResults.get(hashKey);
        if (occurrence && occurrence.count > 0) {
          // 這個組合在歷史中出現過，可以用於預測
          occurrence.isPredict = true;
          occurrence.periods.push({
            firstPeriod: results[i].period,
            secondPeriod: results[j].period,
            // targetPeriod 留空，因為這是預測期
          });
        }
      }
    }
  }
}
```

### 修復邏輯說明

1. **第一階段**: 純粹統計歷史數據
   - 遍歷所有歷史期號組合
   - 統計每個組合模式的出現次數
   - 記錄具體的期號

2. **第二階段**: 標記預測組合
   - 檢查哪些歷史組合可以用於當前預測
   - 只有在歷史中出現過的組合才能用於預測
   - 設置 `isPredict = true`

### 修復效果

| 項目 | 修復前 | 修復後 |
|------|--------|--------|
| 歷史統計 | ❌ 不完整 | ✅ 完整統計所有歷史組合 |
| 預測標記 | ❌ 邏輯錯誤 | ✅ 正確標記可預測組合 |
| 數據一致性 | ❌ 混亂 | ✅ 邏輯清晰分離 |

---

**🎯 結論**: 發現並修復了 Occurrence 統計的根本邏輯錯誤。新的兩階段處理方式確保了數據的正確性和邏輯的清晰性。請執行測試驗證修復效果。
