export const lottoTypeOptions = [
  {
    label: '威力彩',
    value: 'super_lotto638',
  },
  {
    label: '六合彩',
    value: 'lotto_hk',
  },
  {
    label: '大樂透',
    value: 'lotto649',
  },
  {
    label: '今彩539',
    value: 'daily539',
  },
  {
    label: '加州天天樂',
    value: 'ca_lotto',
  }
];

export const getDrawMaxNumber = (drawType: string) => {
  switch (drawType) {
    case 'super_lotto638':
      return 38;
    case 'lotto649':
      return 49;
    case 'daily539':
      return 39;
    case 'lotto_hk':
      return 49;
    case 'ca_lotto':
      return 39;
    default:
      return 49;
  }
};

export const getDrawLabel = (drawType: string) => {
  switch (drawType) {
    case 'super_lotto638':
      return '威力彩';
    case 'lotto649':
      return '大樂透';
    case 'daily539':
      return '今彩539';
    case 'lotto_hk':
      return '六合彩';
    case 'ca_lotto':
      return '加州天天樂';
    default:
      return '';
  }
};
