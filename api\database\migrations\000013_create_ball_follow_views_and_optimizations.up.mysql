-- 建立任務進度統計視圖
CREATE VIEW `ball_follow_task_progress_view` AS
SELECT 
    t.id,
    t.lotto_type,
    t.period,
    t.task_status,
    t.total_combinations,
    t.completed_combinations,
    t.failed_combinations,
    (t.total_combinations - t.completed_combinations - t.failed_combinations) AS remaining_combinations,
    ROUND((t.completed_combinations / t.total_combinations) * 100, 2) AS progress_percentage,
    t.start_time,
    t.end_time,
    t.estimated_duration,
    t.actual_duration,
    CASE 
        WHEN t.task_status = 'running' AND t.start_time IS NOT NULL THEN
            TIMESTAMPDIFF(SECOND, t.start_time, NOW())
        ELSE t.actual_duration
    END AS current_duration,
    t.priority,
    t.triggered_by,
    t.created_at,
    t.updated_at
FROM ball_follow_calculation_tasks t;

-- 建立計算結果統計視圖
CREATE VIEW `ball_follow_results_summary_view` AS
SELECT 
    r.lotto_type,
    r.period,
    r.analysis_date,
    COUNT(*) AS total_results,
    COUNT(CASE WHEN r.calculation_duration IS NOT NULL THEN 1 END) AS completed_calculations,
    AVG(r.calculation_duration) AS avg_calculation_duration,
    MIN(r.calculation_duration) AS min_calculation_duration,
    MAX(r.calculation_duration) AS max_calculation_duration,
    MIN(r.created_at) AS first_calculation_time,
    MAX(r.created_at) AS last_calculation_time
FROM ball_follow_analysis_results r
GROUP BY r.lotto_type, r.period, r.analysis_date;

-- 建立子任務狀態統計視圖
CREATE VIEW `ball_follow_subtask_status_view` AS
SELECT 
    s.task_id,
    t.lotto_type,
    t.period,
    s.subtask_status,
    COUNT(*) AS subtask_count,
    AVG(s.duration) AS avg_duration,
    SUM(CASE WHEN s.memory_usage_mb IS NOT NULL THEN s.memory_usage_mb ELSE 0 END) AS total_memory_usage_mb,
    MAX(s.retry_count) AS max_retry_count
FROM ball_follow_subtasks s
JOIN ball_follow_calculation_tasks t ON s.task_id = t.id
GROUP BY s.task_id, t.lotto_type, t.period, s.subtask_status;

-- 建立效能監控視圖
CREATE VIEW `ball_follow_performance_metrics_view` AS
SELECT 
    DATE(t.created_at) AS calculation_date,
    t.lotto_type,
    COUNT(DISTINCT t.id) AS total_tasks,
    COUNT(DISTINCT CASE WHEN t.task_status = 'completed' THEN t.id END) AS completed_tasks,
    COUNT(DISTINCT CASE WHEN t.task_status = 'failed' THEN t.id END) AS failed_tasks,
    AVG(t.actual_duration) AS avg_task_duration,
    SUM(s.total_subtasks) AS total_subtasks,
    SUM(s.completed_subtasks) AS completed_subtasks,
    SUM(s.failed_subtasks) AS failed_subtasks,
    AVG(s.avg_subtask_duration) AS avg_subtask_duration
FROM ball_follow_calculation_tasks t
LEFT JOIN (
    SELECT 
        task_id,
        COUNT(*) AS total_subtasks,
        COUNT(CASE WHEN subtask_status = 'completed' THEN 1 END) AS completed_subtasks,
        COUNT(CASE WHEN subtask_status = 'failed' THEN 1 END) AS failed_subtasks,
        AVG(duration) AS avg_subtask_duration
    FROM ball_follow_subtasks
    GROUP BY task_id
) s ON t.id = s.task_id
GROUP BY DATE(t.created_at), t.lotto_type;

-- 建立觸發器：自動更新任務進度
DELIMITER $$

CREATE TRIGGER `update_task_progress_on_subtask_change`
AFTER UPDATE ON `ball_follow_subtasks`
FOR EACH ROW
BEGIN
    DECLARE completed_count INT DEFAULT 0;
    DECLARE failed_count INT DEFAULT 0;
    DECLARE total_count INT DEFAULT 0;
    DECLARE current_status VARCHAR(20);
    
    -- 計算該任務的子任務統計
    SELECT 
        COUNT(CASE WHEN subtask_status = 'completed' THEN 1 END),
        COUNT(CASE WHEN subtask_status = 'failed' THEN 1 END),
        COUNT(*)
    INTO completed_count, failed_count, total_count
    FROM ball_follow_subtasks 
    WHERE task_id = NEW.task_id;
    
    -- 取得當前任務狀態
    SELECT task_status INTO current_status
    FROM ball_follow_calculation_tasks
    WHERE id = NEW.task_id;
    
    -- 更新主任務的進度
    UPDATE ball_follow_calculation_tasks 
    SET 
        completed_combinations = completed_count,
        failed_combinations = failed_count,
        updated_at = NOW(),
        -- 如果所有子任務都完成，更新任務狀態為完成
        task_status = CASE 
            WHEN (completed_count + failed_count) >= total_count AND current_status = 'running' THEN 'completed'
            WHEN current_status = 'pending' AND NEW.subtask_status = 'running' THEN 'running'
            ELSE current_status
        END,
        -- 如果任務完成，設定結束時間和實際執行時間
        end_time = CASE 
            WHEN (completed_count + failed_count) >= total_count AND current_status = 'running' THEN NOW()
            ELSE end_time
        END,
        actual_duration = CASE 
            WHEN (completed_count + failed_count) >= total_count AND current_status = 'running' AND start_time IS NOT NULL THEN 
                TIMESTAMPDIFF(SECOND, start_time, NOW())
            ELSE actual_duration
        END
    WHERE id = NEW.task_id;
END$$

DELIMITER ;

-- 建立觸發器：自動設定子任務的計算雜湊值
DELIMITER $$

CREATE TRIGGER `set_subtask_calculation_hash`
BEFORE INSERT ON `ball_follow_subtasks`
FOR EACH ROW
BEGIN
    DECLARE lotto_type_val VARCHAR(20);
    DECLARE period_val INT;
    
    -- 取得主任務的彩種和期數
    SELECT t.lotto_type, t.period 
    INTO lotto_type_val, period_val
    FROM ball_follow_calculation_tasks t
    WHERE t.id = NEW.task_id;
    
    -- 生成計算雜湊值
    IF NEW.calculation_hash IS NULL THEN
        SET NEW.calculation_hash = SHA2(
            CONCAT(lotto_type_val, '-', period_val, '-', NEW.comb1, '-', NEW.comb2, '-', NEW.comb3, '-', NEW.period_num, '-', NEW.max_range),
            256
        );
    END IF;
END$$

DELIMITER ;

-- 建立分區表（按年份分區，提升查詢效能）
-- 注意：這需要在有資料之前執行，或者需要重建表格

-- 為結果表建立額外的複合索引以優化常見查詢
CREATE INDEX `idx_lotto_period_comb` ON `ball_follow_analysis_results` (`lotto_type`, `period`, `comb1`, `comb2`, `comb3`);
CREATE INDEX `idx_analysis_date_lotto` ON `ball_follow_analysis_results` (`analysis_date`, `lotto_type`);
CREATE INDEX `idx_calculation_duration` ON `ball_follow_analysis_results` (`calculation_duration`);

-- 為任務表建立額外索引
CREATE INDEX `idx_status_priority_created` ON `ball_follow_calculation_tasks` (`task_status`, `priority`, `created_at`);
CREATE INDEX `idx_lotto_period_status` ON `ball_follow_calculation_tasks` (`lotto_type`, `period`, `task_status`);

-- 為子任務表建立額外索引
CREATE INDEX `idx_task_status_retry` ON `ball_follow_subtasks` (`task_id`, `subtask_status`, `retry_count`);
CREATE INDEX `idx_status_start_time` ON `ball_follow_subtasks` (`subtask_status`, `start_time`);
CREATE INDEX `idx_worker_status` ON `ball_follow_subtasks` (`worker_id`, `subtask_status`);
