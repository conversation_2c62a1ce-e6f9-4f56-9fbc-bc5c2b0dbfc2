﻿<template>
  <q-page class="justify-center">
    <q-card>
      <q-card-section>
        <div class="text-h4 text-center q-mb-lg">版路圖表</div>

        <!-- 分析方法選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6">分析方法</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedMethod"
                :options="analysisMethodOptions"
                map-options
                emit-value
                class="text-h6"
              />
            </div>
          </div>
        </div>

        <!-- 彩種選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">彩種選擇</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedLottoType"
                :options="lottoTypeOptions"
                emit-value
                map-options
                @update:model-value="onLottoTypeChange"
              />
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">參考日期</div>
            <div class="q-pa-sm">
              <q-input
                outlined
                dense
                v-model="referenceDate"
                type="date"
                mask="YYYY/MM/DD"
                :max="maxDate"
                class="text-h6"
              />
            </div>
          </div>
        </div>

        <!-- 動態參數設定區域 -->
        <div class="row q-mb-lg" v-if="selectedMethod">
          <div class="col-12">
            <div class="text-h6 text-weight-bold q-mb-md">參數設定</div>

            <!-- 版路分析參數 -->
            <template v-if="selectedMethod === 'ball-follow'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb1"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb2"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb3"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">拖牌區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">分析期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.batchAnalysisRange"
                      :options="batchAnalysisRangeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>

              <!-- 篩選條件 -->
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-3">
                  <div class="text-h6">連續拖出次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballConsecutiveHits"
                      :options="accuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">連續拖出篩選</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballConsecutiveFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">準確率</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballTargetProbability"
                      :options="targetProbabilityOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">準確率篩選</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballProbabilityFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                      :disable="filterParams.ballTargetProbability === 'all'"
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">統計方式</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballStatisticType"
                      :options="statisticTypeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 尾數分析參數 -->
            <template v-if="selectedMethod === 'tail'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">拖牌區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">分析期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.batchAnalysisRange"
                      :options="batchAnalysisRangeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>

              <!-- 篩選條件 -->
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-3">
                  <div class="text-h6">連續拖出次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailConsecutiveHits"
                      :options="tailAccuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">連續拖出篩選</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailConsecutiveFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">準確率</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailTargetProbability"
                      :options="targetProbabilityOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">準確率篩選</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailProbabilityFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                      :disable="filterParams.tailTargetProbability === 'all'"
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">統計方式</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailStatisticType"
                      :options="statisticTypeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 綜合分析參數 -->
            <template v-if="selectedMethod === 'pattern'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">版路拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb1"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb2"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb3"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 text-h6">尾數拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">拖牌區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>

                <div class="col-12 col-sm-4">
                  <div class="text-h6">分析期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.batchAnalysisRange"
                      :options="batchAnalysisRangeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>

              <!-- 版路分析篩選條件 -->
              <div class="row q-mb-md">
                <div class="col-12 text-h6">版路分析篩選條件</div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">連續拖出次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballConsecutiveHits"
                      :options="accuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">連續拖出篩選</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballConsecutiveFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">準確率</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballTargetProbability"
                      :options="targetProbabilityOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">準確率篩選</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballProbabilityFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                      :disable="filterParams.ballTargetProbability === 'all'"
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">統計方式</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballStatisticType"
                      :options="statisticTypeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>

              <!-- 尾數分析篩選條件 -->
              <div class="row q-mb-md">
                <div class="col-12 text-h6">尾數分析篩選條件</div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">連續拖出次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailConsecutiveHits"
                      :options="tailAccuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">連續拖出篩選</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailConsecutiveFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">準確率</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailTargetProbability"
                      :options="targetProbabilityOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-3">
                  <div class="text-h6">準確率篩選</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailProbabilityFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                      :disable="filterParams.tailTargetProbability === 'all'"
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">統計方式</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailStatisticType"
                      :options="statisticTypeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <q-card-actions align="right" class="q-my-lg q-py-none q-px-md">
          <q-btn
            type="button"
            label="中斷計算"
            color="negative"
            @click="stopBatchAnalysis"
            class="text-h6 q-mr-md"
            v-if="isCalculating"
          />
          <q-btn
            type="button"
            label="產生圖表"
            color="positive"
            class="text-h6 q-px-lg q-py-sm"
            @click="startBatchAnalysis"
            :loading="isCalculating"
            :disable="!canStartAnalysis"
          >
            <template v-slot:loading>
              <q-spinner-dots />
            </template>
          </q-btn>
        </q-card-actions>
      </q-card-section>

      <!-- 進度條 -->
      <q-card-section v-if="isCalculating">
        <div class="text-center q-mb-sm">
          {{ progressMessage }}
        </div>
        <q-linear-progress
          rounded
          size="md"
          :value="progress"
          :animation-speed="50"
          color="primary"
          class="q-mb-xs"
        />
      </q-card-section>
    </q-card>

    <!-- 結果顯示區域 -->
    <q-card v-if="batchResults.length > 0 && !isCalculating" class="q-mt-lg">
      <q-card-section>
        <div class="text-h6 text-weight-bold q-mb-md">分析結果</div>
        <div class="row q-mb-md">
          <div class="col-12 col-sm-6">
            <div class="text-subtitle1">
              分析方法：{{ getMethodName(selectedMethod) }}
            </div>
            <div class="text-subtitle1">
              彩種：{{ getLottoTypeName(selectedLottoType) }}
            </div>
          </div>
          <div class="col-12 col-sm-6 text-right">
            <!-- 下載按鈕 -->
            <div class="row items-center justify-end q-gutter-sm">
              <q-btn
                color="primary"
                icon="download"
                label="下載 Excel 檔案"
                @click="downloadExcel"
                :disable="batchResults.length === 0"
                unelevated
              />
              <q-btn
                color="secondary"
                icon="image"
                label="下載圖片檔案"
                @click="downloadImage"
                :disable="batchResults.length === 0"
                unelevated
              />
            </div>
          </div>
        </div>

        <!-- 預測號碼統計結果顯示 -->
        <div v-if="predictNumbersStatistics.length > 0" class="q-mt-lg">
          <div class="text-h6 text-weight-bold q-mb-md">根據統計結果，以下為預測開出機率較小的號碼</div>
          <div class="row q-gutter-sm">
            <q-chip
              v-for="number in predictNumbersStatistics"
              :key="number"
              color="primary"
              text-color="white"
              :label="number.toString().padStart(2, '0')"
              size="lg"
            />
          </div>
        </div>

      </q-card-section>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Notify } from 'quasar';
import * as XLSX from 'xlsx';
import ExcelJS from 'exceljs';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { useLotteryAnalysis } from '@/composables/useLotteryAnalysis';
import { handleError } from '@/utils';
import { AnalysisParameters, BallFollowParameters, TailParameters } from '@/models/batch-analysis';
import { StatResult, Occurrence } from '@/models/types';
import { getDrawMaxNumber, lottoTypeOptions } from '@/constants/lottoConstants';

const analysis = useLotteryAnalysis();

// 分析方法選項
const analysisMethodOptions = [
  { label: '版路分析', value: 'ball-follow' },
  { label: '尾數分析', value: 'tail' },
  { label: '綜合分析', value: 'pattern' }
];

// 響應式數據
const selectedMethod = ref('ball-follow');
const selectedLottoType = ref('super_lotto638');
const referenceDate = ref('');

const isCalculating = ref(false);
const progress = ref(0);
const progressMessage = ref('');
const shouldStopAnalysis = ref(false);
const batchResults = ref<BatchAnalysisResult[]>([]);

// 預測號碼統計結果
const predictNumbersStatistics = ref<number[]>([]);

// 統一的分析參數
const analysisParams = ref({
  // 版路拖牌組合
  ballComb1: 1,
  ballComb2: 1,
  ballComb3: 1,
  // 尾數拖牌組合
  tailComb1: 1,
  tailComb2: 1,
  tailComb3: 1,
  // 期數相關參數
  periodNum: 50,
  maxRange: 20,
  aheadNum: 1,
  // 分析期數（獨立參數）
  batchAnalysisRange: 100
});

// 統一的篩選條件
const filterParams = ref({
  // 版路分析篩選條件
  ballConsecutiveHits: 1,
  ballConsecutiveFilterCondition: 'above',
  ballTargetProbability: 'all',
  ballProbabilityFilterCondition: 'above',
  ballStatisticType: 'group',
  // 尾數分析篩選條件
  tailConsecutiveHits: 1,
  tailConsecutiveFilterCondition: 'above',
  tailTargetProbability: 'all',
  tailProbabilityFilterCondition: 'above',
  tailStatisticType: 'group'
});

// 篩選條件選項
const accuracyOpts = ref([
  { label: '已連續拖出 0 次', value: 0 },
  { label: '已連續拖出 1 次', value: 1 },
  { label: '已連續拖出 2 次', value: 2 },
  { label: '已連續拖出 3 次', value: 3 },
  { label: '已連續拖出 4 次', value: 4 },
  { label: '已連續拖出 5 次', value: 5 },
]);

const tailAccuracyOpts = ref([
  { label: '已連續拖出 0 次', value: 0 },
  { label: '已連續拖出 1 次', value: 1 },
  { label: '已連續拖出 2 次', value: 2 },
  { label: '已連續拖出 3 次', value: 3 },
  { label: '已連續拖出 4 次', value: 4 },
  { label: '已連續拖出 5 次', value: 5 },
  { label: '已連續拖出 6 次', value: 6 },
  { label: '已連續拖出 7 次', value: 7 },
  { label: '已連續拖出 8 次', value: 8 },
  { label: '已連續拖出 9 次', value: 9 },
  { label: '已連續拖出 10 次', value: 10 },
]);

const filterConditionOpts = ref([
  { label: '(含)以上', value: 'above' },
  { label: '(含)以下', value: 'below' },
  { label: '剛好', value: 'exact' },
]);

const targetProbabilityOpts = ref([
  { label: '全部準確率', value: 'all' },
  { label: '準確率 100%', value: 1.0 },
  { label: '準確率 90%', value: 0.9 },
  { label: '準確率 80%', value: 0.8 },
  { label: '準確率 70%', value: 0.7 },
  { label: '準確率 60%', value: 0.6 },
  { label: '準確率 50%', value: 0.5 },
  { label: '準確率 40%', value: 0.4 },
  { label: '準確率 30%', value: 0.3 },
  { label: '準確率 20%', value: 0.2 },
  { label: '準確率 10%', value: 0.1 },
]);

const statisticTypeOpts = ref([
  { label: '預測組數統計', value: 'group' },
  { label: '準確次數統計', value: 'matches' },
  { label: '連續拖牌次數統計', value: 'consecutiveHits' },
]);

// 批量分析結果類型定義
interface BatchAnalysisResult {
  date: string;
  period: string;
  analysisType: string;
  ballFollowResults?: StatResult[];
  tailResults?: StatResult[];
  ballFollowOccurrences?: Map<string, Occurrence>;
  tailOccurrences?: Map<string, Occurrence>;
  predictNumbers?: number[];
  actualNumbers?: number[];
  matches?: number[];
  // 新增詳細分析結果
  targetNumAppearances?: Map<number, number>;
  nonAppearedNumbers?: number[];
  nonAppearedByFrequency?: Map<number, number>;
  tailNumAppearances?: Map<number, number>;
  // 預測響應數據
  predictResponse?: {
    draw_date?: string;
    period?: string;
    draw_number_size?: number[];
    special_number?: number;
  };
  // 綜合分析專用數據
  rdTailAppearances?: Map<number, number>;      // 版路分析尾數
  tailRdScoreMap?: Map<number, number>;         // 尾數分析尾數
  tailScoreMap?: Map<number, number>;           // 綜合分析尾數
  tailMatchResults?: number[];                  // 版路+尾數比對結果
  tailMatchResults2?: number[];                 // 版路+綜合比對結果
  tailMatchResults3?: number[];                 // 尾數+綜合比對結果
  // 尾數分析專用統計數據
  tailAnalysisAppearances?: Map<number, number>; // 純尾數分析的統計數據
}

// 當前日期（用於限制參考期號）
const maxDate = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// 檢查是否可以開始分析
const canStartAnalysis = computed(() => {
  return selectedMethod.value &&
         selectedLottoType.value &&
         referenceDate.value &&
         !isCalculating.value;
});

// 獎號組合
const combOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
];
// 尾數組合
const tailCombOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
  { label: '四星組合', value: 4 },
  { label: '五星組合', value: 5 },
];

const periodNumOpts = ref(
  Array.from({ length: 1991 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const maxRangeOpts = ref(
  Array.from({ length: 21 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const aheadNumOpts = ref(
  Array.from({ length: 15 }, (_, i) => ({
    label: `下${i + 1}期`,
    value: i + 1,
  }))
);

const batchAnalysisRangeOpts = ref(
  Array.from({ length: 200 }, (_, i) => ({
    label: `${i + 1}期`,
    value: i + 1,
  }))
);



// 方法
const getMethodName = (method: string) => {
  const methodMap: Record<string, string> = {
    'ball-follow': '版路分析',
    'tail': '尾數分析',
    'pattern': '綜合分析'
  };
  return methodMap[method] || method;
};

const getLottoTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'super_lotto638': '威力彩',
    'lotto649': '大樂透',
    'daily539': '今彩539',
    'lotto_hk': '六合彩',
    'ca_lotto': '加州天天樂',
  };
  return typeMap[type] || type;
};

const onLottoTypeChange = () => {
  // 當彩種改變時，可以在這裡更新相關設定
};

const getAnalysisParameters = (): AnalysisParameters => {
  switch (selectedMethod.value) {
    case 'ball-follow':
      return {
        comb1: analysisParams.value.ballComb1,
        comb2: analysisParams.value.ballComb2,
        comb3: analysisParams.value.ballComb3,
        periodNum: analysisParams.value.periodNum,
        maxRange: analysisParams.value.maxRange,
        aheadNum: analysisParams.value.aheadNum
      };
    case 'tail':
      return {
        tailComb1: analysisParams.value.tailComb1,
        tailComb2: analysisParams.value.tailComb2,
        tailComb3: analysisParams.value.tailComb3,
        periodNum: analysisParams.value.periodNum,
        maxRange: analysisParams.value.maxRange,
        aheadNum: analysisParams.value.aheadNum
      };
    case 'pattern':
      return {
        comb1: analysisParams.value.ballComb1,
        comb2: analysisParams.value.ballComb2,
        comb3: analysisParams.value.ballComb3,
        tailComb1: analysisParams.value.tailComb1,
        tailComb2: analysisParams.value.tailComb2,
        tailComb3: analysisParams.value.tailComb3,
        periodNum: analysisParams.value.periodNum,
        maxRange: analysisParams.value.maxRange,
        aheadNum: analysisParams.value.aheadNum
      };
    default:
      // Return default parameters as fallback
      return {
        comb1: 1,
        comb2: 1,
        comb3: 1,
        tailComb1: 1,
        tailComb2: 1,
        tailComb3: 1,
        periodNum: 50,
        maxRange: 20,
        aheadNum: 1
      };
  }
};

const isSuperLotto = ref(false);

// 輔助函數：從分析結果中提取預測號碼和詳細統計
const extractDetailedAnalysis = (results: StatResult[], maxNumber = 49, consecutiveHitsFilter = 1, consecutiveFilterConditionValue = 'above', targetProbabilityFilter = 'all', probabilityFilterConditionValue = 'above', statisticType = 'group') => {
  // 篩選符合條件的結果
  const filteredResults = results.filter(result => {
    let isConsecutiveFilter = false;
    let isProbabilityFilter = false;

    // 根據連續拖出次數篩選條件判斷
    switch (consecutiveFilterConditionValue) {
      case 'above':
        isConsecutiveFilter = result.consecutiveHits >= consecutiveHitsFilter;
        break;
      case 'below':
        isConsecutiveFilter = result.consecutiveHits <= consecutiveHitsFilter;
        break;
      case 'exact':
        isConsecutiveFilter = result.consecutiveHits === consecutiveHitsFilter;
        break;
      default:
        isConsecutiveFilter = result.consecutiveHits >= consecutiveHitsFilter;
    }

    // 根據準確率篩選條件判斷
    if (targetProbabilityFilter === 'all') {
      isProbabilityFilter = true;
    } else {
      const probabilityValue = targetProbabilityFilter as unknown as number;
      switch (probabilityFilterConditionValue) {
        case 'above':
          isProbabilityFilter = result.targetProbability >= probabilityValue;
          break;
        case 'below':
          isProbabilityFilter = result.targetProbability <= probabilityValue;
          break;
        case 'exact':
          isProbabilityFilter = Math.abs(result.targetProbability - probabilityValue) < 0.01;
          break;
        default:
          isProbabilityFilter = result.targetProbability >= probabilityValue;
      }
    }

    return isConsecutiveFilter && isProbabilityFilter;
  });

  const targetNumAppearances = new Map<number, number>();
  const tailNumAppearances = new Map<number, number>();
  const nonAppearedFrequency = new Map<number, number>();

  // 根據統計方式統計預測號碼出現次數
  filteredResults.forEach(result => {
    result.targetNumbers.forEach(num => {
      let count = 0;
      switch (statisticType) {
        case 'group':
          count = 1;
          break;
        case 'matches':
          count = result.targetMatches;
          break;
        case 'consecutiveHits':
          count = result.consecutiveHits;
          break;
        default:
          count = 1;
      }

      targetNumAppearances.set(num, (targetNumAppearances.get(num) || 0) + count);
      // 統計尾數
      const tail = num % 10;
      tailNumAppearances.set(tail, (tailNumAppearances.get(tail) || 0) + count);
    });
  });

  // 計算未出現號碼
  const nonAppearedNumbers: number[] = [];
  for (let i = 1; i <= maxNumber; i++) {
    if (!targetNumAppearances.has(i)) {
      nonAppearedNumbers.push(i);
      // 統計未出現號碼在所有結果中的頻率
      const frequency = results.filter(result => result.targetNumbers.includes(i)).length;
      nonAppearedFrequency.set(i, frequency);
    }
  }

  // 取前10個最高機率的預測號碼
  const topPredictNumbers = Array.from(targetNumAppearances.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([num]) => num);

  return {
    predictNumbers: topPredictNumbers,
    targetNumAppearances,
    nonAppearedNumbers,
    nonAppearedByFrequency: nonAppearedFrequency,
    tailNumAppearances
  };
};

// 輔助函數：找出預測號碼與實際號碼的匹配
const findMatches = (predictNumbers: number[], actualNumbers: number[]): number[] => {
  return predictNumbers.filter(num => actualNumbers.includes(num));
};

// 輔助函數：獲取預測日期
const getPredictDate = (result: BatchAnalysisResult): string => {
  if (result.predictResponse) {
    if (result.predictResponse.draw_date) {
      return result.predictResponse.draw_date;
    } else {
      return '尚未開獎';
    }
  } else {
    return '尚未開獎';
  }
};

// 執行預測號碼統計分析
const performPredictNumbersStatistics = (columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } }, maxNumber: number) => {
  // 輸出每個欄位的統計結果
  const statisticsResults: { column: number; totalCount: number; accurateCount: number }[] = [];

  // 遍歷所有可能的欄位（根據彩種的最大號碼數量確定欄位數量）
  for (let colIndex = 3; colIndex <= maxNumber + 2; colIndex++) {
    const stats = columnStats[colIndex] || { totalCount: 0, accurateCount: 0 };
    statisticsResults.push({
      column: colIndex - 2, // 轉換為相對欄位編號（第1欄、第2欄...）
      totalCount: stats.totalCount,
      accurateCount: stats.accurateCount
    });
  }

  // 找出準確預測數量小於等於2的欄位
  const lowAccuracyColumns = statisticsResults.filter(stat => stat.accurateCount <= 2);

  // 根據低準確度欄位，找出對應的號碼
  const lowAccuracyNumbers: number[] = [];

  // 遍歷所有分析結果，找出在低準確度欄位中出現的號碼
  const result = batchResults.value[batchResults.value.length - 1];
  if (result.targetNumAppearances) {
    const sortedNumbers = Array.from(result.targetNumAppearances.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([num]) => num);

    // 檢查每個低準確度欄位對應的號碼
    lowAccuracyColumns.forEach(stat => {
      const numberIndex = stat.column - 1; // 轉換為陣列索引
      if (numberIndex < sortedNumbers.length) {
        const number = sortedNumbers[numberIndex];
        if (!lowAccuracyNumbers.includes(number)) {
          lowAccuracyNumbers.push(number);
        }
      }
    });
  }

  // 排序並去重
  const uniqueLowAccuracyNumbers = [...new Set(lowAccuracyNumbers)].sort((a, b) => a - b);

  // 更新響應式數據，用於在UI中顯示
  predictNumbersStatistics.value = uniqueLowAccuracyNumbers;
};

// 執行綜合分析預測號碼統計分析
const performPatternPredictNumbersStatistics = (columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } }, maxNumber: number) => {
  // 輸出每個欄位的統計結果
  const statisticsResults: { column: number; totalCount: number; accurateCount: number }[] = [];

  // 遍歷所有可能的欄位（根據彩種的最大號碼數量確定欄位數量）
  for (let colIndex = 3; colIndex <= maxNumber + 2; colIndex++) {
    const stats = columnStats[colIndex] || { totalCount: 0, accurateCount: 0 };
    statisticsResults.push({
      column: colIndex - 2, // 轉換為相對欄位編號（第1欄、第2欄...）
      totalCount: stats.totalCount,
      accurateCount: stats.accurateCount
    });
  }

  // 找出準確預測數量小於等於2的欄位
  const lowAccuracyColumns = statisticsResults.filter(stat => stat.accurateCount <= 2);

  // 根據低準確度欄位，找出對應的號碼
  const lowAccuracyNumbers: number[] = [];

  // 遍歷所有分析結果，找出在低準確度欄位中出現的號碼
  const result = batchResults.value[batchResults.value.length - 1];
  if (result.targetNumAppearances) {
    const sortedNumbers = Array.from(result.targetNumAppearances.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([num]) => num);

    // 檢查每個低準確度欄位對應的號碼
    lowAccuracyColumns.forEach(stat => {
      const numberIndex = stat.column - 1; // 轉換為陣列索引
      if (numberIndex < sortedNumbers.length) {
        const number = sortedNumbers[numberIndex];
        if (!lowAccuracyNumbers.includes(number)) {
          lowAccuracyNumbers.push(number);
        }
      }
    });
  }

  // 排序並去重
  const uniqueLowAccuracyNumbers = [...new Set(lowAccuracyNumbers)].sort((a, b) => a - b);

  // 更新響應式數據，用於在UI中顯示
  predictNumbersStatistics.value = uniqueLowAccuracyNumbers;
};

// 執行尾數統計分析
const performTailNumbersStatistics = (columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } }, maxNumber: number) => {
  // 輸出每個欄位的統計結果（尾數最多10個欄位，0-9）
  const statisticsResults: { column: number; totalCount: number; accurateCount: number }[] = [];

  // 遍歷所有可能的欄位（最多10個尾數欄位，因為尾數只有0-9）
  for (let colIndex = 3; colIndex <= 12; colIndex++) {
    const stats = columnStats[colIndex] || { totalCount: 0, accurateCount: 0 };
    statisticsResults.push({
      column: colIndex - 2, // 轉換為相對欄位編號（第1欄、第2欄...）
      totalCount: stats.totalCount,
      accurateCount: stats.accurateCount
    });
  }

  // 找出準確預測數量小於等於2的欄位
  const lowAccuracyColumns = statisticsResults.filter(stat => stat.accurateCount <= 2);

  // 根據低準確度欄位，找出對應的尾數
  const lowAccuracyTails: number[] = [];

  // 遍歷所有分析結果，找出在低準確度欄位中出現的尾數
  const result = batchResults.value[batchResults.value.length - 1];
  if (result.tailNumAppearances) {
    const sortedTails = Array.from(result.tailNumAppearances.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([tail]) => tail);

    // 檢查每個低準確度欄位對應的尾數
    lowAccuracyColumns.forEach(stat => {
      const tailIndex = stat.column - 1; // 轉換為陣列索引
      if (tailIndex < sortedTails.length) {
        const tail = sortedTails[tailIndex];
        if (!lowAccuracyTails.includes(tail)) {
          lowAccuracyTails.push(tail);
        }
      }
    });
  }

  // 排序並去重
  const uniqueLowAccuracyTails = [...new Set(lowAccuracyTails)].sort((a, b) => a - b);

  // 將尾數轉換為對應的號碼並更新響應式數據
  const correspondingNumbers: number[] = [];

  uniqueLowAccuracyTails.forEach(tail => {
    for (let num = tail; num <= maxNumber; num += 10) {
      if (num > 0) { // 確保號碼大於0
        correspondingNumbers.push(num);
      }
    }
  });

  // 排序並更新響應式數據
  const sortedCorrespondingNumbers = correspondingNumbers.sort((a, b) => a - b);

  // 更新響應式數據，用於在UI中顯示
  predictNumbersStatistics.value = sortedCorrespondingNumbers;
};

// 執行版路分析統計
const performBallFollowStatistics = async (maxNumber: number) => {
  // 初始化統計數據
  const columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } } = {};

  // 遍歷所有分析結果進行統計
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 統計每個欄位的號碼數量
      sortedNumbers.forEach((number, index) => {
        const colIndex = index + 3; // Excel欄位索引（跳過日期欄位）
        if (!columnStats[colIndex]) {
          columnStats[colIndex] = { totalCount: 0, accurateCount: 0 };
        }
        columnStats[colIndex].totalCount++;

        // 檢查是否為準確預測
        if (result.actualNumbers && result.actualNumbers.includes(number)) {
          columnStats[colIndex].accurateCount++;
        }
      });
    }
  });

  // 執行統計分析
  performPredictNumbersStatistics(columnStats, maxNumber);
};

// 執行尾數分析統計
const performTailAnalysisStatistics = async (maxNumber: number) => {
  // 初始化統計數據
  const columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } } = {};

  // 遍歷所有分析結果進行統計
  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 統計每個欄位的尾數數量
      sortedTails.forEach((tail, index) => {
        const colIndex = index + 3; // Excel欄位索引（跳過日期欄位）
        if (!columnStats[colIndex]) {
          columnStats[colIndex] = { totalCount: 0, accurateCount: 0 };
        }
        columnStats[colIndex].totalCount++;

        // 檢查是否為準確預測（比較尾數）
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          if (actualTails.includes(tail)) {
            columnStats[colIndex].accurateCount++;
          }
        }
      });
    }
  });

  // 執行統計分析
  performTailNumbersStatistics(columnStats, maxNumber);
};

// 執行綜合分析統計
const performPatternAnalysisStatistics = async (maxNumber: number) => {
  // 初始化統計數據
  const columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } } = {};

  // 遍歷所有分析結果進行統計（使用版路分析的數據）
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 統計每個欄位的號碼數量
      sortedNumbers.forEach((number, index) => {
        const colIndex = index + 3; // Excel欄位索引（跳過日期欄位）
        if (!columnStats[colIndex]) {
          columnStats[colIndex] = { totalCount: 0, accurateCount: 0 };
        }
        columnStats[colIndex].totalCount++;

        // 檢查是否為準確預測
        if (result.actualNumbers && result.actualNumbers.includes(number)) {
          columnStats[colIndex].accurateCount++;
        }
      });
    }
  });

  // 執行統計分析
  performPatternPredictNumbersStatistics(columnStats, maxNumber);
};

// 輔助函數：將號碼格式化為單一列的陣列
const formatNumbersIntoRows = (numbers: number[], isForTail = false): string[][] => {
  const formattedNumbers = numbers.map(n =>
    isForTail ? n.toString() : n.toString().padStart(2, '0')
  );
  return [formattedNumbers]; // 返回包含單一列的陣列
};

// 輔助函數：應用紅色字體樣式給匹配的號碼（使用標記方式）
const applyRedFontForMatches = (worksheet: XLSX.WorkSheet, sheetData: (string | number)[][], type: 'predict' | 'tail') => {
  // 遍歷數據行，從第4行開始（跳過標題行）
  for (let rowIndex = 3; rowIndex < sheetData.length; rowIndex++) {
    const row = sheetData[rowIndex];
    const dateCell = row[0];

    // 跳過空白日期行（換列對齊的行）
    if (!dateCell || dateCell === '') continue;

    // 找到對應的批量分析結果
    const result = batchResults.value.find(r => r.date === dateCell);
    if (!result || !result.actualNumbers) {
      // 找不到對應結果
      continue;
    }

    // 檢查每個號碼是否與實際開獎號碼匹配（從第3欄開始，跳過分析日期和預測日期）
    for (let colIndex = 2; colIndex < row.length; colIndex++) {
      const cellValue = row[colIndex];
      if (cellValue && cellValue !== '') {
        let isMatch = false;

        if (type === 'predict') {
          // 預測號碼：直接比較
          const numberToCheck = parseInt(cellValue.toString().replace(/^0+/, ''), 10);
          isMatch = result.actualNumbers.includes(numberToCheck);
        } else {
          // 尾數：比較尾數
          const tailToCheck = parseInt(cellValue.toString(), 10);
          const actualTails = result.actualNumbers.map(n => n % 10);
          isMatch = actualTails.includes(tailToCheck);
        }

        if (isMatch) {
          const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });
          // 使用簡單標記方式（在值後面加上符號）
          const markedValue = `${cellValue}★`;

          // 更新工作表數據
          if (!worksheet[cellRef]) {
            worksheet[cellRef] = { v: markedValue, t: 's' };
          } else {
            worksheet[cellRef].v = markedValue;
          }

          // 同時更新原始數據
          sheetData[rowIndex][colIndex] = markedValue;
        }
      }
    }
  }
};

// 創建參數設定工作表
const createParametersSheet = (workbook: XLSX.WorkBook, analysisType: string) => {
  const sheetData: (string | number)[][] = [];

  // 標題
  const methodName = getMethodName(selectedMethod.value);
  const lottoName = getLottoTypeName(selectedLottoType.value);
  sheetData.push([`${methodName} - 參數設定`]);
  sheetData.push([]);

  // 基本設定
  sheetData.push(['基本設定']);
  sheetData.push(['彩種', lottoName]);
  sheetData.push(['分析方法', methodName]);
  sheetData.push([]);

  // 根據分析類型添加對應的參數
  switch (analysisType) {
    case 'ball':
      // 版路分析參數
      sheetData.push(['版路分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['拖牌區間', analysisParams.value.maxRange]);
      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 篩選條件
      sheetData.push(['篩選條件']);
      const accuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveHits)?.label || filterParams.value.ballConsecutiveHits;
      const filterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveFilterCondition)?.label || filterParams.value.ballConsecutiveFilterCondition;
      const statisticLabel = statisticTypeOpts.value.find(opt => opt.value === filterParams.value.ballStatisticType)?.label || filterParams.value.ballStatisticType;
      const targetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.ballTargetProbability)?.label || filterParams.value.ballTargetProbability;
      const probabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballProbabilityFilterCondition)?.label || filterParams.value.ballProbabilityFilterCondition;
      sheetData.push(['連續拖出次數', accuracyLabel]);
      sheetData.push(['篩選條件', filterLabel]);
      sheetData.push(['統計方式', statisticLabel]);
      sheetData.push(['準確率', targetProbabilityLabel]);
      if (filterParams.value.ballTargetProbability !== 'all') {
        sheetData.push(['準確率篩選條件', probabilityFilterLabel]);
      }
      break;

    case 'tail':
      // 尾數分析參數
      sheetData.push(['尾數分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['拖牌區間', analysisParams.value.maxRange]);
      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 篩選條件
      sheetData.push(['篩選條件']);
      const tailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveHits)?.label || filterParams.value.tailConsecutiveHits;
      const tailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveFilterCondition)?.label || filterParams.value.tailConsecutiveFilterCondition;
      const tailStatisticLabel = statisticTypeOpts.value.find(opt => opt.value === filterParams.value.tailStatisticType)?.label || filterParams.value.tailStatisticType;
      const tailTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.tailTargetProbability)?.label || filterParams.value.tailTargetProbability;
      const tailProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailProbabilityFilterCondition)?.label || filterParams.value.tailProbabilityFilterCondition;
      sheetData.push(['連續拖出次數', tailAccuracyLabel]);
      sheetData.push(['篩選條件', tailFilterLabel]);
      sheetData.push(['統計方式', tailStatisticLabel]);
      sheetData.push(['準確率', tailTargetProbabilityLabel]);
      if (filterParams.value.tailTargetProbability !== 'all') {
        sheetData.push(['準確率篩選條件', tailProbabilityFilterLabel]);
      }
      break;

    case 'pattern':
      // 綜合分析參數
      sheetData.push(['綜合分析參數']);
      sheetData.push([]);

      sheetData.push(['版路分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['拖牌區間', analysisParams.value.maxRange]);
      sheetData.push([]);

      sheetData.push(['尾數分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['拖牌區間', analysisParams.value.maxRange]);
      sheetData.push([]);

      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 版路分析篩選條件
      sheetData.push(['版路分析篩選條件']);
      const patternBallAccuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveHits)?.label || filterParams.value.ballConsecutiveHits;
      const patternBallFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveFilterCondition)?.label || filterParams.value.ballConsecutiveFilterCondition;
      const patternBallStatisticLabel = statisticTypeOpts.value.find(opt => opt.value === filterParams.value.ballStatisticType)?.label || filterParams.value.ballStatisticType;
      const patternBallTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.ballTargetProbability)?.label || filterParams.value.ballTargetProbability;
      const patternBallProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballProbabilityFilterCondition)?.label || filterParams.value.ballProbabilityFilterCondition;
      sheetData.push(['連續拖出次數', patternBallAccuracyLabel]);
      sheetData.push(['篩選條件', patternBallFilterLabel]);
      sheetData.push(['統計方式', patternBallStatisticLabel]);
      sheetData.push(['準確率', patternBallTargetProbabilityLabel]);
      if (filterParams.value.ballTargetProbability !== 'all') {
        sheetData.push(['準確率篩選條件', patternBallProbabilityFilterLabel]);
      }
      sheetData.push([]);

      // 尾數分析篩選條件
      sheetData.push(['尾數分析篩選條件']);
      const patternTailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveHits)?.label || filterParams.value.tailConsecutiveHits;
      const patternTailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveFilterCondition)?.label || filterParams.value.tailConsecutiveFilterCondition;
      const patternTailStatisticLabel = statisticTypeOpts.value.find(opt => opt.value === filterParams.value.tailStatisticType)?.label || filterParams.value.tailStatisticType;
      const patternTailTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.tailTargetProbability)?.label || filterParams.value.tailTargetProbability;
      const patternTailProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailProbabilityFilterCondition)?.label || filterParams.value.tailProbabilityFilterCondition;
      sheetData.push(['連續拖出次數', patternTailAccuracyLabel]);
      sheetData.push(['篩選條件', patternTailFilterLabel]);
      sheetData.push(['統計方式', patternTailStatisticLabel]);
      sheetData.push(['準確率', patternTailTargetProbabilityLabel]);
      if (filterParams.value.tailTargetProbability !== 'all') {
        sheetData.push(['準確率篩選條件', patternTailProbabilityFilterLabel]);
      }
      break;
  }

  sheetData.push([]);

  // 分析時間
  sheetData.push(['分析資訊']);
  sheetData.push(['分析時間', new Date().toLocaleString('zh-TW')]);
  sheetData.push(['分析期數', batchResults.value.length]);

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 20 }, // 參數名稱欄
    { wch: 30 }  // 參數值欄
  ];
  worksheet['!cols'] = colWidths;

  // 設置樣式 - 標題行加粗
  const titleRows = [0, 4, 9, 14]; // 根據不同分析類型調整
  titleRows.forEach(rowIndex => {
    if (rowIndex < sheetData.length) {
      const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: 0 });
      if (worksheet[cellRef]) {
        worksheet[cellRef].s = {
          font: { bold: true, sz: 12 },
          alignment: { horizontal: 'left' }
        };
      }
    }
  });

  XLSX.utils.book_append_sheet(workbook, worksheet, '參數設定');
};

// 計算綜合分析專用數據（參考 PatternResult.vue 的邏輯）
const calculatePatternAnalysisData = (
  ballFollowResults: StatResult[],
  tailResults: StatResult[]
) => {
  // 初始化數據結構
  const rdTailAppearances = new Map<number, number>();
  const tailRdScoreMap = new Map<number, number>();
  const tailScoreMap = new Map<number, number>();

  // 初始化尾數分數
  for (let i = 0; i < 10; i++) {
    tailScoreMap.set(i, 0);
  }

  // 計算版路分析尾數 (rdTailAppearances)
  const ballFilteredResults = ballFollowResults.filter(result => {
    switch (filterParams.value.ballConsecutiveFilterCondition) {
      case 'above': return result.consecutiveHits >= filterParams.value.ballConsecutiveHits;
      case 'below': return result.consecutiveHits <= filterParams.value.ballConsecutiveHits;
      case 'exact': return result.consecutiveHits === filterParams.value.ballConsecutiveHits;
      default: return true;
    }
  });

  for (const result of ballFilteredResults) {
    const tailSet = new Set<number>();
    for (const number of result.targetNumbers) {
      const tailNumber = number % 10;
      tailSet.add(tailNumber);
    }
    for (const tailNumber of tailSet) {
      let count = 0;
      switch (filterParams.value.ballStatisticType) {
        case 'group':
          count = 1;
          break;
        case 'matches':
          count = result.targetMatches;
          break;
        case 'consecutiveHits':
          count = result.consecutiveHits;
          break;
        default:
          count = 1;
      }

      rdTailAppearances.set(tailNumber, (rdTailAppearances.get(tailNumber) || 0) + count);
    }
  }

  // 計算尾數分析尾數 (tailRdScoreMap)
  const tailFilteredResults = tailResults.filter(result => {
    let isConsecutiveFilter = false;
    let isProbabilityFilter = false;

    // 根據連續拖出次數篩選條件判斷
    switch (filterParams.value.tailConsecutiveFilterCondition) {
      case 'above':
        isConsecutiveFilter = result.consecutiveHits >= filterParams.value.tailConsecutiveHits;
        break;
      case 'below':
        isConsecutiveFilter = result.consecutiveHits <= filterParams.value.tailConsecutiveHits;
        break;
      case 'exact':
        isConsecutiveFilter = result.consecutiveHits === filterParams.value.tailConsecutiveHits;
        break;
      default:
        isConsecutiveFilter = true;
    }

    // 根據準確率篩選條件判斷
    if (filterParams.value.tailTargetProbability === 'all') {
      isProbabilityFilter = true;
    } else {
      const probabilityValue = filterParams.value.tailTargetProbability as unknown as number;
      switch (filterParams.value.tailProbabilityFilterCondition) {
        case 'above':
          isProbabilityFilter = result.targetProbability >= probabilityValue;
          break;
        case 'below':
          isProbabilityFilter = result.targetProbability <= probabilityValue;
          break;
        case 'exact':
          isProbabilityFilter = Math.abs(result.targetProbability - probabilityValue) < 0.01;
          break;
        default:
          isProbabilityFilter = result.targetProbability >= probabilityValue;
      }
    }

    return isConsecutiveFilter && isProbabilityFilter;
  });

  for (const result of tailFilteredResults) {
    for (const tailNumber of result.targetNumbers) {
      let count = 0;
      switch (filterParams.value.tailStatisticType) {
        case 'group':
          count = 1;
          break;
        case 'matches':
          count = result.targetMatches;
          break;
        case 'consecutiveHits':
          count = result.consecutiveHits;
          break;
        default:
          count = 1;
      }

      tailRdScoreMap.set(tailNumber, (tailRdScoreMap.get(tailNumber) || 0) + count);
    }
  }

  // 計算綜合分析尾數 (tailScoreMap) - 結合版路和尾數分析的分數
  const rdTotalCount = Math.max(1, ballFilteredResults.reduce((sum, r) => sum + r.consecutiveHits, 0));
  const tailTotalCount = Math.max(1, tailFilteredResults.reduce((sum, r) => sum + r.consecutiveHits, 0));

  // 版路分析貢獻
  for (const [tailNumber, count] of rdTailAppearances.entries()) {
    const score = count / rdTotalCount;
    tailScoreMap.set(tailNumber, (tailScoreMap.get(tailNumber) || 0) + score);
  }

  // 尾數分析貢獻
  for (const [tailNumber, score] of tailRdScoreMap.entries()) {
    const normalizedScore = score / tailTotalCount;
    tailScoreMap.set(tailNumber, (tailScoreMap.get(tailNumber) || 0) + normalizedScore);
  }

  // 標準化綜合分數
  for (const [tailNumber, score] of tailScoreMap.entries()) {
    tailScoreMap.set(tailNumber, Number(((score / 2) * 100).toFixed(1)));
  }

  // 排序所有Map
  const sortedRdTailAppearances = new Map([...rdTailAppearances.entries()].sort((a, b) => b[1] - a[1]));
  const sortedTailRdScoreMap = new Map([...tailRdScoreMap.entries()].sort((a, b) => b[1] - a[1]));
  const sortedTailScoreMap = new Map([...tailScoreMap.entries()].sort((a, b) => b[1] - a[1]));

  // 計算比對結果
  const rdTails = Array.from(sortedRdTailAppearances.keys()).slice(0, 5);
  const tails = Array.from(sortedTailRdScoreMap.keys()).slice(0, 5);
  const patterns = Array.from(sortedTailScoreMap.keys()).slice(0, 5);

  const tailMatchResults: number[] = [];
  const tailMatchResults2: number[] = [];
  const tailMatchResults3: number[] = [];

  // 版路+尾數
  for (const tailNumber of rdTails) {
    if (tails.includes(tailNumber)) {
      tailMatchResults.push(tailNumber);
    }
  }

  // 版路+綜合
  for (const tailNumber of rdTails) {
    if (patterns.includes(tailNumber)) {
      tailMatchResults2.push(tailNumber);
    }
  }

  // 尾數+綜合
  for (const tailNumber of tails) {
    if (patterns.includes(tailNumber)) {
      tailMatchResults3.push(tailNumber);
    }
  }

  // 排序比對結果 (0排在最後面，其他最小排前面)
  const sortTails = (a: number, b: number) => (a === 0 ? 1 : b === 0 ? -1 : a - b);
  tailMatchResults.sort(sortTails);
  tailMatchResults2.sort(sortTails);
  tailMatchResults3.sort(sortTails);

  return {
    rdTailAppearances: sortedRdTailAppearances,
    tailRdScoreMap: sortedTailRdScoreMap,
    tailScoreMap: sortedTailScoreMap,
    tailMatchResults,
    tailMatchResults2,
    tailMatchResults3
  };
};

// 分批處理配置
const BATCH_SIZE = 1; // 每批處理的期數
const MEMORY_CLEANUP_INTERVAL = 1; // 每5批清理一次記憶體

const startBatchAnalysis = async () => {
  if (!canStartAnalysis.value) {
    Notify.create({
      type: 'warning',
      message: '請完整填寫所有必要參數'
    });
    return;
  }

  try {
    isCalculating.value = true;
    shouldStopAnalysis.value = false;
    isSuperLotto.value = selectedLottoType.value === 'super_lotto638';
    progress.value = 0;
    progressMessage.value = '準備開始...';
    batchResults.value = []; // 清空之前的結果
    predictNumbersStatistics.value = []; // 清空統計結果

    // 準備API請求參數 - 獲取要分析的期數
    const analysisCount = analysisParams.value.batchAnalysisRange;

    const response = await LOTTO_API.getLottoList({
      draw_type: selectedLottoType.value,
      date_end: referenceDate.value,
      limit: analysisCount
    });
    // 將結果反轉，讓時間由舊到新排列
    const referenceResults = response.data.reverse();

    progressMessage.value = '正在進行分析...';

    // 分批處理分析
    const params = getAnalysisParameters();
    const totalBatches = Math.ceil(referenceResults.length / BATCH_SIZE);
    let processedCount = 0;
    let batchCount = 0;

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      // 檢查是否需要停止分析
      if (shouldStopAnalysis.value) {
        progressMessage.value = '分析已中斷';
        break;
      }

      const startIndex = batchIndex * BATCH_SIZE;
      const endIndex = Math.min(startIndex + BATCH_SIZE, referenceResults.length);
      const currentBatch = referenceResults.slice(startIndex, endIndex);

      progressMessage.value = `處理批次 ${batchIndex + 1}/${totalBatches} (${startIndex + 1}-${endIndex}/${referenceResults.length})`;

      // 處理當前批次
      const currentBatchResults = [];
      for (const result of currentBatch) {
        // 再次檢查是否需要停止分析
        if (shouldStopAnalysis.value) {
          break;
        }
        // 更新進度條
        progress.value = processedCount / referenceResults.length;
        progressMessage.value = `分析中... ${processedCount++}/${referenceResults.length}`;

        // 獲取分析用的歷史數據
        const historyResponse = await LOTTO_API.getLottoList({
          draw_type: selectedLottoType.value,
          date_end: result.draw_date,
          limit: params.periodNum
        });

        // 獲取預測期的實際開獎結果
        const aheadCount = params.aheadNum;
        const predictResponse = await LOTTO_API.getLottoPredict({
          draw_type: selectedLottoType.value,
          draw_date: result.draw_date,
          ahead_count: aheadCount
        });

        // 組合實際開獎號碼（包含特別號/第二區）
        let actualNumbers: number[] = [];
        if (predictResponse.data) {
          actualNumbers = [...(predictResponse.data.draw_number_size || [])];
          // 威力彩的第二區號碼和其他彩種的特別號都要添加到實際開獎號碼中
          if (predictResponse.data.special_number) {
            actualNumbers.push(predictResponse.data.special_number);
          }
        }

        let batchResult: BatchAnalysisResult = {
          date: result.draw_date,
          period: result.period,
          analysisType: selectedMethod.value,
          actualNumbers: actualNumbers,
          // 保存預測響應數據以供Excel使用
          predictResponse: predictResponse.data
        };

        switch (selectedMethod.value) {
          case 'ball-follow':
            const ballFollowAnalysis = await doRdCalculating(historyResponse.data);
            batchResult.ballFollowResults = ballFollowAnalysis.data;
            batchResult.ballFollowOccurrences = ballFollowAnalysis.occurrences;

            // 獲取彩種的最大號碼
            const maxNumber = getDrawMaxNumber(selectedLottoType.value);
            const detailedAnalysis = extractDetailedAnalysis(
              ballFollowAnalysis.data,
              maxNumber,
              filterParams.value.ballConsecutiveHits,
              filterParams.value.ballConsecutiveFilterCondition,
              filterParams.value.ballTargetProbability,
              filterParams.value.ballProbabilityFilterCondition,
              filterParams.value.ballStatisticType
            );

            batchResult.predictNumbers = detailedAnalysis.predictNumbers;
            batchResult.targetNumAppearances = detailedAnalysis.targetNumAppearances;
            batchResult.nonAppearedNumbers = detailedAnalysis.nonAppearedNumbers;
            batchResult.nonAppearedByFrequency = detailedAnalysis.nonAppearedByFrequency;
            batchResult.tailNumAppearances = detailedAnalysis.tailNumAppearances;
            batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
            break;
          case 'tail':
            const tailAnalysis = await doTailRdCalculating(historyResponse.data);
            batchResult.tailResults = tailAnalysis.data;
            batchResult.tailOccurrences = tailAnalysis.occurrences;

            const tailDetailedAnalysis = extractDetailedAnalysis(
              tailAnalysis.data,
              10, // 尾數最大為9
              filterParams.value.tailConsecutiveHits,
              filterParams.value.tailConsecutiveFilterCondition,
              filterParams.value.tailTargetProbability,
              filterParams.value.tailProbabilityFilterCondition,
              filterParams.value.tailStatisticType
            );

            batchResult.predictNumbers = tailDetailedAnalysis.predictNumbers;
            batchResult.targetNumAppearances = tailDetailedAnalysis.targetNumAppearances;
            batchResult.nonAppearedNumbers = tailDetailedAnalysis.nonAppearedNumbers;
            batchResult.nonAppearedByFrequency = tailDetailedAnalysis.nonAppearedByFrequency;
            batchResult.tailNumAppearances = tailDetailedAnalysis.tailNumAppearances;
            batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
            break;
          case 'pattern':
            const ballAnalysis = await doRdCalculating(historyResponse.data);
            const tailAnalysis2 = await doTailRdCalculating(historyResponse.data);
            batchResult.ballFollowResults = ballAnalysis.data;
            batchResult.ballFollowOccurrences = ballAnalysis.occurrences;
            batchResult.tailResults = tailAnalysis2.data;
            batchResult.tailOccurrences = tailAnalysis2.occurrences;

            // 獲取彩種的最大號碼
            const maxNumber2 = getDrawMaxNumber(selectedLottoType.value);

            // 版路分析詳細統計
            const ballDetailedAnalysis = extractDetailedAnalysis(
              ballAnalysis.data,
              maxNumber2,
              filterParams.value.ballConsecutiveHits,
              filterParams.value.ballConsecutiveFilterCondition,
              filterParams.value.ballTargetProbability,
              filterParams.value.ballProbabilityFilterCondition,
              filterParams.value.ballStatisticType
            );

            // 尾數分析詳細統計
            const tailDetailedAnalysis2 = extractDetailedAnalysis(
              tailAnalysis2.data,
              10,
              filterParams.value.tailConsecutiveHits,
              filterParams.value.tailConsecutiveFilterCondition,
              filterParams.value.tailTargetProbability,
              filterParams.value.tailProbabilityFilterCondition,
              filterParams.value.tailStatisticType
            );

            // 綜合分析的預測號碼需要結合版路和尾數分析
            const ballPredictNumbers = ballDetailedAnalysis.predictNumbers;
            const tailPredictNumbers = tailDetailedAnalysis2.predictNumbers;
            batchResult.predictNumbers = [...new Set([...ballPredictNumbers, ...tailPredictNumbers])];

            // 合併統計數據（使用版路分析的數據作為主要統計）
            batchResult.targetNumAppearances = ballDetailedAnalysis.targetNumAppearances;
            batchResult.nonAppearedNumbers = ballDetailedAnalysis.nonAppearedNumbers;
            batchResult.nonAppearedByFrequency = ballDetailedAnalysis.nonAppearedByFrequency;
            batchResult.tailNumAppearances = ballDetailedAnalysis.tailNumAppearances;

            // 存儲尾數分析的專用統計數據
            batchResult.tailAnalysisAppearances = tailDetailedAnalysis2.tailNumAppearances;

            // 計算綜合分析專用數據
            const patternAnalysisData = calculatePatternAnalysisData(
              ballAnalysis.data,
              tailAnalysis2.data
            );

            batchResult.rdTailAppearances = patternAnalysisData.rdTailAppearances;
            batchResult.tailRdScoreMap = patternAnalysisData.tailRdScoreMap;
            batchResult.tailScoreMap = patternAnalysisData.tailScoreMap;
            batchResult.tailMatchResults = patternAnalysisData.tailMatchResults;
            batchResult.tailMatchResults2 = patternAnalysisData.tailMatchResults2;
            batchResult.tailMatchResults3 = patternAnalysisData.tailMatchResults3;

            batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
            break;
        }

        // 將結果添加到當前批次
        currentBatchResults.push(batchResult);

        // 清理不必要的引用以釋放記憶體
        if (batchResult.ballFollowResults) {
          batchResult.ballFollowResults = undefined;
        }
        if (batchResult.tailResults) {
          batchResult.tailResults = undefined;
        }
        if (batchResult.ballFollowOccurrences) {
          batchResult.ballFollowOccurrences = undefined;
        }
        if (batchResult.tailOccurrences) {
          batchResult.tailOccurrences = undefined;
        }

        // 每處理一定數量後暫停一下，讓瀏覽器有機會進行垃圾回收
        if (processedCount % 5 === 0) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      batchResults.value.push(...currentBatchResults); // 將當前批次結果添加到總結果中

      // 定期清理記憶體
      batchCount++;
      if (batchCount % MEMORY_CLEANUP_INTERVAL === 0) {
        // 強制垃圾回收
        cleanupMemory();
        // 暫停一下讓瀏覽器處理
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    progressMessage.value = '分析完成！';
    progress.value = 1;

    // 執行統計計算
    await performBatchStatistics();

    // 最終記憶體清理
    cleanupMemory();

    Notify.create({
      type: 'positive',
      position: 'top',
      message: '分析完成！'
    });

  } catch (error) {
    handleError(error);
  } finally {
    isCalculating.value = false;
  }
};

// 執行批量統計分析
const performBatchStatistics = async () => {
  if (batchResults.value.length === 0) {
    // 沒有分析結果，跳過統計
    return;
  }

  try {
    // 獲取彩種的最大號碼數量
    const maxNumber = getDrawMaxNumber(selectedLottoType.value);

    // 根據分析方法執行相應的統計
    switch (selectedMethod.value) {
      case 'ball-follow':
        await performBallFollowStatistics(maxNumber);
        break;
      case 'tail':
        await performTailAnalysisStatistics(maxNumber);
        break;
      case 'pattern':
        await performPatternAnalysisStatistics(maxNumber);
        break;
      default:
        console.log('未知的分析方法:', selectedMethod.value);
    }
  } catch (error) {
    console.error('統計分析過程中發生錯誤:', error);
  }
};

// 記憶體清理
const cleanupMemory = () => {
  if ('gc' in window && typeof (window as { gc?: () => void }).gc === 'function') {
    (window as { gc: () => void }).gc();
  }
};

const stopBatchAnalysis = () => {
  shouldStopAnalysis.value = true;
  analysis.stopAnalyzer();
  isCalculating.value = false;
  progressMessage.value = '分析已中斷';

  Notify.create({
    type: 'warning',
    message: '分析已中斷'
  });
};

// 獎號拖牌
const doRdCalculating = async (drawResults: LottoItem[]) => {
  const params = getAnalysisParameters() as BallFollowParameters;
  const serializedDrawResults = drawResults.map((item: LottoItem) => {
    const numbers = [...item.draw_number_size];
    if (item.special_number && !isSuperLotto.value) {
      // 除威力彩，其餘有彩種皆須加入特別號
      numbers.push(item.special_number);
    }
    // 返回一個純粹的對象
    return {
      numbers: [...numbers], // 確保創建新數組
      period: String(item.period), // 確保 period 是字符串
    };
  }).reverse();

  // 設置配置
  analysis.init(
    {
      firstGroupSize: params.comb1 || 1,
      secondGroupSize: params.comb2 || 1,
      targetGroupSize: params.comb3 || 1,
      maxRange: params.maxRange || 20,
      lookAheadCount: params.aheadNum || 1,
    },
    serializedDrawResults
  );

  return await analysis.analyzeWithProgress();
};

// 尾數拖牌
const doTailRdCalculating = async (drawResults: LottoItem[]) => {
  const params = getAnalysisParameters() as TailParameters;

  // 創建數據結構
  const serializedTailResults = drawResults.map((item: LottoItem) => {
    const numbers = new Set<number>();

    for (let number of item.draw_number_size) {
      numbers.add(number % 10);
    }

    if (item.special_number && !isSuperLotto.value) {
      numbers.add(item.special_number % 10);
    }

    const sorted = Array.from(numbers).sort((a, b) => {
      if (a === 0) return 1;
      if (b === 0) return -1;
      return a - b;
    });

    return {
      period: String(item.period),
      numbers: [...sorted],
    };
  }).reverse();

  analysis.init(
    {
      firstGroupSize: params.tailComb1 || 1,
      secondGroupSize: params.tailComb2 || 1,
      targetGroupSize: params.tailComb3 || 1,
      maxRange: params.maxRange || 20,
      lookAheadCount: params.aheadNum || 1,
    },
    serializedTailResults
  );

  return await analysis.analyzeWithProgress();
};



// 暫時保留原有的 XLSX 版本作為備用
const downloadExcelOld = () => {
  if (batchResults.value.length === 0) {
    Notify.create({
      type: 'warning',
      message: '沒有可下載的結果'
    });
    return;
  }

  try {
    // 創建工作簿
    const workbook = XLSX.utils.book_new();

    // 根據分析方法創建不同的工作表
    switch (selectedMethod.value) {
      case 'ball-follow':
        createParametersSheet(workbook, 'ball');
        createPredictNumbersSheet(workbook);
        createNonAppearedByFrequencySheet(workbook);
        createNonAppearedBySizeSheet(workbook);
        createTailNumbersSheet(workbook);
        createActualNumbersSheet(workbook);
        break;
      case 'tail':
        createParametersSheet(workbook, 'tail');
        createTailAppearancesSheet(workbook);
        createActualNumbersSheet(workbook);
        break;
      case 'pattern':
        createParametersSheet(workbook, 'pattern');
        createPatternSheet(workbook);
        break;
    }

    // 生成檔案名稱
    const methodName = getMethodName(selectedMethod.value);
    const lottoName = getLottoTypeName(selectedLottoType.value);
    const dateStr = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const fileName = `${methodName}_${lottoName}_${dateStr}.xlsx`;

    // 下載檔案
    XLSX.writeFile(workbook, fileName);

    Notify.create({
      type: 'positive',
      message: '報表下載成功！'
    });
  } catch (error) {
    console.error('Excel生成失敗:', error);
    Notify.create({
      type: 'negative',
      message: 'Excel檔案生成失敗'
    });
  }
};

// 新的 ExcelJS 版本（支援背景色標記）
const downloadExcel = async () => {
  if (batchResults.value.length === 0) {
    Notify.create({
      type: 'warning',
      message: '沒有可下載的結果'
    });
    return;
  }

  try {
    // 創建工作簿
    const workbook = new ExcelJS.Workbook();
    workbook.creator = '樂透即時報';
    workbook.created = new Date();

    // 根據分析方法創建不同的工作表
    switch (selectedMethod.value) {
      case 'ball-follow':
        await createParametersSheetExcelJS(workbook, 'ball');
        await createPredictNumbersSheetExcelJS(workbook);
        await createNonAppearedByFrequencySheetExcelJS(workbook);
        await createNonAppearedBySizeSheetExcelJS(workbook);
        await createTailNumbersSheetExcelJS(workbook);
        await createActualNumbersSheetExcelJS(workbook);
        break;
      case 'tail':
        await createParametersSheetExcelJS(workbook, 'tail');
        await createTailAppearancesSheetExcelJS(workbook);
        await createActualNumbersSheetExcelJS(workbook);
        break;
      case 'pattern':
        await createParametersSheetExcelJS(workbook, 'pattern');
        await createPatternSheetExcelJS(workbook);
        break;
    }

    // 生成檔案名稱
    const methodName = getMethodName(selectedMethod.value);
    const lottoName = getLottoTypeName(selectedLottoType.value);
    const dateStr = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const fileName = `${methodName}_${lottoName}_${dateStr}.xlsx`;

    // 下載檔案
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);

    Notify.create({
      type: 'positive',
      message: '報表下載成功！'
    });
  } catch (error) {
    console.error('Excel生成失敗:', error);
    console.error('錯誤詳情:', error);

    // 如果 ExcelJS 失敗，回退到原有的 XLSX 版本
    Notify.create({
      type: 'warning',
      message: '增強版生成失敗，使用標準版本...'
    });
    downloadExcelOld();
  }
};

// ExcelJS 版本的工作表創建函數
// 創建參數工作表（ExcelJS版本）
const createParametersSheetExcelJS = async (workbook: ExcelJS.Workbook, type: 'ball' | 'tail' | 'pattern') => {
  const worksheet = workbook.addWorksheet('分析參數');

  const sheetData: (string | number)[][] = [];

  switch (type) {
    case 'ball':
      // 版路分析參數
      sheetData.push(['版路分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['拖牌區間', analysisParams.value.maxRange]);
      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 篩選條件
      sheetData.push(['篩選條件']);
      const ballAccuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveHits)?.label || filterParams.value.ballConsecutiveHits;
      const ballFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveFilterCondition)?.label || filterParams.value.ballConsecutiveFilterCondition;
      const ballStatisticLabel = statisticTypeOpts.value.find(opt => opt.value === filterParams.value.ballStatisticType)?.label || filterParams.value.ballStatisticType;
      const ballTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.ballTargetProbability)?.label || filterParams.value.ballTargetProbability;
      const ballProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballProbabilityFilterCondition)?.label || filterParams.value.ballProbabilityFilterCondition;
      sheetData.push(['連續拖出次數', ballAccuracyLabel]);
      sheetData.push(['篩選條件', ballFilterLabel]);
      sheetData.push(['統計方式', ballStatisticLabel]);
      sheetData.push(['準確率', ballTargetProbabilityLabel]);
      if (filterParams.value.ballTargetProbability !== 'all') {
        sheetData.push(['準確率篩選條件', ballProbabilityFilterLabel]);
      }
      break;

    case 'tail':
      // 尾數分析參數
      sheetData.push(['尾數分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['拖牌區間', analysisParams.value.maxRange]);
      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 篩選條件
      sheetData.push(['篩選條件']);
      const tailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveHits)?.label || filterParams.value.tailConsecutiveHits;
      const tailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveFilterCondition)?.label || filterParams.value.tailConsecutiveFilterCondition;
      const tailStatisticLabel = statisticTypeOpts.value.find(opt => opt.value === filterParams.value.tailStatisticType)?.label || filterParams.value.tailStatisticType;
      const tailTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.tailTargetProbability)?.label || filterParams.value.tailTargetProbability;
      const tailProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailProbabilityFilterCondition)?.label || filterParams.value.tailProbabilityFilterCondition;
      sheetData.push(['連續拖出次數', tailAccuracyLabel]);
      sheetData.push(['篩選條件', tailFilterLabel]);
      sheetData.push(['統計方式', tailStatisticLabel]);
      sheetData.push(['準確率', tailTargetProbabilityLabel]);
      if (filterParams.value.tailTargetProbability !== 'all') {
        sheetData.push(['準確率篩選條件', tailProbabilityFilterLabel]);
      }
      break;

    case 'pattern':
      // 綜合分析參數
      sheetData.push(['綜合分析參數']);
      sheetData.push([]);

      sheetData.push(['版路分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['拖牌區間', analysisParams.value.maxRange]);
      sheetData.push([]);

      sheetData.push(['尾數分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['拖牌區間', analysisParams.value.maxRange]);
      sheetData.push([]);

      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 版路分析篩選條件
      sheetData.push(['版路分析篩選條件']);
      const patternBallAccuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveHits)?.label || filterParams.value.ballConsecutiveHits;
      const patternBallFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveFilterCondition)?.label || filterParams.value.ballConsecutiveFilterCondition;
      const patternBallStatisticLabel = statisticTypeOpts.value.find(opt => opt.value === filterParams.value.ballStatisticType)?.label || filterParams.value.ballStatisticType;
      const patternBallTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.ballTargetProbability)?.label || filterParams.value.ballTargetProbability;
      const patternBallProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballProbabilityFilterCondition)?.label || filterParams.value.ballProbabilityFilterCondition;
      sheetData.push(['連續拖出次數', patternBallAccuracyLabel]);
      sheetData.push(['篩選條件', patternBallFilterLabel]);
      sheetData.push(['統計方式', patternBallStatisticLabel]);
      sheetData.push(['準確率', patternBallTargetProbabilityLabel]);
      if (filterParams.value.ballTargetProbability !== 'all') {
        sheetData.push(['準確率篩選條件', patternBallProbabilityFilterLabel]);
      }
      sheetData.push([]);

      // 尾數分析篩選條件
      sheetData.push(['尾數分析篩選條件']);
      const patternTailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveHits)?.label || filterParams.value.tailConsecutiveHits;
      const patternTailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveFilterCondition)?.label || filterParams.value.tailConsecutiveFilterCondition;
      const patternTailStatisticLabel = statisticTypeOpts.value.find(opt => opt.value === filterParams.value.tailStatisticType)?.label || filterParams.value.tailStatisticType;
      const patternTailTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.tailTargetProbability)?.label || filterParams.value.tailTargetProbability;
      const patternTailProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailProbabilityFilterCondition)?.label || filterParams.value.tailProbabilityFilterCondition;
      sheetData.push(['連續拖出次數', patternTailAccuracyLabel]);
      sheetData.push(['篩選條件', patternTailFilterLabel]);
      sheetData.push(['統計方式', patternTailStatisticLabel]);
      sheetData.push(['準確率', patternTailTargetProbabilityLabel]);
      if (filterParams.value.tailTargetProbability !== 'all') {
        sheetData.push(['準確率篩選條件', patternTailProbabilityFilterLabel]);
      }
      break;
  }

  sheetData.push([]);

  // 分析時間
  sheetData.push(['分析資訊']);
  sheetData.push(['分析時間', new Date().toLocaleString('zh-TW')]);
  sheetData.push(['分析期數', batchResults.value.length]);

  // 添加數據到工作表
  sheetData.forEach((row, ) => {
    const excelRow = worksheet.addRow(row);

    // 設置標題行樣式（16號字體）
    if (row.length === 1 && row[0] && typeof row[0] === 'string' &&
        (row[0].includes('參數') || row[0].includes('條件') || row[0].includes('資訊'))) {
      excelRow.font = { bold: true, size: 16 };
    } else {
      // 設置普通行字體為14號
      excelRow.font = { size: 14 };
    }
  });

  // 設置列寬
  worksheet.getColumn(1).width = 20;
  worksheet.getColumn(2).width = 30;
};

// 創建預測號碼統計工作表（ExcelJS版本）
const createPredictNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('預測號碼');

  // 添加標題
  worksheet.addRow(['預測號碼統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  // 獲取彩種的最大號碼數量，用於確定欄位數量
  const maxNumber = getDrawMaxNumber(selectedLottoType.value);

  // 初始化統計數據：每個欄位的號碼出現次數和準確預測次數
  const columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } } = {};

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 統計每個欄位的號碼數量（從第3欄開始，跳過日期欄位）
        row.forEach((number, colIndex) => {
          if (number && number !== '') {
            const actualColIndex = colIndex + 3; // 實際Excel欄位索引
            if (!columnStats[actualColIndex]) {
              columnStats[actualColIndex] = { totalCount: 0, accurateCount: 0 };
            }
            columnStats[actualColIndex].totalCount++;
          }
        });

        // 檢查並標記匹配的號碼（使用背景色）
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              // 統計準確預測次數
              if (!columnStats[cellIndex]) {
                columnStats[cellIndex] = { totalCount: 0, accurateCount: 0 };
              }
              columnStats[cellIndex].accurateCount++;

              // 設置紅色背景和白色字體，14號字體
              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 執行統計分析並輸出結果
  performPredictNumbersStatistics(columnStats, maxNumber);

  // 設置列寬（調整日期欄位寬度以適應14號字體）
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8; // 號碼欄
  }
};

// 創建其他必要的 ExcelJS 函數
const createNonAppearedByFrequencySheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('未出現號碼-預測次數');

  // 添加標題
  worksheet.addRow(['未出現號碼 - 依預測次數排序']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      // 依預測次數排序
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);
              // 設置單元格樣式 - ExcelJS 類型定義不完整，需要使用類型斷言
              const cellWithStyle = cell as {
                fill: {
                  type: string;
                  pattern: string;
                  fgColor: { argb: string };
                };
                font: {
                  color: { argb: string };
                  bold: boolean;
                  size: number;
                };
              };
              cellWithStyle.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFFF0000' } // 紅色背景
              };
              cellWithStyle.font = { color: { argb: 'FFFFFFFF' }, bold: true, size: 14 }; // 白色字體，14號
            }
          });
        }
      });
    }
  });

  // 設置列寬
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8; // 號碼欄
  }
};

const createNonAppearedBySizeSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('未出現號碼-大小排序');

  // 添加標題
  worksheet.addRow(['未出現號碼 - 依大小排序']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      // 依大小排序
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 設置列寬
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8; // 號碼欄
  }
};

const createTailNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('尾數統計');

  // 添加標題
  worksheet.addRow(['預測尾數統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按統計次數排序（次數高的在前）
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])  // 按次數降序排列
        .map(([tail]) => tail);       // 只取尾數值

      // 格式化尾數為單一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 設置列寬（調整日期欄位寬度以適應14號字體）
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6; // 尾數欄
  }
};

const createTailAppearancesSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('預測尾數');

  // 添加標題
  worksheet.addRow(['預測尾數統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  // 獲取彩種的最大號碼數量（雖然尾數分析不直接使用，但為了統計函數的一致性）
  const maxNumber = getDrawMaxNumber(selectedLottoType.value);

  // 初始化統計數據：每個欄位的尾數出現次數和準確預測次數（尾數只有0-9，最多10個欄位）
  const columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } } = {};

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 格式化尾數為單一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 統計每個欄位的尾數數量（從第3欄開始，跳過日期欄位）
        row.forEach((tail, colIndex) => {
          if (tail !== undefined && tail !== '') {
            const actualColIndex = colIndex + 3; // 實際Excel欄位索引
            if (!columnStats[actualColIndex]) {
              columnStats[actualColIndex] = { totalCount: 0, accurateCount: 0 };
            }
            columnStats[actualColIndex].totalCount++;
          }
        });

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              // 統計準確預測次數
              if (!columnStats[cellIndex]) {
                columnStats[cellIndex] = { totalCount: 0, accurateCount: 0 };
              }
              columnStats[cellIndex].accurateCount++;

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 執行尾數統計分析並輸出結果
  performTailNumbersStatistics(columnStats, maxNumber);

  // 設置列寬（調整日期欄位寬度以適應14號字體）
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6; // 尾數欄
  }
};

const createActualNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('實際開獎號碼');

  // 獲取預測參數
  const params = getAnalysisParameters();
  const aheadCount = params.aheadNum;

  worksheet.addRow([`實際開獎號碼 (預測${aheadCount}期後)`]);
  worksheet.addRow([]);

  // 檢查實際數據中是否有特別號/第二區
  const hasSpecialNumber = batchResults.value.some(result => {
    if (!result.actualNumbers || result.actualNumbers.length === 0) return false;

    // 根據彩種判斷是否應該有特別號/第二區
    if (selectedLottoType.value === 'super_lotto638') {
      return result.actualNumbers.length > 6; // 威力彩有第二區
    } else if (selectedLottoType.value === 'daily539' || selectedLottoType.value === 'ca_lotto') {
      return false; // 今彩539沒有特別號
    } else {
      // 大樂透和六合彩有特別號，檢查實際數據長度
      return result.actualNumbers.length > 6;
    }
  });

  // 創建表頭
  const headers = ['分析日期', '預測日期'];

  // 根據彩種決定號碼欄位數量
  let maxNumbers = 6; // 預設6個號碼
  switch (selectedLottoType.value) {
    case 'super_lotto638':
      maxNumbers = 6;
      break;
    case 'lotto649':
      maxNumbers = 6;
      break;
    case 'daily539':
      maxNumbers = 5;
      break;
    case 'lotto_hk':
      maxNumbers = 6;
      break;
    case 'ca_lotto':
      maxNumbers = 5;
      break;
  }

  // 添加一般號碼欄位
  for (let i = 1; i <= maxNumbers; i++) {
    headers.push(`號碼${i}`);
  }

  // 只有在實際有特別號/第二區的情況下才添加對應欄位
  if (hasSpecialNumber) {
    if (selectedLottoType.value === 'super_lotto638') {
      headers.push('第二區');
    } else {
      headers.push('特別號');
    }
  }

  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, size: 14 };

  // 設置標題樣式
  worksheet.getRow(1).font = { bold: true, size: 16 };

  // 添加數據行
  batchResults.value.forEach(result => {
    // 從predictResponse中獲取正確的預測日期
    let predictDate = '';
    let isDrawn = false;

    if (result.predictResponse) {
      if (result.predictResponse.draw_date) {
        predictDate = result.predictResponse.draw_date;
        // 檢查是否已開獎（有期號表示已開獎）
        isDrawn = !!result.predictResponse.period;
      } else {
        predictDate = '尚未開獎';
      }
    } else {
      predictDate = '尚未開獎';
    }

    const row: (string | number)[] = [result.date, predictDate];

    // 分離一般號碼和特別號/第二區
    let normalNumbers: number[] = [];
    let specialNumber: number | undefined;

    if (result.actualNumbers && result.actualNumbers.length > 0 && isDrawn) {
      if (selectedLottoType.value === 'super_lotto638') {
        // 威力彩：前6個是一般號碼，第7個是第二區
        if (result.actualNumbers.length > 6) {
          normalNumbers = result.actualNumbers.slice(0, 6).sort((a, b) => a - b);
          specialNumber = result.actualNumbers[6]; // 第二區號碼
        } else {
          normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
        }
      } else {
        // 其他彩種：最後一個是特別號
        if (result.actualNumbers.length > maxNumbers) {
          normalNumbers = result.actualNumbers.slice(0, -1).sort((a, b) => a - b);
          specialNumber = result.actualNumbers[result.actualNumbers.length - 1];
        } else {
          normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
        }
      }

      // 添加一般號碼
      normalNumbers.forEach(num => {
        row.push(num.toString().padStart(2, '0'));
      });

      // 填充剩餘的一般號碼欄位
      for (let i = normalNumbers.length; i < maxNumbers; i++) {
        row.push('');
      }

      // 添加特別號/第二區
      if (hasSpecialNumber) {
        if (specialNumber !== undefined) {
          row.push(specialNumber.toString().padStart(2, '0'));
        } else {
          row.push('');
        }
      }
    } else {
      // 填充空白或未開獎
      for (let i = 0; i < maxNumbers; i++) {
        if (!isDrawn && predictDate !== '尚未開獎') {
          row.push('尚未開獎');
        } else {
          row.push('');
        }
      }

      // 特別號/第二區欄位
      if (hasSpecialNumber) {
        if (!isDrawn && predictDate !== '尚未開獎') {
          row.push('尚未開獎');
        } else {
          row.push('');
        }
      }
    }

    const excelRow = worksheet.addRow(row);
    // 設置數據行字體大小為14號
    excelRow.font = { size: 14 };
  });

  // 設置列寬（調整日期欄位寬度以適應14號字體）
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 0; i < maxNumbers + (hasSpecialNumber ? 1 : 0); i++) {
    worksheet.getColumn(i + 3).width = 8; // 號碼欄
  }
};

const createPatternSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  // 根據原有的 createPatternSheet 實現，創建所有綜合分析的工作表

  // 創建綜合分析專用工作表（放在最前面）
  await createPatternRdTailAnalysisSheetExcelJS(workbook);
  await createPatternTailRdAnalysisSheetExcelJS(workbook);
  await createPatternComprehensiveAnalysisSheetExcelJS(workbook);
  await createPatternRdTailComparisonSheetExcelJS(workbook);
  await createPatternRdComprehensiveComparisonSheetExcelJS(workbook);
  await createPatternTailComprehensiveComparisonSheetExcelJS(workbook);

  // 創建版路分析子表（添加標記）
  await createPatternPredictNumbersSheetExcelJS(workbook);
  await createPatternNonAppearedByFrequencySheetExcelJS(workbook);
  await createPatternNonAppearedBySizeSheetExcelJS(workbook);
  await createPatternTailNumbersSheetExcelJS(workbook);

  // 創建尾數分析子表（添加標記）
  await createPatternTailAppearancesSheetExcelJS(workbook);

  // 創建實際開獎號碼工作表
  await createActualNumbersSheetExcelJS(workbook);
};

// 綜合分析專用工作表的 ExcelJS 函數
const createPatternRdTailAnalysisSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析尾數');

  worksheet.addRow(['版路分析尾數']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.rdTailAppearances) {
      const sortedTails = Array.from(result.rdTailAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3;
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternTailRdAnalysisSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('尾數分析尾數');

  worksheet.addRow(['尾數分析尾數']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailRdScoreMap) {
      const sortedTails = Array.from(result.tailRdScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternComprehensiveAnalysisSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('綜合分析尾數');

  worksheet.addRow(['綜合分析尾數']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailScoreMap) {
      const sortedTails = Array.from(result.tailScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

// 比對工作表的 ExcelJS 函數
const createPatternRdTailComparisonSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路+尾數比對');

  worksheet.addRow(['版路+尾數比對']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailMatchResults) {
      const rows = formatNumbersIntoRows(result.tailMatchResults, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternRdComprehensiveComparisonSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路+綜合比對');

  worksheet.addRow(['版路+綜合比對']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailMatchResults2) {
      const rows = formatNumbersIntoRows(result.tailMatchResults2, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternTailComprehensiveComparisonSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('尾數+綜合比對');

  worksheet.addRow(['尾數+綜合比對']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailMatchResults3) {
      const rows = formatNumbersIntoRows(result.tailMatchResults3, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

// 綜合分析的 ExcelJS 函數
const createPatternPredictNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析-預測號碼');

  worksheet.addRow(['版路分析-預測號碼統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  // 獲取彩種的最大號碼數量，用於確定欄位數量
  const maxNumber = getDrawMaxNumber(selectedLottoType.value);

  // 初始化統計數據：每個欄位的號碼出現次數和準確預測次數
  const columnStats: { [colIndex: number]: { totalCount: number; accurateCount: number } } = {};

  // 使用與 createPredictNumbersSheetExcelJS 相同的邏輯
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 統計每個欄位的號碼數量（從第3欄開始，跳過日期欄位）
        row.forEach((number, colIndex) => {
          if (number && number !== '') {
            const actualColIndex = colIndex + 3; // 實際Excel欄位索引
            if (!columnStats[actualColIndex]) {
              columnStats[actualColIndex] = { totalCount: 0, accurateCount: 0 };
            }
            columnStats[actualColIndex].totalCount++;
          }
        });

        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3;
              const cell = excelRow.getCell(cellIndex);

              // 統計準確預測次數
              if (!columnStats[cellIndex]) {
                columnStats[cellIndex] = { totalCount: 0, accurateCount: 0 };
              }
              columnStats[cellIndex].accurateCount++;

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 執行統計分析並輸出結果（綜合分析版本）
  performPatternPredictNumbersStatistics(columnStats, maxNumber);

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8;
  }
};

const createPatternNonAppearedByFrequencySheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析-未出現號碼-預測次數');

  worksheet.addRow(['版路分析-未出現號碼 - 依預測次數排序']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  // 簡化實現，使用基本邏輯
  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8;
  }
};

const createPatternNonAppearedBySizeSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析-未出現號碼-大小排序');

  worksheet.addRow(['版路分析-未出現號碼 - 依大小排序']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8;
  }
};

const createPatternTailNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析-尾數統計');

  worksheet.addRow(['版路分析-尾數統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3;
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternTailAppearancesSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('尾數分析-預測尾數');

  worksheet.addRow(['尾數分析-預測尾數統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailAnalysisAppearances) {
      const sortedTails = Array.from(result.tailAnalysisAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3;
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

// 創建預測號碼統計工作表
const createPredictNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['預測號碼統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]); // 空白日期，對齊號碼
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎號碼相符的預測號碼
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '預測號碼');
};

// 創建未出現號碼工作表 - 依預測次數排序
const createNonAppearedByFrequencySheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['未出現號碼 - 依預測次數排序']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      // 按預測次數排序未出現號碼
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式（理論上未出現號碼不會有匹配，但為了完整性添加）
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '未出現號碼-預測次數');
};

// 創建未出現號碼工作表 - 依大小排序
const createNonAppearedBySizeSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['未出現號碼 - 依大小排序']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      // 依大小排序
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式（理論上未出現號碼不會有匹配，但為了完整性添加）
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '未出現號碼-大小排序');
};

// 創建尾數統計工作表
const createTailNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['預測尾數統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按統計次數排序（次數高的在前）
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])  // 按次數降序排列
        .map(([tail]) => tail);       // 只取尾數值

      // 格式化尾數為單一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎尾數相符的預測尾數
  applyRedFontForMatches(worksheet, sheetData, 'tail');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數統計');
};

// 創建實際開獎號碼工作表
const createActualNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  // 獲取預測參數
  const params = getAnalysisParameters();
  const aheadCount = params.aheadNum;

  sheetData.push([`實際開獎號碼 (預測${aheadCount}期後)`]);
  sheetData.push([]);

  // 檢查實際數據中是否有特別號/第二區
  const hasSpecialNumber = batchResults.value.some(result => {
    if (!result.actualNumbers || result.actualNumbers.length === 0) return false;

    // 根據彩種判斷是否應該有特別號/第二區
    if (selectedLottoType.value === 'super_lotto638') {
      return result.actualNumbers.length > 6; // 威力彩有第二區
    } else if (selectedLottoType.value === 'daily539' || selectedLottoType.value === 'ca_lotto') {
      return false; // 今彩539沒有特別號
    } else {
      // 大樂透和六合彩有特別號，檢查實際數據長度
      return result.actualNumbers.length > 6;
    }
  });

  // 創建表頭
  const headers = ['分析日期', '預測日期'];

  // 根據彩種決定號碼欄位數量
  let maxNumbers = 6; // 預設6個號碼
  switch (selectedLottoType.value) {
    case 'super_lotto638':
      maxNumbers = 6;
      break;
    case 'lotto649':
      maxNumbers = 6;
      break;
    case 'daily539':
      maxNumbers = 5;
      break;
    case 'lotto_hk':
      maxNumbers = 6;
      break;
    case 'ca_lotto':
      maxNumbers = 5;
      break;
  }

  // 添加一般號碼欄位
  for (let i = 1; i <= maxNumbers; i++) {
    headers.push(`號碼${i}`);
  }

  // 只有在實際有特別號/第二區的情況下才添加對應欄位
  if (hasSpecialNumber) {
    if (selectedLottoType.value === 'super_lotto638') {
      headers.push('第二區');
    } else {
      headers.push('特別號');
    }
  }

  sheetData.push(headers);

  // 添加數據行
  batchResults.value.forEach(result => {
    // 從predictResponse中獲取正確的預測日期
    let predictDate = '';
    let isDrawn = false;

    if (result.predictResponse) {
      if (result.predictResponse.draw_date) {
        predictDate = result.predictResponse.draw_date;
        // 檢查是否已開獎（有期號表示已開獎）
        isDrawn = !!result.predictResponse.period;
      } else {
        predictDate = '尚未開獎';
      }
    } else {
      predictDate = '尚未開獎';
    }

    const row: (string | number)[] = [result.date, predictDate];

    // 分離一般號碼和特別號/第二區
    let normalNumbers: number[] = [];
    let specialNumber: number | undefined;

    if (result.actualNumbers && result.actualNumbers.length > 0 && isDrawn) {
      if (selectedLottoType.value === 'super_lotto638') {
        // 威力彩：前6個是一般號碼，第7個是第二區
        if (result.actualNumbers.length > 6) {
          normalNumbers = result.actualNumbers.slice(0, 6).sort((a, b) => a - b);
          specialNumber = result.actualNumbers[6]; // 第二區號碼
        } else {
          normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
        }
      } else {
        // 其他彩種：最後一個是特別號
        if (result.actualNumbers.length > maxNumbers) {
          normalNumbers = result.actualNumbers.slice(0, -1).sort((a, b) => a - b);
          specialNumber = result.actualNumbers[result.actualNumbers.length - 1];
        } else {
          normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
        }
      }
    }

    // 添加一般號碼
    for (let i = 0; i < maxNumbers; i++) {
      if (i < normalNumbers.length) {
        row.push(normalNumbers[i].toString().padStart(2, '0'));
      } else if (!isDrawn && predictDate !== '尚未開獎') {
        row.push('尚未開獎'); // 如果有預測日期但還沒開獎
      } else {
        row.push(''); // 如果完全沒有數據
      }
    }

    // 添加特別號
    if (hasSpecialNumber) {
      if (specialNumber !== undefined) {
        row.push(specialNumber.toString().padStart(2, '0'));
      } else if (!isDrawn && predictDate !== '尚未開獎') {
        row.push('尚未開獎'); // 如果有預測日期但還沒開獎
      } else {
        row.push(''); // 如果完全沒有數據
      }
    }

    sheetData.push(row);
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }  // 預測日期欄
  ];
  for (let i = 0; i < maxNumbers + (hasSpecialNumber ? 1 : 0); i++) {
    colWidths.push({ wch: 8 }); // 號碼欄
  }
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '實際開獎號碼');
};



// 創建尾數分析統計工作表（出現次數）
const createTailAppearancesSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['預測尾數統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 格式化尾數為單一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]); // 空白日期，對齊尾數
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎尾數相符的預測尾數
  applyRedFontForMatches(worksheet, sheetData, 'tail');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '預測尾數');
};

// 創建版路分析尾數工作表
const createPatternRdTailAnalysisSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析尾數']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.rdTailAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.rdTailAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 格式化尾數為單一列
      const rows = formatNumbersIntoRows(sortedTails, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析尾數');
};

// 創建尾數分析尾數工作表
const createPatternTailRdAnalysisSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['尾數分析尾數']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailRdScoreMap) {
      // 按分數排序尾數
      const sortedTails = Array.from(result.tailRdScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 格式化尾數為單一列
      const rows = formatNumbersIntoRows(sortedTails, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數分析尾數');
};

// 創建綜合分析尾數工作表
const createPatternComprehensiveAnalysisSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['綜合分析尾數']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailScoreMap) {
      // 按分數排序尾數
      const sortedTails = Array.from(result.tailScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 格式化尾數為單一列
      const rows = formatNumbersIntoRows(sortedTails, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '綜合分析尾數');
};

// 應用★標記給匹配的尾數（適用於只有一個日期欄位的工作表）
const applyTailStarMarks = (worksheet: XLSX.WorkSheet, sheetData: (string | number)[][]) => {
  // 遍歷數據行，從第4行開始（跳過標題行）
  for (let rowIndex = 3; rowIndex < sheetData.length; rowIndex++) {
    const row = sheetData[rowIndex];
    const dateCell = row[0];

    if (!dateCell || dateCell === '') continue;

    // 找到對應的批量分析結果
    const result = batchResults.value.find(r => r.date === dateCell);
    if (!result || !result.actualNumbers) continue;

    // 獲取實際開獎尾數
    const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];

    // 檢查每個尾數是否匹配（從第2欄開始，只有一個日期欄位）
    for (let colIndex = 1; colIndex < row.length; colIndex++) {
      const cellValue = row[colIndex];
      if (cellValue && cellValue !== '') {
        const tailNum = parseInt(cellValue.toString(), 10);
        if (actualTails.includes(tailNum)) {
          const markedValue = `${cellValue}★`;
          const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });

          if (!worksheet[cellRef]) {
            worksheet[cellRef] = { v: markedValue, t: 's' };
          } else {
            worksheet[cellRef].v = markedValue;
          }

          // 同時更新原始數據
          sheetData[rowIndex][colIndex] = markedValue;
        }
      }
    }
  }
};

// 應用★標記給匹配的尾數（適用於有兩個日期欄位的工作表）
const applyTailStarMarksWithTwoDates = (worksheet: XLSX.WorkSheet, sheetData: (string | number)[][]) => {
  // 遍歷數據行，從第4行開始（跳過標題行）
  for (let rowIndex = 3; rowIndex < sheetData.length; rowIndex++) {
    const row = sheetData[rowIndex];
    const dateCell = row[0];

    if (!dateCell || dateCell === '') continue;

    // 找到對應的批量分析結果
    const result = batchResults.value.find(r => r.date === dateCell);
    if (!result || !result.actualNumbers) continue;

    // 獲取實際開獎尾數
    const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];

    // 檢查每個尾數是否匹配（從第3欄開始，跳過分析日期和預測日期）
    for (let colIndex = 2; colIndex < row.length; colIndex++) {
      const cellValue = row[colIndex];
      if (cellValue && cellValue !== '') {
        const tailNum = parseInt(cellValue.toString(), 10);
        if (actualTails.includes(tailNum)) {
          const markedValue = `${cellValue}★`;
          const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });

          if (!worksheet[cellRef]) {
            worksheet[cellRef] = { v: markedValue, t: 's' };
          } else {
            worksheet[cellRef].v = markedValue;
          }

          // 同時更新原始數據
          sheetData[rowIndex][colIndex] = markedValue;
        }
      }
    }
  }
};

// 創建版路+尾數比對結果工作表
const createPatternRdTailComparisonSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路+尾數比對結果']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailMatchResults && result.tailMatchResults.length > 0) {
      // 格式化尾數為單一列
      const rows = formatNumbersIntoRows(result.tailMatchResults, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    } else {
      // 如果沒有比對結果，顯示空行
      sheetData.push([result.date]);
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路+尾數比對');
};

// 創建版路+綜合比對結果工作表
const createPatternRdComprehensiveComparisonSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路+綜合比對結果']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailMatchResults2 && result.tailMatchResults2.length > 0) {
      // 格式化尾數為單一列
      const rows = formatNumbersIntoRows(result.tailMatchResults2, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    } else {
      // 如果沒有比對結果，顯示空行
      sheetData.push([result.date]);
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路+綜合比對');
};

// 創建尾數+綜合比對結果工作表
const createPatternTailComprehensiveComparisonSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['尾數+綜合比對結果']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailMatchResults3 && result.tailMatchResults3.length > 0) {
      // 格式化尾數為單一列
      const rows = formatNumbersIntoRows(result.tailMatchResults3, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    } else {
      // 如果沒有比對結果，顯示空行
      sheetData.push([result.date]);
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數+綜合比對');
};

// 創建帶標記的版路分析預測號碼工作表
const createPatternPredictNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析-預測號碼統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]); // 空白日期，對齊號碼
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎號碼相符的預測號碼
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析-預測號碼');
};

// 創建帶標記的版路分析未出現號碼工作表 - 依預測次數排序
const createPatternNonAppearedByFrequencySheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析-未出現號碼 - 依預測次數排序']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      // 依預測次數排序
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式（理論上未出現號碼不會有匹配，但為了完整性添加）
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析-未出現號碼-預測次數');
};

// 創建帶標記的版路分析未出現號碼工作表 - 依大小排序
const createPatternNonAppearedBySizeSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析-未出現號碼 - 依大小排序']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      // 依大小排序
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);

      // 格式化號碼為單一列
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析-未出現號碼-大小排序');
};

// 創建帶標記的版路分析尾數統計工作表
const createPatternTailNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析-尾數統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 格式化尾數為單一列
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarksWithTwoDates(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析-尾數統計');
};

// 創建帶標記的尾數分析預測尾數工作表
const createPatternTailAppearancesSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['尾數分析-預測尾數統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailAnalysisAppearances) {
      // 按出現次數排序尾數（使用尾數分析的專用統計數據）
      const sortedTails = Array.from(result.tailAnalysisAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 格式化尾數為單一列
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarksWithTwoDates(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數分析-預測尾數');
};



// 創建綜合分析工作表
const createPatternSheet = (workbook: XLSX.WorkBook) => {
  // 創建綜合分析專用工作表（放在最前面）
  createPatternRdTailAnalysisSheet(workbook);
  createPatternTailRdAnalysisSheet(workbook);
  createPatternComprehensiveAnalysisSheet(workbook);
  createPatternRdTailComparisonSheet(workbook);
  createPatternRdComprehensiveComparisonSheet(workbook);
  createPatternTailComprehensiveComparisonSheet(workbook);

  // 創建版路分析子表（添加標記）
  createPatternPredictNumbersSheet(workbook);
  createPatternNonAppearedByFrequencySheet(workbook);
  createPatternNonAppearedBySizeSheet(workbook);
  createPatternTailNumbersSheet(workbook);

  // 創建尾數分析子表（添加標記）
  createPatternTailAppearancesSheet(workbook);

  // 創建實際開獎號碼工作表
  createActualNumbersSheet(workbook);
};

// 圖片下載功能
const downloadImage = async () => {
  if (batchResults.value.length === 0) {
    Notify.create({
      type: 'warning',
      message: '沒有可下載的結果'
    });
    return;
  }

  try {
    // 創建一個隱藏的 canvas 元素
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('無法創建 Canvas 上下文');
    }

    // 設置字體和樣式
    const fontSize = 14;
    const lineHeight = 20;
    const padding = 20;
    const headerFontSize = 16;
    const headerLineHeight = 24;

    // 計算所需的畫布尺寸
    const { width, height, sheets } = calculateCanvasSize();

    canvas.width = width;
    canvas.height = height;

    // 設置背景色為白色
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);

    // 繪製所有工作表
    let currentY = padding;

    for (const sheet of sheets) {
      currentY = await drawSheet(ctx, sheet, padding, currentY, fontSize, lineHeight, headerFontSize, headerLineHeight);
      currentY += 40; // 工作表之間的間距
    }

    // 轉換為圖片並下載
    canvas.toBlob((blob) => {
      if (blob) {
        const methodName = getMethodName(selectedMethod.value);
        const lottoName = getLottoTypeName(selectedLottoType.value);
        const dateStr = new Date().toISOString().split('T')[0].replace(/-/g, '');
        const fileName = `${methodName}_${lottoName}_${dateStr}.png`;

        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.click();
        URL.revokeObjectURL(url);

        Notify.create({
          type: 'positive',
          message: '圖片下載成功！'
        });
      }
    }, 'image/png');

  } catch (error) {
    console.error('圖片生成失敗:', error);
    Notify.create({
      type: 'negative',
      message: '圖片檔案生成失敗'
    });
  }
};

// 計算畫布尺寸和準備工作表數據
const calculateCanvasSize = () => {
  const sheets: SheetData[] = [];
  let maxWidth = 800;
  let totalHeight = 40; // 初始 padding

  // 根據分析方法準備工作表數據
  switch (selectedMethod.value) {
    case 'ball-follow':
      sheets.push(
        createSheetData('分析參數', createParametersData('ball')),
        createSheetData('預測號碼', createPredictNumbersData()),
        createSheetData('未出現號碼-預測次數', createNonAppearedByFrequencyData()),
        createSheetData('未出現號碼-大小排序', createNonAppearedBySizeData()),
        createSheetData('尾數統計', createTailNumbersData()),
        createSheetData('實際開獎號碼', createActualNumbersData())
      );
      break;
    case 'tail':
      sheets.push(
        createSheetData('分析參數', createParametersData('tail')),
        createSheetData('預測尾數', createTailAppearancesData()),
        createSheetData('實際開獎號碼', createActualNumbersData())
      );
      break;
    case 'pattern':
      sheets.push(
        createSheetData('分析參數', createParametersData('pattern')),
        createSheetData('版路分析尾數', createPatternRdTailAnalysisData()),
        createSheetData('尾數分析尾數', createPatternTailRdAnalysisData()),
        createSheetData('綜合分析尾數', createPatternComprehensiveAnalysisData()),
        createSheetData('版路+尾數比對', createPatternRdTailComparisonData()),
        createSheetData('版路+綜合比對', createPatternRdComprehensiveComparisonData()),
        createSheetData('尾數+綜合比對', createPatternTailComprehensiveComparisonData()),
        createSheetData('版路分析-預測號碼', createPredictNumbersData()),
        createSheetData('版路分析-未出現號碼-預測次數', createNonAppearedByFrequencyData()),
        createSheetData('版路分析-未出現號碼-大小排序', createNonAppearedBySizeData()),
        createSheetData('版路分析-尾數統計', createTailNumbersData()),
        createSheetData('尾數分析-預測尾數', createTailAppearancesData()),
        createSheetData('實際開獎號碼', createActualNumbersData())
      );
      break;
  }

  // 計算每個工作表的尺寸
  for (const sheet of sheets) {
    const sheetHeight = (sheet.data.length * 20) + 60; // 標題 + 數據行 + 間距
    totalHeight += sheetHeight;

    // 計算最大寬度
    const maxRowWidth = Math.max(...sheet.data.map(row => row.length)) * 80 + 40;
    maxWidth = Math.max(maxWidth, maxRowWidth);
  }

  // 增加底部空間，確保最後一個工作表不會被切到
  totalHeight += 100; // 額外的底部空間

  return { width: maxWidth, height: totalHeight, sheets };
};

// 工作表數據類型
interface SheetData {
  title: string;
  data: (string | number)[][];
}

// 創建工作表數據
const createSheetData = (title: string, data: (string | number)[][]): SheetData => {
  return { title, data };
};

// 繪製工作表
const drawSheet = async (
  ctx: CanvasRenderingContext2D,
  sheet: SheetData,
  padding: number,
  startY: number,
  fontSize: number,
  lineHeight: number,
  headerFontSize: number,
  headerLineHeight: number
): Promise<number> => {
  let currentY = startY;

  // 繪製工作表標題
  ctx.fillStyle = '#000000';
  ctx.font = `bold ${headerFontSize}px Arial, "Microsoft JhengHei", sans-serif`;
  ctx.fillText(sheet.title, padding, currentY);
  currentY += headerLineHeight + 10;

  // 繪製數據
  ctx.font = `${fontSize}px Arial, "Microsoft JhengHei", sans-serif`;

  for (let rowIndex = 0; rowIndex < sheet.data.length; rowIndex++) {
    const row = sheet.data[rowIndex];
    let currentX = padding;

    for (let colIndex = 0; colIndex < row.length; colIndex++) {
      const cellValue = row[colIndex];
      if (cellValue !== undefined && cellValue !== '') {
        let displayValue = cellValue.toString();
        let textColor = '#000000';

        // 檢查是否為匹配的號碼（紅色標記）
        if (displayValue.includes('★')) {
          textColor = '#ff0000';
          displayValue = displayValue.replace('★', ''); // 移除星號，只保留紅色字體
        }

        // 檢查是否為標題行（第一行或包含特定關鍵字）
        if (rowIndex === 0 || (row.length === 1 && typeof cellValue === 'string' &&
            (cellValue.includes('參數') || cellValue.includes('條件') || cellValue.includes('資訊') ||
             cellValue.includes('統計') || cellValue.includes('分析') || cellValue.includes('比對')))) {
          ctx.fillStyle = '#000000';
          ctx.font = `bold ${fontSize}px Arial, "Microsoft JhengHei", sans-serif`;
        } else {
          ctx.fillStyle = textColor;
          ctx.font = `${fontSize}px Arial, "Microsoft JhengHei", sans-serif`;
        }

        ctx.fillText(displayValue, currentX, currentY);
      }

      // 計算下一欄的位置
      currentX += colIndex < 2 ? 120 : 60; // 日期欄位較寬，號碼欄位較窄
    }

    currentY += lineHeight;
  }

  return currentY;
};

// 創建參數數據
const createParametersData = (type: 'ball' | 'tail' | 'pattern'): (string | number)[][] => {
  const data: (string | number)[][] = [];

  switch (type) {
    case 'ball':
      data.push(['版路分析參數']);
      data.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      data.push(['推算期數', analysisParams.value.periodNum]);
      data.push(['拖牌區間', analysisParams.value.maxRange]);
      data.push(['預測期數', analysisParams.value.aheadNum]);
      data.push([]);
      data.push(['篩選條件']);
      const ballAccuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveHits)?.label || filterParams.value.ballConsecutiveHits;
      const ballFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveFilterCondition)?.label || filterParams.value.ballConsecutiveFilterCondition;
      const ballTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.ballTargetProbability)?.label || filterParams.value.ballTargetProbability;
      const ballProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballProbabilityFilterCondition)?.label || filterParams.value.ballProbabilityFilterCondition;
      data.push(['連續拖出次數', ballAccuracyLabel]);
      data.push(['篩選條件', ballFilterLabel]);
      data.push(['準確率', ballTargetProbabilityLabel]);
      if (filterParams.value.ballTargetProbability !== 'all') {
        data.push(['準確率篩選條件', ballProbabilityFilterLabel]);
      }
      break;

    case 'tail':
      data.push(['尾數分析參數']);
      data.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      data.push(['推算期數', analysisParams.value.periodNum]);
      data.push(['拖牌區間', analysisParams.value.maxRange]);
      data.push(['預測期數', analysisParams.value.aheadNum]);
      data.push([]);
      data.push(['篩選條件']);
      const tailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveHits)?.label || filterParams.value.tailConsecutiveHits;
      const tailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveFilterCondition)?.label || filterParams.value.tailConsecutiveFilterCondition;
      const tailTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.tailTargetProbability)?.label || filterParams.value.tailTargetProbability;
      const tailProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailProbabilityFilterCondition)?.label || filterParams.value.tailProbabilityFilterCondition;
      data.push(['連續拖出次數', tailAccuracyLabel]);
      data.push(['篩選條件', tailFilterLabel]);
      data.push(['準確率', tailTargetProbabilityLabel]);
      if (filterParams.value.tailTargetProbability !== 'all') {
        data.push(['準確率篩選條件', tailProbabilityFilterLabel]);
      }
      break;

    case 'pattern':
      data.push(['綜合分析參數']);
      data.push([]);
      data.push(['版路分析參數']);
      data.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      data.push(['推算期數', analysisParams.value.periodNum]);
      data.push(['拖牌區間', analysisParams.value.maxRange]);
      data.push([]);
      data.push(['尾數分析參數']);
      data.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      data.push(['推算期數', analysisParams.value.periodNum]);
      data.push(['拖牌區間', analysisParams.value.maxRange]);
      data.push([]);
      data.push(['預測期數', analysisParams.value.aheadNum]);
      data.push([]);
      data.push(['版路分析篩選條件']);
      const patternBallAccuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveHits)?.label || filterParams.value.ballConsecutiveHits;
      const patternBallFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballConsecutiveFilterCondition)?.label || filterParams.value.ballConsecutiveFilterCondition;
      const patternBallTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.ballTargetProbability)?.label || filterParams.value.ballTargetProbability;
      const patternBallProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballProbabilityFilterCondition)?.label || filterParams.value.ballProbabilityFilterCondition;
      data.push(['連續拖出次數', patternBallAccuracyLabel]);
      data.push(['篩選條件', patternBallFilterLabel]);
      data.push(['準確率', patternBallTargetProbabilityLabel]);
      if (filterParams.value.ballTargetProbability !== 'all') {
        data.push(['準確率篩選條件', patternBallProbabilityFilterLabel]);
      }
      data.push([]);
      data.push(['尾數分析篩選條件']);
      const patternTailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveHits)?.label || filterParams.value.tailConsecutiveHits;
      const patternTailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailConsecutiveFilterCondition)?.label || filterParams.value.tailConsecutiveFilterCondition;
      const patternTailTargetProbabilityLabel = targetProbabilityOpts.value.find(opt => opt.value === filterParams.value.tailTargetProbability)?.label || filterParams.value.tailTargetProbability;
      const patternTailProbabilityFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailProbabilityFilterCondition)?.label || filterParams.value.tailProbabilityFilterCondition;
      data.push(['連續拖出次數', patternTailAccuracyLabel]);
      data.push(['篩選條件', patternTailFilterLabel]);
      data.push(['準確率', patternTailTargetProbabilityLabel]);
      if (filterParams.value.tailTargetProbability !== 'all') {
        data.push(['準確率篩選條件', patternTailProbabilityFilterLabel]);
      }
      break;
  }

  data.push([]);
  data.push(['分析資訊']);
  data.push(['分析時間', new Date().toLocaleString('zh-TW')]);
  data.push(['分析期數', batchResults.value.length]);

  return data;
};

// 創建預測號碼數據
const createPredictNumbersData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['預測號碼統計']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell && result.actualNumbers && result.actualNumbers.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    }
  });

  return data;
};

// 創建未出現號碼數據（依預測次數排序）
const createNonAppearedByFrequencyData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['未出現號碼 - 依預測次數排序']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell && result.actualNumbers && result.actualNumbers.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    }
  });

  return data;
};

// 創建未出現號碼數據（依大小排序）
const createNonAppearedBySizeData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['未出現號碼 - 依大小排序']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell && result.actualNumbers && result.actualNumbers.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    }
  });

  return data;
};

// 創建尾數統計數據
const createTailNumbersData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['預測尾數統計']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell !== undefined && actualTails.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    }
  });

  return data;
};

// 創建實際開獎號碼數據
const createActualNumbersData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  const params = getAnalysisParameters();
  const aheadCount = params.aheadNum;

  data.push([`實際開獎號碼 (預測${aheadCount}期後)`]);
  data.push([]);

  // 檢查實際數據中是否有特別號/第二區
  const hasSpecialNumber = batchResults.value.some(result => {
    if (!result.actualNumbers || result.actualNumbers.length === 0) return false;
    if (selectedLottoType.value === 'super_lotto638') {
      return result.actualNumbers.length > 6; // 威力彩有第二區
    } else if (selectedLottoType.value === 'daily539' || selectedLottoType.value === 'ca_lotto') {
      return false; // 今彩539沒有特別號
    } else {
      return result.actualNumbers.length > 6;
    }
  });

  // 創建表頭
  const headers = ['分析日期', '預測日期'];
  let maxNumbers = 6;
  if (selectedLottoType.value === 'daily539' || selectedLottoType.value === 'ca_lotto') {
    maxNumbers = 5;
  }

  for (let i = 1; i <= maxNumbers; i++) {
    headers.push(`號碼${i}`);
  }

  if (hasSpecialNumber) {
    if (selectedLottoType.value === 'super_lotto638') {
      headers.push('第二區');
    } else {
      headers.push('特別號');
    }
  }

  data.push(headers);

  // 添加數據行
  batchResults.value.forEach(result => {
    let predictDate = '';
    let isDrawn = false;

    if (result.predictResponse) {
      if (result.predictResponse.draw_date) {
        predictDate = result.predictResponse.draw_date;
        isDrawn = !!result.predictResponse.period;
      } else {
        predictDate = '尚未開獎';
      }
    } else {
      predictDate = '尚未開獎';
    }

    const row: (string | number)[] = [result.date, predictDate];

    // 分離一般號碼和特別號/第二區
    let normalNumbers: number[] = [];
    let specialNumber: number | undefined;

    if (result.actualNumbers && result.actualNumbers.length > 0 && isDrawn) {
      if (selectedLottoType.value === 'super_lotto638') {
        // 威力彩：前6個是一般號碼，第7個是第二區
        if (result.actualNumbers.length > 6) {
          normalNumbers = result.actualNumbers.slice(0, 6).sort((a, b) => a - b);
          specialNumber = result.actualNumbers[6]; // 第二區號碼
        } else {
          normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
        }
      } else {
        // 其他彩種：最後一個是特別號
        if (result.actualNumbers.length > maxNumbers) {
          normalNumbers = result.actualNumbers.slice(0, -1).sort((a, b) => a - b);
          specialNumber = result.actualNumbers[result.actualNumbers.length - 1];
        } else {
          normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
        }
      }

      // 添加一般號碼
      normalNumbers.forEach(num => {
        row.push(num.toString().padStart(2, '0'));
      });

      // 填充剩餘的一般號碼欄位
      for (let i = normalNumbers.length; i < maxNumbers; i++) {
        row.push('');
      }

      // 添加特別號/第二區
      if (hasSpecialNumber) {
        if (specialNumber !== undefined) {
          row.push(specialNumber.toString().padStart(2, '0'));
        } else {
          row.push('');
        }
      }
    } else {
      // 填充空白或未開獎
      for (let i = 0; i < maxNumbers; i++) {
        if (!isDrawn && predictDate !== '尚未開獎') {
          row.push('尚未開獎');
        } else {
          row.push('');
        }
      }

      // 特別號/第二區欄位
      if (hasSpecialNumber) {
        if (!isDrawn && predictDate !== '尚未開獎') {
          row.push('尚未開獎');
        } else {
          row.push('');
        }
      }
    }

    data.push(row);
  });

  return data;
};

// 創建尾數分析數據
const createTailAppearancesData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['預測尾數統計']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell !== undefined && actualTails.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    }
  });

  return data;
};

// 創建綜合分析相關數據函數
const createPatternRdTailAnalysisData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['版路分析尾數']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.rdTailAppearances) {
      const sortedTails = Array.from(result.rdTailAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell !== undefined && actualTails.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    }
  });

  return data;
};

const createPatternTailRdAnalysisData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['尾數分析尾數']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailRdScoreMap) {
      const sortedTails = Array.from(result.tailRdScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell !== undefined && actualTails.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    }
  });

  return data;
};

const createPatternComprehensiveAnalysisData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['綜合分析尾數']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailScoreMap) {
      const sortedTails = Array.from(result.tailScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell !== undefined && actualTails.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    }
  });

  return data;
};

const createPatternRdTailComparisonData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['版路+尾數比對']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailMatchResults && result.tailMatchResults.length > 0) {
      const rows = formatNumbersIntoRows(result.tailMatchResults, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell !== undefined && actualTails.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    } else {
      data.push([result.date, getPredictDate(result)]);
    }
  });

  return data;
};

const createPatternRdComprehensiveComparisonData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['版路+綜合比對']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailMatchResults2 && result.tailMatchResults2.length > 0) {
      const rows = formatNumbersIntoRows(result.tailMatchResults2, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell !== undefined && actualTails.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    } else {
      data.push([result.date, getPredictDate(result)]);
    }
  });

  return data;
};

const createPatternTailComprehensiveComparisonData = (): (string | number)[][] => {
  const data: (string | number)[][] = [];

  data.push(['尾數+綜合比對']);
  data.push([]);
  data.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailMatchResults3 && result.tailMatchResults3.length > 0) {
      const rows = formatNumbersIntoRows(result.tailMatchResults3, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          const markedRow = rowData.map((cell, cellIndex) => {
            if (cellIndex >= 2 && cell !== undefined && actualTails.includes(parseInt(cell.toString()))) {
              return `${cell}★`;
            }
            return cell;
          });
          data.push(markedRow);
        } else {
          data.push(rowData);
        }
      });
    } else {
      data.push([result.date, getPredictDate(result)]);
    }
  });

  return data;
};

// 初始化
onMounted(() => {
  // 設定預設參考日期為今天
  referenceDate.value = new Date().toISOString().split('T')[0];
});
</script>

<style lang="scss" scoped>
.q-card {
  max-width: 1200px;
  margin: 0 auto;
}

.q-option-group {
  .q-radio {
    margin-right: 2rem;
  }
}
</style>

