package notification

import (
	"fmt"
	"net/smtp"
	"strings"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"

	"lottery/ball_follow_scheduler/config"
)

// NotificationLevel 通知級別
type NotificationLevel string

const (
	NotificationLevelInfo     NotificationLevel = "info"
	NotificationLevelWarning  NotificationLevel = "warning"
	NotificationLevelError    NotificationLevel = "error"
	NotificationLevelCritical NotificationLevel = "critical"
)

// Notification 通知訊息
type Notification struct {
	Level     NotificationLevel `json:"level"`
	Title     string            `json:"title"`
	Message   string            `json:"message"`
	Timestamp time.Time         `json:"timestamp"`
	Source    string            `json:"source"`
}

// Notifier 通知器
type Notifier struct {
	config config.NotificationConfig
	
	// 通知歷史
	history []Notification
	mu      sync.RWMutex
	
	// 限流控制
	lastNotificationTime map[string]time.Time
	rateLimitMu          sync.RWMutex
}

// NewNotifier 建立新的通知器
func NewNotifier(cfg config.NotificationConfig) *Notifier {
	return &Notifier{
		config:               cfg,
		history:              make([]Notification, 0),
		lastNotificationTime: make(map[string]time.Time),
	}
}

// SendNotification 發送通知
func (n *Notifier) SendNotification(level NotificationLevel, title, message string) {
	if !n.config.Enabled {
		return
	}
	
	// 檢查是否需要發送此級別的通知
	if !n.shouldSendNotification(level) {
		return
	}
	
	// 檢查限流
	if n.isRateLimited(title) {
		log.Debugf("Notification rate limited: %s", title)
		return
	}
	
	notification := Notification{
		Level:     level,
		Title:     title,
		Message:   message,
		Timestamp: time.Now(),
		Source:    "Ball Follow Scheduler",
	}
	
	// 記錄到歷史
	n.addToHistory(notification)
	
	// 發送通知
	go n.sendNotificationAsync(notification)
}

// sendNotificationAsync 異步發送通知
func (n *Notifier) sendNotificationAsync(notification Notification) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic in sendNotificationAsync: %v", r)
		}
	}()
	
	// 記錄到日誌
	n.logNotification(notification)
	
	// 發送郵件通知
	if err := n.sendEmailNotification(notification); err != nil {
		log.Errorf("Failed to send email notification: %v", err)
	}
	
	// 更新限流時間
	n.updateRateLimit(notification.Title)
}

// sendEmailNotification 發送郵件通知
func (n *Notifier) sendEmailNotification(notification Notification) error {
	if n.config.Email.SMTPHost == "" || len(n.config.Recipients) == 0 {
		return fmt.Errorf("email configuration incomplete")
	}
	
	// 建立郵件內容
	subject := fmt.Sprintf("[%s] %s", strings.ToUpper(string(notification.Level)), notification.Title)
	body := n.buildEmailBody(notification)
	
	// 建立郵件訊息
	message := n.buildEmailMessage(subject, body)
	
	// 設定SMTP認證
	auth := smtp.PlainAuth("", n.config.Email.Username, n.config.Email.Password, n.config.Email.SMTPHost)
	
	// 發送郵件
	addr := fmt.Sprintf("%s:%d", n.config.Email.SMTPHost, n.config.Email.SMTPPort)
	err := smtp.SendMail(addr, auth, n.config.Email.FromAddress, n.config.Recipients, []byte(message))
	
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}
	
	log.Infof("Email notification sent: %s", notification.Title)
	return nil
}

// buildEmailBody 建立郵件內容
func (n *Notifier) buildEmailBody(notification Notification) string {
	return fmt.Sprintf(`
版路分析排程服務通知

級別: %s
標題: %s
時間: %s
來源: %s

訊息:
%s

---
此郵件由版路分析排程服務自動發送
`,
		strings.ToUpper(string(notification.Level)),
		notification.Title,
		notification.Timestamp.Format("2006-01-02 15:04:05"),
		notification.Source,
		notification.Message,
	)
}

// buildEmailMessage 建立郵件訊息
func (n *Notifier) buildEmailMessage(subject, body string) string {
	headers := make(map[string]string)
	headers["From"] = fmt.Sprintf("%s <%s>", n.config.Email.FromName, n.config.Email.FromAddress)
	headers["To"] = strings.Join(n.config.Recipients, ",")
	headers["Subject"] = subject
	headers["MIME-Version"] = "1.0"
	headers["Content-Type"] = "text/plain; charset=UTF-8"
	headers["Date"] = time.Now().Format(time.RFC1123Z)
	
	message := ""
	for k, v := range headers {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body
	
	return message
}

// shouldSendNotification 檢查是否應該發送通知
func (n *Notifier) shouldSendNotification(level NotificationLevel) bool {
	for _, allowedLevel := range n.config.NotificationLevels {
		if string(level) == allowedLevel {
			return true
		}
	}
	return false
}

// isRateLimited 檢查是否被限流
func (n *Notifier) isRateLimited(title string) bool {
	n.rateLimitMu.RLock()
	defer n.rateLimitMu.RUnlock()
	
	lastTime, exists := n.lastNotificationTime[title]
	if !exists {
		return false
	}
	
	// 同樣的通知在5分鐘內只發送一次
	return time.Since(lastTime) < 5*time.Minute
}

// updateRateLimit 更新限流時間
func (n *Notifier) updateRateLimit(title string) {
	n.rateLimitMu.Lock()
	defer n.rateLimitMu.Unlock()
	
	n.lastNotificationTime[title] = time.Now()
}

// addToHistory 添加到通知歷史
func (n *Notifier) addToHistory(notification Notification) {
	n.mu.Lock()
	defer n.mu.Unlock()
	
	n.history = append(n.history, notification)
	
	// 保持歷史記錄在合理範圍內（最多1000條）
	if len(n.history) > 1000 {
		n.history = n.history[len(n.history)-1000:]
	}
}

// logNotification 記錄通知到日誌
func (n *Notifier) logNotification(notification Notification) {
	switch notification.Level {
	case NotificationLevelInfo:
		log.Infof("NOTIFICATION [%s]: %s", notification.Title, notification.Message)
	case NotificationLevelWarning:
		log.Warnf("NOTIFICATION [%s]: %s", notification.Title, notification.Message)
	case NotificationLevelError:
		log.Errorf("NOTIFICATION [%s]: %s", notification.Title, notification.Message)
	case NotificationLevelCritical:
		log.Errorf("CRITICAL NOTIFICATION [%s]: %s", notification.Title, notification.Message)
	default:
		log.Infof("NOTIFICATION [%s]: %s", notification.Title, notification.Message)
	}
}

// GetHistory 取得通知歷史
func (n *Notifier) GetHistory(limit int) []Notification {
	n.mu.RLock()
	defer n.mu.RUnlock()
	
	if limit <= 0 || limit > len(n.history) {
		// 返回副本
		history := make([]Notification, len(n.history))
		copy(history, n.history)
		return history
	}
	
	// 返回最近的N條記錄
	start := len(n.history) - limit
	history := make([]Notification, limit)
	copy(history, n.history[start:])
	return history
}

// GetHistoryByLevel 根據級別取得通知歷史
func (n *Notifier) GetHistoryByLevel(level NotificationLevel, limit int) []Notification {
	n.mu.RLock()
	defer n.mu.RUnlock()
	
	var filtered []Notification
	for _, notification := range n.history {
		if notification.Level == level {
			filtered = append(filtered, notification)
		}
	}
	
	if limit <= 0 || limit > len(filtered) {
		return filtered
	}
	
	// 返回最近的N條記錄
	start := len(filtered) - limit
	return filtered[start:]
}

// ClearHistory 清除通知歷史
func (n *Notifier) ClearHistory() {
	n.mu.Lock()
	defer n.mu.Unlock()
	
	n.history = make([]Notification, 0)
}

// GetStats 取得通知統計資訊
func (n *Notifier) GetStats() map[string]interface{} {
	n.mu.RLock()
	defer n.mu.RUnlock()
	
	stats := map[string]interface{}{
		"total_notifications": len(n.history),
		"by_level":           make(map[string]int),
	}
	
	levelCounts := make(map[string]int)
	for _, notification := range n.history {
		levelCounts[string(notification.Level)]++
	}
	
	stats["by_level"] = levelCounts
	
	// 最近24小時的通知數量
	recent := 0
	cutoff := time.Now().Add(-24 * time.Hour)
	for _, notification := range n.history {
		if notification.Timestamp.After(cutoff) {
			recent++
		}
	}
	stats["recent_24h"] = recent
	
	return stats
}

// TestEmailConfiguration 測試郵件配置
func (n *Notifier) TestEmailConfiguration() error {
	if n.config.Email.SMTPHost == "" {
		return fmt.Errorf("SMTP host not configured")
	}
	
	if len(n.config.Recipients) == 0 {
		return fmt.Errorf("no recipients configured")
	}
	
	// 發送測試郵件
	testNotification := Notification{
		Level:     NotificationLevelInfo,
		Title:     "Test Notification",
		Message:   "This is a test notification to verify email configuration.",
		Timestamp: time.Now(),
		Source:    "Ball Follow Scheduler Test",
	}
	
	return n.sendEmailNotification(testNotification)
}

// SendSystemStartupNotification 發送系統啟動通知
func (n *Notifier) SendSystemStartupNotification() {
	n.SendNotification(NotificationLevelInfo,
		"System Started",
		"Ball Follow Scheduler has started successfully.")
}

// SendSystemShutdownNotification 發送系統關閉通知
func (n *Notifier) SendSystemShutdownNotification() {
	n.SendNotification(NotificationLevelInfo,
		"System Shutdown",
		"Ball Follow Scheduler is shutting down.")
}

// SendTaskCompletedNotification 發送任務完成通知
func (n *Notifier) SendTaskCompletedNotification(lottoType string, period int, duration time.Duration) {
	message := fmt.Sprintf("Task for %s period %d completed successfully in %v", lottoType, period, duration)
	n.SendNotification(NotificationLevelInfo, "Task Completed", message)
}

// SendTaskFailedNotification 發送任務失敗通知
func (n *Notifier) SendTaskFailedNotification(lottoType string, period int, err error) {
	message := fmt.Sprintf("Task for %s period %d failed: %v", lottoType, period, err)
	n.SendNotification(NotificationLevelError, "Task Failed", message)
}

// SendMemoryWarningNotification 發送記憶體警告通知
func (n *Notifier) SendMemoryWarningNotification(usage int64, percent int) {
	message := fmt.Sprintf("Memory usage is high: %dMB (%d%%)", usage, percent)
	n.SendNotification(NotificationLevelWarning, "High Memory Usage", message)
}
