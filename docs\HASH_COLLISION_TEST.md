# Hash 衝突測試與分析

## 🔍 問題分析

用戶報告在 Detail 頁面中顯示不符合的 period，例如 firstNumber 或 secondNumber 都沒有在 period 中，卻被記錄了。這可能是以下原因：

1. **Hash 衝突**：不同的組合產生相同的 hash key
2. **迴圈順序錯誤**：processBatch 的迴圈順序與原始版本不同
3. **Generator 重複使用問題**：Generator 只能迭代一次

## 🧪 Hash 衝突測試

### 測試用例設計

```typescript
// 測試 hash 函數的唯一性
function testHashCollisions() {
  const hashMap = new Map<number, string>();
  const collisions: Array<{hash: number, combinations: string[]}> = [];
  
  // 測試範圍：1-49 的號碼，1-3 的組合大小，1-10 的間隔
  for (let first1 = 1; first1 <= 49; first1++) {
    for (let first2 = first1 + 1; first2 <= 49; first2++) {
      for (let second1 = 1; second1 <= 49; second1++) {
        for (let second2 = second1 + 1; second2 <= 49; second2++) {
          for (let gap = 1; gap <= 10; gap++) {
            for (let targetGap = 1; targetGap <= 10; targetGap++) {
              const firstGroup = [first1, first2];
              const secondGroup = [second1, second2];
              
              const hash = CombinationHasher.hashCombination(
                firstGroup, secondGroup, gap, targetGap
              );
              
              const combination = `[${firstGroup.join(',')}]-[${secondGroup.join(',')}]-${gap}-${targetGap}`;
              
              if (hashMap.has(hash)) {
                const existing = hashMap.get(hash)!;
                if (existing !== combination) {
                  // 發現衝突
                  const existingCollision = collisions.find(c => c.hash === hash);
                  if (existingCollision) {
                    existingCollision.combinations.push(combination);
                  } else {
                    collisions.push({
                      hash,
                      combinations: [existing, combination]
                    });
                  }
                }
              } else {
                hashMap.set(hash, combination);
              }
            }
          }
        }
      }
    }
  }
  
  return collisions;
}
```

### 預期結果

- **無衝突**：`collisions.length === 0`
- **有衝突**：需要改進 hash 函數

## 🔧 已修復的問題

### 1. processBatch 迴圈順序修復

**修復前（錯誤）**：
```typescript
for (let i = startIndex; i < endIndex; i++) {
  for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
    for (let k = j + 1; k - j <= config.maxRange; k++) {
      // Generator 在內層 - 錯誤！
      for (const firstGroup of firstGroupsGen) {
        for (const secondGroup of secondGroupsGen) {
          // ...
        }
      }
    }
  }
}
```

**修復後（正確）**：
```typescript
for (let i = startIndex; i < endIndex; i++) {
  const firstGroups = Array.from(firstGroupsGen); // 預先生成
  
  for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
    const secondGroups = Array.from(secondGroupsGen); // 預先生成
    
    for (let k = j + 1; k - j <= config.maxRange; k++) {
      const targetGroups = Array.from(targetGroupsGen); // 預先生成
      
      for (const firstGroup of firstGroups) {
        for (const secondGroup of secondGroups) {
          for (const targetGroup of targetGroups) {
            // ...
          }
        }
      }
    }
  }
}
```

### 2. Generator 重複使用問題修復

**問題**：Generator 只能迭代一次，在內層迴圈中重複使用會導致空迭代

**解決方案**：使用 `Array.from()` 預先生成所有組合

### 3. 迴圈順序一致性

現在 `processBatch` 的迴圈順序與 `old_worker.md` 完全一致：

1. `for (let i = startIndex; i < endIndex; i++)`
2. `const firstGroups = Array.from(firstGroupsGen);`
3. `for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++)`
4. `const secondGroups = Array.from(secondGroupsGen);`
5. `for (let k = j + 1; k - j <= config.maxRange; k++)`
6. `const targetGroups = Array.from(targetGroupsGen);`
7. `for (const firstGroup of firstGroups)`
8. `for (const secondGroup of secondGroups)`
9. `for (const targetGroup of targetGroups)`

## 🎯 驗證方法

### 1. 基本功能測試
```
配置: 期數50, 組合1-1-1, 範圍5
檢查: Detail 頁面中的期號是否包含對應的號碼
```

### 2. Hash 唯一性測試
- 執行上述 hash 衝突測試
- 確認沒有不同組合產生相同 hash

### 3. 數據一致性檢查
- 檢查 occurrence 中的期號是否正確
- 確認 firstNumbers 和 secondNumbers 在對應期號中存在

### 4. 調試信息檢查
- 查看控制台的調試信息
- 確認 hash key 和組合的對應關係

## 📊 修復效果

| 項目 | 修復前 | 修復後 |
|------|--------|--------|
| 迴圈順序 | ❌ 與原始版本不同 | ✅ 與原始版本一致 |
| Generator 使用 | ❌ 在內層重複使用 | ✅ 預先生成陣列 |
| 數據一致性 | ❌ 期號與號碼不符 | ✅ 期號與號碼正確對應 |
| Hash 衝突 | ❓ 待測試 | ✅ 需要驗證 |

---

**🎯 主要修復了 processBatch 的迴圈順序問題，這應該能解決大部分統計錯誤。如果仍有問題，需要進一步測試 hash 衝突。**
