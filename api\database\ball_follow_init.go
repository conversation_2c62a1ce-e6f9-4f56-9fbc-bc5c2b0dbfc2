package database

import (
	"fmt"
	"time"

	"gorm.io/gorm"

	. "lottery/models"
)

// InitBallFollowTables 初始化版路分析相關表格
func InitBallFollowTables(db *gorm.DB) error {
	// 自動遷移表格結構
	err := db.AutoMigrate(
		&BallFollowAnalysisResult{},
		&BallFollowCalculationTask{},
		&BallFollowSubtask{},
	)
	if err != nil {
		return fmt.Errorf("failed to migrate ball follow tables: %w", err)
	}

	return nil
}

// CreateSampleBallFollowData 建立範例資料（僅用於測試）
func CreateSampleBallFollowData(db *gorm.DB) error {
	// 建立範例計算任務
	task := &BallFollowCalculationTask{
		LottoType:             Daily539Str,
		Period:                113001,
		TaskStatus:            TaskStatusCompleted,
		TotalCombinations:     1350,
		CompletedCombinations: 1350,
		FailedCombinations:    0,
		StartTime:             timePtr(time.Now().Add(-2 * time.Hour)),
		EndTime:               timePtr(time.Now().Add(-1 * time.Hour)),
		EstimatedDuration:     uint32Ptr(7200), // 2小時
		ActualDuration:        uint32Ptr(3600), // 1小時
		Priority:              5,
		TriggeredBy:           TriggerTypeScheduler,
		ConfigSnapshot: BallFollowJSON{
			"max_concurrent_calculations": 3,
			"memory_threshold_percent":    80,
			"auto_gc_enabled":             true,
		},
		ProgressDetails: BallFollowJSON{
			"current_combination":      "completed",
			"estimated_remaining_time": 0,
			"average_calculation_time": 2.67,
		},
	}

	if err := db.Create(task).Error; err != nil {
		return fmt.Errorf("failed to create sample task: %w", err)
	}

	// 建立範例分析結果
	result := &BallFollowAnalysisResult{
		LottoType:       Daily539Str,
		Period:          113001,
		AnalysisDate:    time.Now(),
		Comb1:           1,
		Comb2:           1,
		Comb3:           1,
		PeriodNum:       120,
		MaxRange:        15,
		AnalysisPeriods: 50,
		PredictNumbers: BallFollowJSON{
			"numbers":           []int{1, 5, 12, 23, 35},
			"appearances":       map[string]int{"1": 15, "5": 12, "12": 10, "23": 8, "35": 6},
			"probabilities":     map[string]float64{"1": 0.3, "5": 0.24, "12": 0.2, "23": 0.16, "35": 0.12},
			"sorted_by_freq":    []int{1, 5, 12, 23, 35},
			"total_predictions": 50,
		},
		NonAppearedNumbers: BallFollowJSON{
			"numbers":        []int{2, 7, 18, 29, 39},
			"total_numbers":  5,
			"analysis_range": "1-39",
		},
		TailStatistics: BallFollowJSON{
			"tail_appearances":   map[string]int{"1": 15, "5": 12, "2": 10, "3": 8, "9": 6},
			"tail_probabilities": map[string]float64{"1": 0.29, "5": 0.23, "2": 0.19, "3": 0.15, "9": 0.12},
			"sorted_tails":       []int{1, 5, 2, 3, 9},
			"total_tails":        51,
		},
		TargetAppearances: BallFollowJSON{
			"number_appearances": map[string]int{"1": 15, "5": 12, "12": 10, "23": 8, "35": 6},
			"total_targets":      5,
			"max_appearances":    15,
			"min_appearances":    6,
		},
		CalculationDuration: uint32Ptr(3),
		DataSourcePeriods: BallFollowJSON{
			"start_period": 112951,
			"end_period":   113000,
			"total_periods": 50,
		},
	}

	// 自動生成計算雜湊值
	result.CalculationHash = result.GenerateCalculationHash()

	if err := db.Create(result).Error; err != nil {
		return fmt.Errorf("failed to create sample result: %w", err)
	}

	// 建立範例子任務
	subtask := &BallFollowSubtask{
		TaskID:           task.ID,
		Comb1:            1,
		Comb2:            1,
		Comb3:            1,
		PeriodNum:        120,
		MaxRange:         15,
		SubtaskStatus:    SubtaskStatusCompleted,
		StartTime:        timePtr(time.Now().Add(-2 * time.Hour)),
		EndTime:          timePtr(time.Now().Add(-2*time.Hour + 3*time.Second)),
		Duration:         uint32Ptr(3),
		ResultID:         &result.ID,
		MemoryUsageMB:    uint32Ptr(256),
		CalculationHash:  &result.CalculationHash,
		WorkerID:         stringPtr("worker-001"),
	}

	if err := db.Create(subtask).Error; err != nil {
		return fmt.Errorf("failed to create sample subtask: %w", err)
	}

	return nil
}

// ValidateBallFollowTables 驗證表格結構和約束
func ValidateBallFollowTables(db *gorm.DB) error {
	// 檢查表格是否存在
	tables := []string{
		"ball_follow_analysis_results",
		"ball_follow_calculation_tasks",
		"ball_follow_subtasks",
	}

	for _, table := range tables {
		if !db.Migrator().HasTable(table) {
			return fmt.Errorf("table %s does not exist", table)
		}
	}

	// 檢查索引是否存在
	indexes := map[string][]string{
		"ball_follow_analysis_results": {
			"idx_lotto_period",
			"idx_calculation_hash",
			"idx_analysis_date",
			"idx_parameters",
		},
		"ball_follow_calculation_tasks": {
			"idx_lotto_period",
			"idx_task_status",
			"idx_start_time",
		},
		"ball_follow_subtasks": {
			"idx_task_id",
			"idx_subtask_status",
			"idx_parameters",
		},
	}

	for table, indexList := range indexes {
		for _, index := range indexList {
			if !db.Migrator().HasIndex(table, index) {
				return fmt.Errorf("index %s does not exist on table %s", index, table)
			}
		}
	}

	return nil
}

// CleanupBallFollowTestData 清理測試資料
func CleanupBallFollowTestData(db *gorm.DB) error {
	// 按照外鍵依賴順序刪除
	if err := db.Where("1 = 1").Delete(&BallFollowSubtask{}).Error; err != nil {
		return fmt.Errorf("failed to cleanup subtasks: %w", err)
	}

	if err := db.Where("1 = 1").Delete(&BallFollowAnalysisResult{}).Error; err != nil {
		return fmt.Errorf("failed to cleanup results: %w", err)
	}

	if err := db.Where("1 = 1").Delete(&BallFollowCalculationTask{}).Error; err != nil {
		return fmt.Errorf("failed to cleanup tasks: %w", err)
	}

	return nil
}

// GetBallFollowTableStats 取得表格統計資訊
func GetBallFollowTableStats(db *gorm.DB) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 計算各表格的記錄數量
	var resultCount, taskCount, subtaskCount int64

	if err := db.Model(&BallFollowAnalysisResult{}).Count(&resultCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count results: %w", err)
	}

	if err := db.Model(&BallFollowCalculationTask{}).Count(&taskCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count tasks: %w", err)
	}

	if err := db.Model(&BallFollowSubtask{}).Count(&subtaskCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count subtasks: %w", err)
	}

	stats["analysis_results_count"] = resultCount
	stats["calculation_tasks_count"] = taskCount
	stats["subtasks_count"] = subtaskCount

	// 計算各狀態的任務數量
	var pendingTasks, runningTasks, completedTasks, failedTasks int64

	db.Model(&BallFollowCalculationTask{}).Where("task_status = ?", TaskStatusPending).Count(&pendingTasks)
	db.Model(&BallFollowCalculationTask{}).Where("task_status = ?", TaskStatusRunning).Count(&runningTasks)
	db.Model(&BallFollowCalculationTask{}).Where("task_status = ?", TaskStatusCompleted).Count(&completedTasks)
	db.Model(&BallFollowCalculationTask{}).Where("task_status = ?", TaskStatusFailed).Count(&failedTasks)

	stats["pending_tasks"] = pendingTasks
	stats["running_tasks"] = runningTasks
	stats["completed_tasks"] = completedTasks
	stats["failed_tasks"] = failedTasks

	// 計算平均執行時間
	var avgDuration float64
	db.Model(&BallFollowCalculationTask{}).
		Where("task_status = ? AND actual_duration IS NOT NULL", TaskStatusCompleted).
		Select("AVG(actual_duration)").Scan(&avgDuration)

	stats["average_task_duration_seconds"] = avgDuration

	return stats, nil
}

// 輔助函數
func timePtr(t time.Time) *time.Time {
	return &t
}

func uint32Ptr(i uint32) *uint32 {
	return &i
}

func stringPtr(s string) *string {
	return &s
}
