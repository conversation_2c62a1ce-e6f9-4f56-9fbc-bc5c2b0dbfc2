package engine

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"

	"lottery/ball_follow_scheduler/config"
	. "lottery/models"
)

// CalculationEngine 版路分析計算引擎
type CalculationEngine struct {
	config     config.CalculationConfig
	repository *BallFollowRepository

	// 計算狀態
	mu                 sync.RWMutex
	activeCalculations map[uint64]*CalculationContext

	// 統計資訊
	stats *EngineStats
}

// CalculationContext 計算上下文
type CalculationContext struct {
	TaskID      uint64
	SubtaskID   uint64
	StartTime   time.Time
	WorkerID    string
	Cancel      context.CancelFunc
	MemoryStart int64
}

// EngineStats 引擎統計資訊
type EngineStats struct {
	TotalCalculations      int64         `json:"total_calculations"`
	SuccessfulCalculations int64         `json:"successful_calculations"`
	FailedCalculations     int64         `json:"failed_calculations"`
	AverageCalculationTime time.Duration `json:"average_calculation_time"`
	PeakMemoryUsage        int64         `json:"peak_memory_usage"`
	ActiveCalculations     int           `json:"active_calculations"`
	mu                     sync.RWMutex
}

// CalculationResult 計算結果
type CalculationResult struct {
	Success       bool
	Result        *BallFollowAnalysisResult
	Error         error
	Duration      time.Duration
	MemoryUsageMB int64
	RetryCount    int
}

// NewCalculationEngine 建立新的計算引擎
func NewCalculationEngine(cfg config.CalculationConfig, repo *BallFollowRepository) *CalculationEngine {
	return &CalculationEngine{
		config:             cfg,
		repository:         repo,
		activeCalculations: make(map[uint64]*CalculationContext),
		stats:              &EngineStats{},
	}
}

// CalculateSubtask 執行子任務計算
func (e *CalculationEngine) CalculateSubtask(ctx context.Context, subtaskID uint64, workerID string) *CalculationResult {
	startTime := time.Now()

	// 取得子任務資訊
	subtask, err := e.getSubtaskInfo(subtaskID)
	if err != nil {
		return &CalculationResult{
			Success: false,
			Error:   fmt.Errorf("failed to get subtask info: %w", err),
		}
	}

	// 建立計算上下文
	calcCtx, cancel := context.WithTimeout(ctx, e.config.CalculationTimeout)
	defer cancel()

	calcContext := &CalculationContext{
		TaskID:      subtask.TaskID,
		SubtaskID:   subtaskID,
		StartTime:   startTime,
		WorkerID:    workerID,
		Cancel:      cancel,
		MemoryStart: e.getCurrentMemoryMB(),
	}

	// 註冊活躍計算
	e.registerCalculation(subtaskID, calcContext)
	defer e.unregisterCalculation(subtaskID)

	// 更新子任務狀態為執行中
	if err := e.updateSubtaskStatus(subtaskID, SubtaskStatusRunning, workerID); err != nil {
		log.Errorf("Failed to update subtask status to running: %v", err)
	}

	// 執行計算
	result := e.performCalculation(calcCtx, subtask)

	// 計算執行時間和記憶體使用
	result.Duration = time.Since(startTime)
	result.MemoryUsageMB = e.getCurrentMemoryMB() - calcContext.MemoryStart

	// 更新統計資訊
	e.updateStats(result)

	// 更新子任務狀態
	if result.Success {
		e.updateSubtaskCompleted(subtaskID, result)
	} else {
		e.updateSubtaskFailed(subtaskID, result)
	}

	return result
}

// performCalculation 執行實際的版路分析計算
func (e *CalculationEngine) performCalculation(ctx context.Context, subtask *BallFollowSubtask) *CalculationResult {
	// 取得主任務資訊
	task, err := e.repository.GetCalculationTask(subtask.TaskID)
	if err != nil {
		return &CalculationResult{
			Success: false,
			Error:   fmt.Errorf("failed to get task info: %w", err),
		}
	}

	// 檢查是否已有相同參數的計算結果
	calculationHash := subtask.GenerateCalculationHash(task.LottoType, task.Period)
	if existingResult, err := e.repository.GetAnalysisResult(calculationHash); err == nil {
		log.Infof("Found existing calculation result for hash %s", calculationHash)
		return &CalculationResult{
			Success: true,
			Result:  existingResult,
		}
	}

	// 取得歷史開獎資料
	historyData, err := e.getHistoryData(task.LottoType, task.Period)
	if err != nil {
		return &CalculationResult{
			Success: false,
			Error:   fmt.Errorf("failed to get history data: %w", err),
		}
	}

	// 執行版路分析計算
	analysisResult, err := e.executeBallFollowAnalysis(ctx, subtask, historyData)
	if err != nil {
		return &CalculationResult{
			Success: false,
			Error:   fmt.Errorf("ball follow analysis failed: %w", err),
		}
	}

	// 設定計算結果的基本資訊
	analysisResult.LottoType = task.LottoType
	analysisResult.Period = task.Period
	analysisResult.AnalysisDate = time.Now()
	analysisResult.Comb1 = subtask.Comb1
	analysisResult.Comb2 = subtask.Comb2
	analysisResult.Comb3 = subtask.Comb3
	analysisResult.PeriodNum = subtask.PeriodNum
	analysisResult.MaxRange = subtask.MaxRange
	analysisResult.AnalysisPeriods = uint8(e.config.AnalysisPeriods)
	analysisResult.CalculationHash = calculationHash

	// 儲存計算結果
	if err := e.repository.CreateAnalysisResult(analysisResult); err != nil {
		return &CalculationResult{
			Success: false,
			Error:   fmt.Errorf("failed to save analysis result: %w", err),
		}
	}

	return &CalculationResult{
		Success: true,
		Result:  analysisResult,
	}
}

// executeBallFollowAnalysis 執行版路分析核心邏輯
func (e *CalculationEngine) executeBallFollowAnalysis(ctx context.Context, subtask *BallFollowSubtask, historyData []LottoResult) (*BallFollowAnalysisResult, error) {
	// 檢查上下文是否已取消
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 轉換歷史資料格式
	drawResults := e.convertToDrawResults(historyData)

	// 建立分析配置
	analysisConfig := AnalysisConfig{
		FirstGroupSize:  int(subtask.Comb1),
		SecondGroupSize: int(subtask.Comb2),
		TargetGroupSize: int(subtask.Comb3),
		MaxRange:        int(subtask.MaxRange),
		LookAheadCount:  1, // 預測下一期
	}

	// 執行版路分析計算
	analyzer := NewBallFollowAnalyzer(analysisConfig, drawResults)

	// 設定進度回調（可選）
	analyzer.SetProgressCallback(func(progress float64) {
		// 可以在這裡更新進度資訊
		log.Debugf("Subtask %d progress: %.2f%%", subtask.ID, progress*100)
	})

	// 執行分析
	analysisResults, err := analyzer.Analyze(ctx)
	if err != nil {
		return nil, fmt.Errorf("analysis execution failed: %w", err)
	}

	// 處理分析結果並轉換為資料庫格式
	result := e.processAnalysisResults(analysisResults, subtask)

	return result, nil
}

// processAnalysisResults 處理分析結果
func (e *CalculationEngine) processAnalysisResults(analysisResults *AnalysisResults, subtask *BallFollowSubtask) *BallFollowAnalysisResult {
	// 處理預測號碼統計
	predictNumbers := e.processPredictNumbers(analysisResults.StatResults)

	// 處理未出現號碼統計
	nonAppearedNumbers := e.processNonAppearedNumbers(analysisResults.StatResults)

	// 處理尾數統計
	tailStatistics := e.processTailStatistics(analysisResults.StatResults)

	// 處理目標號碼出現次數
	targetAppearances := e.processTargetAppearances(analysisResults.StatResults)

	// 處理未出現號碼按頻率排序
	nonAppearedByFrequency := e.processNonAppearedByFrequency(analysisResults.StatResults)

	// 處理尾數號碼出現次數
	tailNumAppearances := e.processTailNumAppearances(analysisResults.StatResults)

	// 建立資料來源期數資訊
	dataSourcePeriods := BallFollowJSON{
		"total_periods": len(analysisResults.SourcePeriods),
		"periods":       analysisResults.SourcePeriods,
		"start_period":  analysisResults.StartPeriod,
		"end_period":    analysisResults.EndPeriod,
	}

	return &BallFollowAnalysisResult{
		PredictNumbers:         predictNumbers,
		NonAppearedNumbers:     nonAppearedNumbers,
		TailStatistics:         tailStatistics,
		TargetAppearances:      targetAppearances,
		NonAppearedByFrequency: nonAppearedByFrequency,
		TailNumAppearances:     tailNumAppearances,
		DataSourcePeriods:      dataSourcePeriods,
	}
}

// 資料處理方法
func (e *CalculationEngine) processPredictNumbers(statResults []StatResult) BallFollowJSON {
	// 統計預測號碼出現次數
	numberAppearances := make(map[string]int)
	var allNumbers []int

	for _, stat := range statResults {
		for _, number := range stat.TargetNumbers {
			key := fmt.Sprintf("%d", number)
			numberAppearances[key]++
			allNumbers = append(allNumbers, number)
		}
	}

	// 計算機率
	total := len(allNumbers)
	probabilities := make(map[string]float64)
	for number, count := range numberAppearances {
		probabilities[number] = float64(count) / float64(total)
	}

	// 按頻率排序
	sortedNumbers := e.sortNumbersByFrequency(numberAppearances)

	return BallFollowJSON{
		"numbers":           e.getUniqueNumbers(allNumbers),
		"appearances":       numberAppearances,
		"probabilities":     probabilities,
		"sorted_by_freq":    sortedNumbers,
		"total_predictions": total,
	}
}

func (e *CalculationEngine) processNonAppearedNumbers(statResults []StatResult) BallFollowJSON {
	// 找出未出現的號碼
	appearedNumbers := make(map[int]bool)
	for _, stat := range statResults {
		for _, number := range stat.TargetNumbers {
			appearedNumbers[number] = true
		}
	}

	// 假設號碼範圍是1-39（今彩539）
	var nonAppearedNumbers []int
	for i := 1; i <= 39; i++ {
		if !appearedNumbers[i] {
			nonAppearedNumbers = append(nonAppearedNumbers, i)
		}
	}

	return BallFollowJSON{
		"numbers":        nonAppearedNumbers,
		"total_numbers":  len(nonAppearedNumbers),
		"analysis_range": "1-39",
	}
}

func (e *CalculationEngine) processTailStatistics(statResults []StatResult) BallFollowJSON {
	// 統計尾數出現次數
	tailAppearances := make(map[string]int)
	var allTails []int

	for _, stat := range statResults {
		for _, number := range stat.TargetNumbers {
			tail := number % 10
			key := fmt.Sprintf("%d", tail)
			tailAppearances[key]++
			allTails = append(allTails, tail)
		}
	}

	// 計算尾數機率
	total := len(allTails)
	tailProbabilities := make(map[string]float64)
	for tail, count := range tailAppearances {
		tailProbabilities[tail] = float64(count) / float64(total)
	}

	// 按頻率排序尾數
	sortedTails := e.sortTailsByFrequency(tailAppearances)

	return BallFollowJSON{
		"tail_appearances":   tailAppearances,
		"tail_probabilities": tailProbabilities,
		"sorted_tails":       sortedTails,
		"total_tails":        total,
	}
}

func (e *CalculationEngine) processTargetAppearances(statResults []StatResult) BallFollowJSON {
	numberAppearances := make(map[string]int)

	for _, stat := range statResults {
		for _, number := range stat.TargetNumbers {
			key := fmt.Sprintf("%d", number)
			numberAppearances[key]++
		}
	}

	// 計算統計資訊
	var maxAppearances, minAppearances int
	first := true
	for _, count := range numberAppearances {
		if first {
			maxAppearances = count
			minAppearances = count
			first = false
		} else {
			if count > maxAppearances {
				maxAppearances = count
			}
			if count < minAppearances {
				minAppearances = count
			}
		}
	}

	return BallFollowJSON{
		"number_appearances": numberAppearances,
		"total_targets":      len(numberAppearances),
		"max_appearances":    maxAppearances,
		"min_appearances":    minAppearances,
	}
}

func (e *CalculationEngine) processNonAppearedByFrequency(statResults []StatResult) BallFollowJSON {
	// 這裡可以實作更複雜的未出現號碼頻率分析
	// 暫時返回基本結構
	return BallFollowJSON{
		"frequency_analysis": "not_implemented",
	}
}

func (e *CalculationEngine) processTailNumAppearances(statResults []StatResult) BallFollowJSON {
	// 這裡可以實作尾數號碼出現次數的詳細分析
	// 暫時返回基本結構
	return BallFollowJSON{
		"tail_number_analysis": "not_implemented",
	}
}

// 輔助方法
func (e *CalculationEngine) getSubtaskInfo(subtaskID uint64) (*BallFollowSubtask, error) {
	var subtask BallFollowSubtask
	if err := e.repository.db.First(&subtask, subtaskID).Error; err != nil {
		return nil, err
	}
	return &subtask, nil
}

func (e *CalculationEngine) getHistoryData(lottoType LottoTypeStr, period int) ([]LottoResult, error) {
	return e.repository.GetHistoryDataForAnalysis(lottoType, period, e.config.AnalysisPeriods)
}

func (e *CalculationEngine) convertToDrawResults(lottoResults []LottoResult) []DrawResult {
	var drawResults []DrawResult

	for _, result := range lottoResults {
		numbers := make([]int, len(result.DrawNumberSize))
		copy(numbers, result.DrawNumberSize)

		// 如果有特別號且不是威力彩，加入特別號
		if result.SpecialNumber > 0 {
			numbers = append(numbers, result.SpecialNumber)
		}

		drawResult := DrawResult{
			Period:  fmt.Sprintf("%d", result.Period),
			Numbers: numbers,
		}

		drawResults = append(drawResults, drawResult)
	}

	return drawResults
}

func (e *CalculationEngine) getUniqueNumbers(numbers []int) []int {
	seen := make(map[int]bool)
	var unique []int

	for _, num := range numbers {
		if !seen[num] {
			seen[num] = true
			unique = append(unique, num)
		}
	}

	return unique
}

func (e *CalculationEngine) sortNumbersByFrequency(appearances map[string]int) []int {
	type numberFreq struct {
		number int
		freq   int
	}

	var pairs []numberFreq
	for numStr, freq := range appearances {
		if num := parseInt(numStr); num > 0 {
			pairs = append(pairs, numberFreq{number: num, freq: freq})
		}
	}

	// 按頻率降序排序
	for i := 0; i < len(pairs)-1; i++ {
		for j := i + 1; j < len(pairs); j++ {
			if pairs[i].freq < pairs[j].freq {
				pairs[i], pairs[j] = pairs[j], pairs[i]
			}
		}
	}

	var sorted []int
	for _, pair := range pairs {
		sorted = append(sorted, pair.number)
	}

	return sorted
}

func (e *CalculationEngine) sortTailsByFrequency(appearances map[string]int) []int {
	return e.sortNumbersByFrequency(appearances) // 使用相同的邏輯
}

func parseInt(s string) int {
	var result int
	for _, r := range s {
		if r >= '0' && r <= '9' {
			result = result*10 + int(r-'0')
		} else {
			return 0
		}
	}
	return result
}

func (e *CalculationEngine) getCurrentMemoryMB() int64 {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	return int64(m.Alloc / 1024 / 1024)
}

func (e *CalculationEngine) registerCalculation(subtaskID uint64, ctx *CalculationContext) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.activeCalculations[subtaskID] = ctx
}

func (e *CalculationEngine) unregisterCalculation(subtaskID uint64) {
	e.mu.Lock()
	defer e.mu.Unlock()
	delete(e.activeCalculations, subtaskID)
}

func (e *CalculationEngine) updateSubtaskStatus(subtaskID uint64, status SubtaskStatus, workerID string) error {
	updates := map[string]interface{}{
		"subtask_status": status,
		"worker_id":      workerID,
	}

	if status == SubtaskStatusRunning {
		updates["start_time"] = time.Now()
	}

	return e.repository.UpdateSubtaskStatus(subtaskID, updates)
}

func (e *CalculationEngine) updateSubtaskCompleted(subtaskID uint64, result *CalculationResult) error {
	updates := map[string]interface{}{
		"subtask_status":  SubtaskStatusCompleted,
		"end_time":        time.Now(),
		"duration":        uint32(result.Duration.Seconds()),
		"memory_usage_mb": result.MemoryUsageMB,
	}

	if result.Result != nil {
		updates["result_id"] = result.Result.ID
		updates["calculation_hash"] = result.Result.CalculationHash
	}

	return e.repository.UpdateSubtaskStatus(subtaskID, updates)
}

func (e *CalculationEngine) updateSubtaskFailed(subtaskID uint64, result *CalculationResult) error {
	updates := map[string]interface{}{
		"subtask_status":  SubtaskStatusFailed,
		"end_time":        time.Now(),
		"duration":        uint32(result.Duration.Seconds()),
		"error_message":   result.Error.Error(),
		"memory_usage_mb": result.MemoryUsageMB,
	}

	return e.repository.UpdateSubtaskStatus(subtaskID, updates)
}

func (e *CalculationEngine) updateStats(result *CalculationResult) {
	e.stats.mu.Lock()
	defer e.stats.mu.Unlock()

	e.stats.TotalCalculations++
	if result.Success {
		e.stats.SuccessfulCalculations++
	} else {
		e.stats.FailedCalculations++
	}

	// 更新平均計算時間
	if e.stats.TotalCalculations > 0 {
		totalTime := time.Duration(e.stats.TotalCalculations) * e.stats.AverageCalculationTime
		e.stats.AverageCalculationTime = (totalTime + result.Duration) / time.Duration(e.stats.TotalCalculations)
	} else {
		e.stats.AverageCalculationTime = result.Duration
	}

	// 更新峰值記憶體使用
	if result.MemoryUsageMB > e.stats.PeakMemoryUsage {
		e.stats.PeakMemoryUsage = result.MemoryUsageMB
	}
}

// GetStats 取得引擎統計資訊
func (e *CalculationEngine) GetStats() *EngineStats {
	e.stats.mu.RLock()
	defer e.stats.mu.RUnlock()

	e.mu.RLock()
	activeCount := len(e.activeCalculations)
	e.mu.RUnlock()

	statsCopy := *e.stats
	statsCopy.ActiveCalculations = activeCount
	return &statsCopy
}

// GetActiveCalculations 取得活躍計算列表
func (e *CalculationEngine) GetActiveCalculations() map[uint64]*CalculationContext {
	e.mu.RLock()
	defer e.mu.RUnlock()

	// 返回副本
	result := make(map[uint64]*CalculationContext)
	for k, v := range e.activeCalculations {
		result[k] = v
	}
	return result
}
