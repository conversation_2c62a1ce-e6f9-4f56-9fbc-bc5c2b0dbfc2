# UserPage.vue 日期格式化優化總結

## 優化內容

### 1. 創建專用的日期格式化工具模組

**文件位置**: `front/src/utils/date-formatter.ts`

**主要功能**:
- 提供完整的日期格式化工具函數集
- 支援台灣地區的日期時間格式
- 包含錯誤處理和邊界情況處理
- 支援多種格式化選項和自定義配置

### 2. 優化 UserPage.vue 中的日期處理

**優化前**:
```typescript
// 複雜的內聯日期格式化
user.expires_at = user.expires_at
  ? new Date(user.expires_at).toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  : '';

user.last_login_at = user.last_login_at
  ? new Date(user.last_login_at).toLocaleString('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    })
  : '尚未登入';

// 複雜的日期驗證
(val) =>
  !val ||
  (val &&
    val.length == 10 &&
    !isNaN(new Date(val).getTime())) ||
  '請輸入正確的日期',
```

**優化後**:
```typescript
// 簡潔的函數調用
user.expires_at = user.expires_at ? formatDateTW(user.expires_at) : '';
user.last_login_at = user.last_login_at 
  ? formatDateTimeTW(user.last_login_at) 
  : '尚未登入';
user.created_at = formatDateTimeTW(user.created_at);

// 簡潔的日期驗證
(val) =>
  !val ||
  (val && val.length == 10 && isValidDate(val)) ||
  '請輸入正確的日期',
```

### 3. 改進表格列定義

**優化前**:
```typescript
{
  name: 'expires_at',
  label: '使用期限',
  field: 'expires_at',
  align: 'center' as const,
  sortable: true,
}
```

**優化後**:
```typescript
{
  name: 'expires_at',
  label: '使用期限',
  field: 'expires_at',
  format: (val: string | null) => val || '無期限',
  align: 'center' as const,
  sortable: true,
}
```

## 新增的工具函數

### 基本格式化函數
- `formatDate()` - 通用日期格式化，支援多種選項
- `formatDateTW()` - 台灣標準日期格式 (YYYY/MM/DD)
- `formatDateTimeTW()` - 台灣標準日期時間格式
- `formatDateOnly()` - 僅顯示日期部分
- `formatDateTime()` - 完整日期時間格式

### 智能格式化函數
- `formatSmartDate()` - 智能日期顯示（今天顯示時間，其他顯示日期）
- `formatRelativeTime()` - 相對時間顯示（如：2小時前）

### 專用格式化函數
- `formatTimeOnly()` - 僅顯示時間
- `formatDateISO()` - ISO 日期格式
- `formatDateRange()` - 日期範圍格式化

### 工具函數
- `isValidDate()` - 日期有效性檢查
- `isToday()` / `isYesterday()` - 日期比較
- `getDaysDifference()` - 計算日期差

## 優化效果

### 1. 代碼可讀性提升
- 消除了複雜的內聯日期格式化代碼
- 使用語義化的函數名稱
- 減少了代碼重複

### 2. 維護性改善
- 集中管理日期格式化邏輯
- 統一的錯誤處理機制
- 易於修改和擴展

### 3. 重用性增強
- 可在整個應用程式中重用
- 支援多種格式化需求
- 提供完整的 TypeScript 類型支援

### 4. 錯誤處理改進
- 統一的錯誤處理策略
- 安全的 null/undefined 值處理
- 防止應用程式崩潰

## 使用示例

### 在組件中使用
```vue
<template>
  <div>
    <p>建立時間: {{ formatDateTimeTW(user.created_at) }}</p>
    <p>最後登入: {{ formatSmartDate(user.last_login_at) || '尚未登入' }}</p>
  </div>
</template>

<script setup>
import { formatDateTimeTW, formatSmartDate } from '@/utils/date-formatter';
</script>
```

### 在表格中使用
```typescript
const columns = [
  {
    name: 'created_at',
    label: '建立時間',
    field: 'created_at',
    format: (val: string) => formatDateTimeTW(val),
    align: 'center' as const,
    sortable: true,
  }
];
```

## 文件結構

```
front/src/utils/
├── date-formatter.ts              # 主要的日期格式化工具
├── date-formatter.example.ts      # 使用示例
├── date-formatter.README.md       # 詳細文檔
└── index.ts                       # 統一導出
```

## 後續建議

1. **擴展功能**: 可以根據需要添加更多專用的格式化函數
2. **國際化**: 可以擴展支援其他地區的日期格式
3. **性能優化**: 對於大量數據可以考慮添加緩存機制
4. **測試**: 建議為日期格式化函數編寫單元測試
