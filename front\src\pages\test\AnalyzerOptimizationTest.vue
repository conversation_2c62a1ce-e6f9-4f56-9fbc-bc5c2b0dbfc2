<template>
  <q-page padding>
    <div class="q-pa-md">
      <h4>Analyzer Worker 優化測試</h4>

      <q-card class="q-mb-md">
        <q-card-section>
          <h6>測試配置</h6>
          <q-banner
            v-if="!memorySupported"
            class="bg-orange-2 text-orange-8 q-mb-md"
            icon="warning"
          >
            注意：當前瀏覽器不支援記憶體監控，將無法顯示記憶體使用數據
          </q-banner>
          <div class="row q-gutter-md">
            <q-input
              v-model.number="testConfig.periods"
              label="測試期數"
              type="number"
              style="width: 150px"
            />
            <q-input
              v-model.number="testConfig.firstGroupSize"
              label="第一組大小"
              type="number"
              style="width: 150px"
            />
            <q-input
              v-model.number="testConfig.secondGroupSize"
              label="第二組大小"
              type="number"
              style="width: 150px"
            />
            <q-input
              v-model.number="testConfig.targetGroupSize"
              label="目標組大小"
              type="number"
              style="width: 150px"
            />
            <q-input
              v-model.number="testConfig.maxRange"
              label="最大範圍"
              type="number"
              style="width: 150px"
            />
          </div>
        </q-card-section>

        <q-card-actions>
          <q-btn
            @click="runComparison"
            color="primary"
            :loading="isRunning"
            :disable="isRunning"
          >
            開始比較測試
          </q-btn>
          <q-btn
            @click="clearResults"
            color="secondary"
            outline
          >
            清除結果
          </q-btn>
        </q-card-actions>
      </q-card>

      <!-- 測試結果 -->
      <q-card v-if="testResults.length > 0">
        <q-card-section>
          <h6>測試結果</h6>
          <q-table
            :rows="testResults"
            :columns="resultColumns"
            row-key="version"
            flat
            bordered
          />
        </q-card-section>
      </q-card>

      <!-- 性能比較 -->
      <q-card v-if="comparisonResult" class="q-mt-md">
        <q-card-section>
          <h6>性能比較</h6>
          <div class="row q-gutter-md">
            <div class="col">
              <q-linear-progress
                :value="comparisonResult.speedImprovement / 100"
                color="positive"
                size="20px"
              />
              <div class="text-center q-mt-xs">
                速度改善: {{ comparisonResult.speedImprovement.toFixed(2) }}%
              </div>
            </div>
            <div class="col">
              <q-linear-progress
                :value="comparisonResult.memoryReduction / 100"
                color="info"
                size="20px"
              />
              <div class="text-center q-mt-xs">
                記憶體減少: {{ comparisonResult.memoryReduction.toFixed(2) }}%
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 進度顯示 -->
      <q-dialog v-model="showProgress" persistent>
        <q-card style="min-width: 300px">
          <q-card-section>
            <div class="text-h6">{{ currentTest }}</div>
          </q-card-section>
          <q-card-section>
            <q-linear-progress
              :value="progress / 100"
              color="primary"
              size="10px"
            />
            <div class="text-center q-mt-xs">{{ progress.toFixed(1) }}%</div>
          </q-card-section>
        </q-card>
      </q-dialog>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { DrawResult } from '@/models/types';

interface TestResult {
  version: string;
  duration: number;
  peakMemory: number;
  currentMemory: number;
  resultCount: number;
  status: string;
}

interface ComparisonResult {
  speedImprovement: number;
  memoryReduction: number;
}

const isRunning = ref(false);
const showProgress = ref(false);
const currentTest = ref('');
const progress = ref(0);

// 檢查記憶體監控支援
const memorySupported = ref(false);

// 初始化時檢查記憶體支援
const extendedPerformance = performance as ExtendedPerformance;
memorySupported.value = extendedPerformance.memory !== undefined;

const testConfig = reactive({
  periods: 200,
  firstGroupSize: 2,
  secondGroupSize: 2,
  targetGroupSize: 2,
  maxRange: 10,
  lookAheadCount: 1
});

const testResults = ref<TestResult[]>([]);
const comparisonResult = ref<ComparisonResult | null>(null);

const resultColumns = [
  { name: 'version', label: '版本', field: 'version', align: 'left' as const },
  { name: 'duration', label: '執行時間(ms)', field: 'duration', align: 'right' as const },
  { name: 'peakMemory', label: '峰值記憶體(MB)', field: 'peakMemory', align: 'right' as const },
  { name: 'currentMemory', label: '當前記憶體(MB)', field: 'currentMemory', align: 'right' as const },
  { name: 'resultCount', label: '結果數量', field: 'resultCount', align: 'right' as const },
  { name: 'status', label: '狀態', field: 'status', align: 'center' as const }
];

// 生成測試數據
function generateTestData(periods: number): DrawResult[] {
  const results: DrawResult[] = [];
  for (let i = 1; i <= periods; i++) {
    const numbers = Array.from({length: 6}, () => Math.floor(Math.random() * 49) + 1)
      .sort((a, b) => a - b);
    results.push({
      period: i.toString().padStart(6, '0'),
      numbers: numbers
    });
  }
  return results;
}

// 擴展 Performance 接口
interface ExtendedPerformance extends Performance {
  memory?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

// 執行單個測試
async function runSingleTest(workerPath: string, version: string): Promise<TestResult> {
  return new Promise((resolve, ) => {
    const startTime = performance.now();
    let peakMemory = 0;
    let currentMemory = 0;
    let memorySupported = false;

    const worker = new Worker(workerPath, { type: 'module' });
    const testData = generateTestData(testConfig.periods);

    // 檢查記憶體監控支援
    const extendedPerformance = performance as ExtendedPerformance;
    memorySupported = extendedPerformance.memory !== undefined;

    // 監控記憶體（如果支援）
    const memoryInterval = setInterval(() => {
      if (memorySupported && extendedPerformance.memory) {
        const used = extendedPerformance.memory.usedJSHeapSize;
        peakMemory = Math.max(peakMemory, used);
        currentMemory = used;
      }
    }, 100);

    worker.onmessage = (e) => {
      const { type, data } = e.data;

      if (type === 'progress') {
        progress.value = (data.progress / data.total) * 100;
      } else if (type === 'complete') {
        clearInterval(memoryInterval);
        const endTime = performance.now();

        resolve({
          version,
          duration: Math.round(endTime - startTime),
          peakMemory: memorySupported ? Math.round(peakMemory / 1024 / 1024) : 0,
          currentMemory: memorySupported ? Math.round(currentMemory / 1024 / 1024) : 0,
          resultCount: data.data.length,
          status: memorySupported ? '成功' : '成功 (記憶體監控不可用)'
        });

        worker.terminate();
      } else if (type === 'error') {
        clearInterval(memoryInterval);
        resolve({
          version,
          duration: 0,
          peakMemory: 0,
          currentMemory: 0,
          resultCount: 0,
          status: '失敗: ' + data.message
        });
        worker.terminate();
      }
    };

    worker.onerror = (error) => {
      clearInterval(memoryInterval);
      resolve({
        version,
        duration: 0,
        peakMemory: 0,
        currentMemory: 0,
        resultCount: 0,
        status: '錯誤: ' + error.message
      });
      worker.terminate();
    };

    // 發送測試數據
    worker.postMessage({
      type: 'init',
      data: {
        results: JSON.stringify(testData),
        config: JSON.stringify(testConfig)
      }
    });
  });
}

// 執行比較測試
async function runComparison(): Promise<void> {
  isRunning.value = true;
  showProgress.value = true;
  testResults.value = [];
  comparisonResult.value = null;

  try {
    // 測試優化版本
    currentTest.value = '測試優化版本...';
    progress.value = 0;
    const optimizedResult = await runSingleTest('/src/workers/analyzer.worker.ts', '優化版本');
    testResults.value.push(optimizedResult);

    // 等待記憶體穩定
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 如果有原始版本，進行比較
    // 注意：這裡假設您已經備份了原始版本
    // const originalResult = await runSingleTest('/src/workers/analyzer.worker.original.ts', '原始版本');
    // testResults.value.push(originalResult);

    // 計算改善程度
    // if (testResults.value.length === 2) {
    //   const [optimized, original] = testResults.value;
    //   comparisonResult.value = {
    //     speedImprovement: ((original.duration - optimized.duration) / original.duration) * 100,
    //     memoryReduction: ((original.peakMemory - optimized.peakMemory) / original.peakMemory) * 100
    //   };
    // }

  } catch (error) {
    console.error('測試失敗:', error);
  } finally {
    isRunning.value = false;
    showProgress.value = false;
  }
}

function clearResults(): void {
  testResults.value = [];
  comparisonResult.value = null;
}
</script>
