import { register } from 'register-service-worker';

// 定義全局 window 類型
interface GlobalWindow {
  location: {
    href: string;
    reload(): void;
  };
}

// The ready(), registered(), cached(), updatefound() and updated()
// events passes a ServiceWorkerRegistration instance in their arguments.
// ServiceWorkerRegistration: https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerRegistration

register(process.env.SERVICE_WORKER_FILE, {
  // The registrationOptions object will be passed as the second argument
  // to ServiceWorkerContainer.register()
  // https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register#Parameter

  // registrationOptions: { scope: './' },

  ready (/* registration */) {
    // console.log('Service worker is active.')
  },

  registered (/* registration */) {
    // console.log('Service worker has been registered.')
  },

  cached (/* registration */) {
    // console.log('Content has been cached for offline use.')
  },

  updatefound (/* registration */) {
    // console.log('New content is downloading.')
  },

  updated (registration) {
    // 當發現新內容時，自動更新

    // 自動觸發更新流程
    const performAutoUpdate = () => {
      if ('serviceWorker' in navigator) {
        if (registration.waiting) {
          registration.waiting.postMessage({ type: 'SKIP_WAITING' })
        }
      }

      // 等待一小段時間讓新的 Service Worker 啟動，然後重新載入頁面
      setTimeout(() => {
        try {
          // 使用 globalThis 來訪問全局對象
          const globalWindow = globalThis as unknown as GlobalWindow
          if (globalWindow && globalWindow.location && globalWindow.location.reload) {
            globalWindow.location.reload()
          } else {
            // 備用方案：重新導向到當前頁面
            const currentUrl = globalWindow.location.href
            globalWindow.location.href = currentUrl
          }
        } catch (error) {
          console.error('重新載入頁面時發生錯誤:', error)
          // 最後的備用方案
          const globalWindow = globalThis as unknown as GlobalWindow
          globalWindow.location.href = globalWindow.location.href
        }
      }, 1000) // 增加等待時間確保 Service Worker 完全啟動
    }

    // 使用 Quasar 的 Notify 插件顯示更新通知（可選）
    import('quasar').then(({ Notify }) => {
      // 顯示簡短的更新通知
      Notify.create({
        message: '正在更新到最新版本...',
        icon: 'cloud_download',
        color: 'primary',
        position: 'top',
        timeout: 2000, // 2秒後自動關閉
        actions: [
          {
            label: '立即更新',
            color: 'white',
            handler: performAutoUpdate
          }
        ]
      })

      // 3秒後自動執行更新（給用戶一點時間看到通知）
      setTimeout(performAutoUpdate, 3000)
    }).catch(() => {
      // 如果 Quasar 載入失敗，直接執行更新
      performAutoUpdate()
    })
  },

  offline () {
    // console.log('No internet connection found. App is running in offline mode.')
  },

  error (/* err */) {
    // console.error('Error during service worker registration:', err)
  },
});



