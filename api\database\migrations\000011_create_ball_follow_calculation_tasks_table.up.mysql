CREATE TABLE IF NOT EXISTS `ball_follow_calculation_tasks` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `lotto_type` VARCHAR(20) NOT NULL COMMENT '彩種類型',
    `period` INT NOT NULL COMMENT '期數',
    `task_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '任務狀態(pending/running/completed/failed/stopped)',
    `total_combinations` SMALLINT UNSIGNED NOT NULL DEFAULT 1350 COMMENT '總參數組合數',
    `completed_combinations` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已完成組合數',
    `failed_combinations` SMALLINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '失敗組合數',
    `start_time` TIMESTAMP NULL COMMENT '開始時間',
    `end_time` TIMESTAMP NULL COMMENT '結束時間',
    `estimated_duration` INT UNSIGNED NULL COMMENT '預估執行時間(秒)',
    `actual_duration` INT UNSIGNED NULL COMMENT '實際執行時間(秒)',
    `error_message` TEXT NULL COMMENT '錯誤訊息',
    `retry_count` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重試次數',
    `priority` TINYINT UNSIGNED NOT NULL DEFAULT 5 COMMENT '任務優先級(1-10, 數字越小優先級越高)',
    `triggered_by` VARCHAR(50) NOT NULL DEFAULT 'scheduler' COMMENT '觸發方式(scheduler/manual/api)',
    `config_snapshot` JSON NULL COMMENT '執行時的配置快照',
    `progress_details` JSON NULL COMMENT '進度詳細資訊',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE INDEX `idx_lotto_period` (`lotto_type`, `period`),
    INDEX `idx_task_status` (`task_status`),
    INDEX `idx_start_time` (`start_time`),
    INDEX `idx_lotto_type_status` (`lotto_type`, `task_status`),
    INDEX `idx_priority_status` (`priority`, `task_status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='版路分析計算任務狀態表';
