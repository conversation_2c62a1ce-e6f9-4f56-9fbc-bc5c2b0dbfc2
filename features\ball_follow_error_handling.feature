# language: zh-TW
功能: 版路分析錯誤處理與重試機制
  作為系統管理員
  我需要確保版路分析系統
  能夠妥善處理各種錯誤情況
  並具備適當的重試和恢復機制

  背景:
    假設 版路分析排程服務正在運行
    而且 系統具備錯誤監控和日誌記錄功能

  場景: 記憶體不足錯誤的處理
    假設 正在執行參數組合(3,3,3)、推算期數300、拖牌區間30的計算
    當 系統記憶體使用率達到95%
    而且 發生OutOfMemoryError
    那麼 系統應該記錄錯誤訊息 "記憶體不足，暫停計算"
    而且 應該觸發垃圾回收機制
    而且 應該暫停當前計算任務
    而且 應該等待記憶體使用率降到70%以下
    而且 應該重試該參數組合的計算
    而且 重試次數應該增加1

  場景: 資料庫連接錯誤的重試機制
    假設 正在儲存計算結果到資料庫
    當 發生資料庫連接超時錯誤
    那麼 系統應該記錄錯誤訊息
    而且 應該等待5秒後重試
    而且 如果重試3次仍然失敗
    那麼 應該將該計算結果暫存到本地檔案
    而且 應該繼續執行下一個計算任務
    而且 應該在資料庫恢復後重新儲存暫存的結果

  場景: 計算邏輯錯誤的處理
    假設 正在執行版路分析計算
    當 計算過程中發生除零錯誤或陣列越界錯誤
    那麼 系統應該記錄詳細的錯誤堆疊資訊
    而且 應該記錄當前的參數組合和輸入資料
    而且 應該標記該參數組合為失敗
    而且 不應該重試該參數組合
    而且 應該繼續執行下一個參數組合
    而且 應該發送錯誤通知給開發團隊

  場景: 網路連接中斷的處理
    假設 系統需要從外部API獲取開獎資料
    當 發生網路連接中斷
    那麼 系統應該記錄網路錯誤
    而且 應該使用指數退避策略重試
    而且 第1次重試應該等待2秒
    而且 第2次重試應該等待4秒
    而且 第3次重試應該等待8秒
    而且 如果3次重試都失敗
    那麼 應該使用本地快取的資料繼續計算
    而且 應該標記資料來源為 "cached"

  場景: 檔案系統錯誤的處理
    假設 系統需要寫入暫存檔案或日誌檔案
    當 發生磁碟空間不足錯誤
    那麼 系統應該記錄錯誤訊息
    而且 應該清理超過7天的舊日誌檔案
    而且 應該清理暫存目錄中的過期檔案
    而且 如果清理後仍然空間不足
    那麼 應該暫停所有計算任務
    而且 應該發送緊急通知給管理員

  場景: 並行計算衝突的處理
    假設 系統配置為最多3個並行計算
    當 第4個計算任務嘗試開始執行
    那麼 系統應該將該任務加入等待佇列
    而且 應該記錄 "達到並行上限，任務加入佇列" 的訊息
    而且 當有計算任務完成時
    那麼 應該自動從佇列中取出下一個任務執行

  場景: 資料完整性錯誤的處理
    假設 正在讀取今彩539的歷史開獎資料
    當 發現某期的開獎號碼資料不完整或格式錯誤
    那麼 系統應該記錄資料錯誤訊息
    而且 應該跳過該期的資料
    而且 應該使用其他完整期數的資料繼續計算
    而且 應該在計算結果中標註 "部分期數資料缺失"
    而且 應該通知資料維護人員檢查資料

  場景: 系統資源監控和預警
    假設 系統正在執行多個計算任務
    當 CPU使用率超過90%持續5分鐘
    那麼 系統應該記錄效能警告
    而且 應該降低並行計算數量到1
    而且 應該發送效能警告通知
    而且 當CPU使用率降到70%以下
    那麼 應該恢復正常的並行計算數量

  場景: 計算超時的處理
    假設 單個參數組合的計算預期在10分鐘內完成
    當 某個計算任務執行超過30分鐘
    那麼 系統應該強制終止該計算任務
    而且 應該記錄超時錯誤訊息
    而且 應該標記該參數組合為失敗
    而且 應該釋放相關的系統資源
    而且 應該繼續執行下一個計算任務

  場景: 重試次數限制的驗證
    假設 某個參數組合已經重試2次都失敗
    當 第3次重試也失敗
    那麼 系統應該停止重試該參數組合
    而且 應該將重試次數記錄為3
    而且 應該標記該參數組合為永久失敗
    而且 應該記錄最終的錯誤訊息
    而且 應該繼續執行其他參數組合

  場景: 嚴重錯誤的緊急停止機制
    假設 系統檢測到以下嚴重錯誤之一:
      | 錯誤類型           | 錯誤描述                 |
      | 資料庫完全無法連接  | 所有資料庫連接池都失效    |
      | 系統記憶體洩漏     | 記憶體使用率持續上升到98% |
      | 核心服務崩潰       | 計算引擎程序意外終止      |
    當 檢測到任一嚴重錯誤
    那麼 系統應該立即停止所有計算任務
    而且 應該保存當前的計算狀態
    而且 應該發送緊急通知給管理員
    而且 應該記錄詳細的系統狀態快照
    而且 應該嘗試優雅地關閉服務

  場景: 錯誤恢復後的狀態重建
    假設 系統因嚴重錯誤而停止
    而且 錯誤已被修復，系統重新啟動
    當 系統重新啟動後
    那麼 應該檢查未完成的計算任務
    而且 應該恢復 "running" 狀態的任務為 "pending"
    而且 應該重新載入計算佇列
    而且 應該驗證資料庫連接和資料完整性
    而且 應該記錄系統恢復的日誌訊息

  場景: 錯誤統計和報告
    假設 系統運行了24小時
    當 生成錯誤統計報告
    那麼 報告應該包含以下資訊:
      | 統計項目              | 說明                     |
      | total_errors         | 總錯誤次數               |
      | error_types          | 錯誤類型分布             |
      | retry_success_rate   | 重試成功率               |
      | failed_combinations  | 永久失敗的參數組合數     |
      | average_recovery_time| 平均錯誤恢復時間         |
      | system_uptime        | 系統正常運行時間百分比   |

  場景: 自動錯誤分類和處理策略
    假設 系統遇到不同類型的錯誤
    當 錯誤發生時
    那麼 系統應該根據錯誤類型採用不同的處理策略:
      | 錯誤類型     | 處理策略                           |
      | 暫時性錯誤   | 立即重試，最多3次                  |
      | 資源不足錯誤 | 等待資源釋放後重試                 |
      | 邏輯錯誤     | 不重試，記錄詳細資訊               |
      | 系統錯誤     | 重啟相關服務後重試                 |
      | 資料錯誤     | 跳過錯誤資料，繼續處理             |

  場景: 錯誤通知機制的測試
    假設 系統配置了錯誤通知機制
    當 發生需要通知的錯誤
    那麼 系統應該根據錯誤嚴重程度發送不同級別的通知:
      | 錯誤級別 | 通知方式                    | 通知對象     |
      | INFO     | 記錄到日誌檔案              | 無           |
      | WARN     | 記錄到日誌檔案 + 系統監控   | 運維人員     |
      | ERROR    | 日誌 + 監控 + 郵件通知      | 開發團隊     |
      | CRITICAL | 日誌 + 監控 + 郵件 + 簡訊   | 管理員+開發團隊 |
