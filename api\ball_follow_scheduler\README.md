# Ball Follow Scheduler - 版路分析後台排程服務

版路分析後台排程服務是一個獨立的 Golang 應用程序，專門用於自動化執行彩票版路分析計算。

## 功能特點

### 🎯 核心功能
- **自動排程**: 每晚 10:30 自動檢查新開獎號碼並觸發計算
- **版路分析**: 執行完整的版路分析計算，支援 1350 種參數組合
- **多彩種支援**: 支援今彩539，可擴展至大樂透、六合彩等
- **並行計算**: 可配置的並行計算數量，提升處理效率
- **結果存儲**: 計算結果自動存儲到資料庫供查詢使用

### 🛡️ 可靠性保障
- **錯誤處理**: 完整的錯誤處理和重試機制
- **記憶體管理**: 自動記憶體監控和垃圾回收
- **進度追蹤**: 即時任務進度監控和狀態管理
- **通知系統**: 郵件通知重要事件和錯誤

### 📊 監控與管理
- **統計資訊**: 詳細的執行統計和效能指標
- **日誌記錄**: 結構化日誌記錄，支援多種格式
- **配置管理**: 靈活的 JSON 配置檔案
- **測試模式**: 內建測試模式驗證功能

## 系統架構

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scheduler     │    │  Worker Pool    │    │ Calculation     │
│   排程服務      │───▶│  工作程序池     │───▶│ Engine          │
│                 │    │                 │    │ 計算引擎        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Memory Manager  │    │   Notifier      │    │   Database      │
│ 記憶體管理器    │    │   通知系統      │    │   資料庫        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 安裝與配置

### 系統需求
- Go 1.19 或更高版本
- MySQL 5.7 或更高版本
- 至少 2GB 可用記憶體
- 足夠的磁碟空間存儲日誌和計算結果

### 安裝步驟

1. **編譯程序**
```bash
cd api/ball_follow_scheduler
go build -o ball_follow_scheduler main.go
```

2. **配置資料庫**
```bash
# 執行 migration 建立必要的資料表
cd ../database/migrations
# 使用 golang-migrate 或其他工具執行 migration
```

3. **配置服務**
```bash
# 複製並編輯配置檔案
cp config/ball_follow_scheduler.json config/production.json
# 編輯配置檔案，設定資料庫連接、通知等
```

4. **設定權限**
```bash
chmod +x start.sh
```

## 使用方法

### 啟動腳本
```bash
# 啟動服務
./start.sh start

# 停止服務
./start.sh stop

# 重啟服務
./start.sh restart

# 檢查狀態
./start.sh status

# 查看日誌
./start.sh logs

# 測試模式
./start.sh test
```

### 直接執行
```bash
# 使用預設配置
./ball_follow_scheduler

# 使用指定配置
./ball_follow_scheduler -config /path/to/config.json

# 測試模式
./ball_follow_scheduler -test

# 設定日誌級別
./ball_follow_scheduler -log-level debug

# 顯示版本
./ball_follow_scheduler -version
```

## 配置說明

### 資料庫配置
```json
{
  "database": {
    "host": "localhost",
    "port": 3306,
    "username": "lottery",
    "password": "password",
    "database": "lottery",
    "charset": "utf8mb4",
    "timezone": "Asia/Taipei"
  }
}
```

### 排程配置
```json
{
  "scheduler": {
    "check_schedule": "30 22 * * *",  // 每晚 10:30
    "timezone": "Asia/Taipei",
    "enabled": true,
    "supported_lotteries": ["daily539"]
  }
}
```

### 計算配置
```json
{
  "calculation": {
    "max_concurrent_calculations": 3,
    "calculation_timeout": "30m",
    "retry_limit": 3,
    "analysis_periods": 50,
    "parameter_combinations": {
      "comb_range": [1, 2, 3],
      "period_numbers": [30, 60, 90, 120, 150, 180, 210, 240, 270, 300],
      "max_ranges": [10, 15, 20, 25, 30]
    }
  }
}
```

### 記憶體管理配置
```json
{
  "memory": {
    "threshold_percent": 80,
    "auto_gc_enabled": true,
    "gc_interval": "5m",
    "max_memory_mb": 2048
  }
}
```

### 通知配置
```json
{
  "notification": {
    "enabled": true,
    "notification_levels": ["error", "critical"],
    "recipients": ["<EMAIL>"],
    "email": {
      "smtp_host": "smtp.gmail.com",
      "smtp_port": 587,
      "use_tls": true
    }
  }
}
```

## 監控與維護

### 日誌檔案
- **主日誌**: `logs/ball_follow_scheduler.log`
- **啟動日誌**: `logs/startup.log`
- **錯誤日誌**: 包含在主日誌中，可通過級別篩選

### 監控指標
- 任務執行狀態和進度
- 記憶體使用情況
- 計算效能統計
- 錯誤率和重試次數

### 維護建議
1. **定期檢查日誌**: 監控錯誤和警告訊息
2. **監控記憶體使用**: 確保不超過系統限制
3. **資料庫維護**: 定期清理舊的計算結果
4. **配置調優**: 根據系統效能調整並行數量

## 故障排除

### 常見問題

**Q: 服務無法啟動**
A: 檢查配置檔案格式、資料庫連接、權限設定

**Q: 記憶體使用過高**
A: 調整 `max_concurrent_calculations` 和 `memory.threshold_percent`

**Q: 計算速度慢**
A: 增加 `worker_count` 或檢查資料庫查詢效能

**Q: 通知郵件無法發送**
A: 檢查 SMTP 配置和網路連接

### 除錯模式
```bash
# 啟用除錯日誌
./ball_follow_scheduler -log-level debug

# 測試模式驗證功能
./ball_follow_scheduler -test
```

## 開發與擴展

### 新增彩種支援
1. 在 `supported_lotteries` 中添加新彩種
2. 更新 `GetLatestPeriodByLottoType` 方法
3. 調整參數組合配置

### 自定義計算邏輯
1. 修改 `BallFollowAnalyzer` 類別
2. 更新計算參數配置
3. 測試新的計算邏輯

### API 整合
服務提供內部 API 供管理介面使用，詳見 API 文檔。

## 版本資訊

- **版本**: 1.0.0
- **Go 版本**: 1.19+
- **相依套件**: 詳見 `go.mod`

## 授權

此專案為內部使用，請遵守相關授權條款。
