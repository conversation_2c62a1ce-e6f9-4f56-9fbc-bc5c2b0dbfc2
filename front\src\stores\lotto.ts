import { defineStore } from 'pinia';
import { LottoItem } from '@/api/modules/lotto';
import { getDrawLabel } from '@/constants/lottoConstants';

export const useLottoStore = defineStore('lotto', {
  state: () => {
    return {
      drawType: '' as string,
      lotto: null as LottoItem | null,
    };
  },
  persist: true,
  getters: {
    getDrawType: (state) => state.drawType,
    getDrawLabel: (state) => getDrawLabel(state.drawType),
    getLotto: (state) => state.lotto,
    isSuperLotto: (state) => state.drawType === 'super_lotto638',
  },
  actions: {
    setDrawType(drawType: string) {
      this.drawType = drawType;
    },
    setLotto(lotto: LottoItem) {
      this.lotto = lotto;
    },
  },
});
