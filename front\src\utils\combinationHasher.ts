/**
 * 組合 Hash 工具類 - 前端版本
 * 與 worker 中的 CombinationHasher 保持一致
 */
export class CombinationKeyGenerator {
  static generateKey(
    firstGroup: number[] | Uint8Array | Uint16Array,
    secondGroup: number[] | Uint8Array | Uint16Array,
    gap: number,
    targetGap: number
  ): string {
    // 確保組合內部排序一致，避免 [1,2] 和 [2,1] 產生不同 key
    const sortedFirst = Array.from(firstGroup).sort((a, b) => a - b);
    const sortedSecond = Array.from(secondGroup).sort((a, b) => a - b);

    return `${sortedFirst.join(',')}-${sortedSecond.join(',')}-${gap}-${targetGap}`;
  }
}
