package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"time"

	log "github.com/sirupsen/logrus"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"lottery/ball_follow_scheduler/config"
	"lottery/ball_follow_scheduler/scheduler"
	"lottery/database"
)

var (
	configPath = flag.String("config", "", "Path to configuration file")
	logLevel   = flag.String("log-level", "", "Log level (debug, info, warn, error)")
	testMode   = flag.Bool("test", false, "Run in test mode")
	version    = flag.Bool("version", false, "Show version information")
)

const (
	AppName    = "Ball Follow Scheduler"
	AppVersion = "1.0.0"
)

func main() {
	flag.Parse()

	// 顯示版本資訊
	if *version {
		fmt.Printf("%s v%s\n", AppName, AppVersion)
		os.Exit(0)
	}

	// 載入配置
	cfg, err := loadConfiguration()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// 設定日誌
	setupLogging(cfg)

	log.Infof("Starting %s v%s", AppName, AppVersion)

	// 連接資料庫
	db, err := connectDatabase(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化資料庫表格
	if err := initializeDatabase(db); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 建立排程服務
	schedulerService, err := scheduler.NewScheduler(cfg, db)
	if err != nil {
		log.Fatalf("Failed to create scheduler: %v", err)
	}

	// 測試模式
	if *testMode {
		runTestMode(cfg, db, schedulerService)
		return
	}

	// 運行排程服務
	if err := schedulerService.Run(); err != nil {
		log.Fatalf("Scheduler error: %v", err)
	}

	log.Info("Ball Follow Scheduler stopped")
}

// loadConfiguration 載入配置
func loadConfiguration() (*config.Config, error) {
	var cfg *config.Config
	var err error

	if *configPath != "" {
		// 從指定路徑載入配置
		cfg, err = config.LoadConfig(*configPath)
		if err != nil {
			return nil, fmt.Errorf("failed to load config from %s: %w", *configPath, err)
		}
	} else {
		// 嘗試從預設路徑載入配置
		defaultPaths := []string{
			"config/ball_follow_scheduler.json",
			"../config/ball_follow_scheduler.json",
			"/etc/lottery/ball_follow_scheduler.json",
		}

		for _, path := range defaultPaths {
			if _, err := os.Stat(path); err == nil {
				cfg, err = config.LoadConfig(path)
				if err == nil {
					log.Infof("Loaded configuration from: %s", path)
					break
				}
			}
		}

		// 如果找不到配置檔案，使用預設配置
		if cfg == nil {
			log.Warn("No configuration file found, using default configuration")
			cfg = config.GetDefaultConfig()
		}
	}

	// 覆蓋日誌級別
	if *logLevel != "" {
		cfg.Logging.Level = *logLevel
	}

	return cfg, nil
}

// setupLogging 設定日誌
func setupLogging(cfg *config.Config) {
	// 設定日誌級別
	level, err := log.ParseLevel(cfg.Logging.Level)
	if err != nil {
		log.Warnf("Invalid log level %s, using info", cfg.Logging.Level)
		level = log.InfoLevel
	}
	log.SetLevel(level)

	// 設定日誌格式
	if cfg.Logging.Format == "json" {
		log.SetFormatter(&log.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	} else {
		log.SetFormatter(&log.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}

	// 設定日誌輸出
	if cfg.Logging.OutputPath != "" {
		// 確保日誌目錄存在
		logDir := filepath.Dir(cfg.Logging.OutputPath)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			log.Warnf("Failed to create log directory %s: %v", logDir, err)
			return
		}

		// 開啟日誌檔案
		logFile, err := os.OpenFile(cfg.Logging.OutputPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			log.Warnf("Failed to open log file %s: %v", cfg.Logging.OutputPath, err)
			return
		}

		log.SetOutput(logFile)
		log.Infof("Log output set to: %s", cfg.Logging.OutputPath)
	}
}

// connectDatabase 連接資料庫
func connectDatabase(cfg *config.Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=%s",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Database,
		cfg.Database.Charset,
		cfg.Database.Timezone,
	)

	// 設定GORM日誌級別
	var gormLogLevel logger.LogLevel
	switch cfg.Logging.Level {
	case "debug":
		gormLogLevel = logger.Info
	case "info":
		gormLogLevel = logger.Warn
	default:
		gormLogLevel = logger.Error
	}

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(gormLogLevel),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 設定連接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database instance: %w", err)
	}

	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.Database.ConnMaxLifetime)

	log.Info("Database connected successfully")
	return db, nil
}

// initializeDatabase 初始化資料庫
func initializeDatabase(db *gorm.DB) error {
	log.Info("Initializing database tables...")

	// 初始化版路分析相關表格
	if err := database.InitBallFollowTables(db); err != nil {
		return fmt.Errorf("failed to initialize ball follow tables: %w", err)
	}

	// 驗證表格結構
	if err := database.ValidateBallFollowTables(db); err != nil {
		log.Warnf("Table validation warning: %v", err)
	}

	log.Info("Database initialization completed")
	return nil
}

// runTestMode 運行測試模式
func runTestMode(cfg *config.Config, db *gorm.DB, schedulerService *scheduler.Scheduler) {
	log.Info("Running in test mode...")

	// 建立測試資料
	if err := database.CreateSampleBallFollowData(db); err != nil {
		log.Errorf("Failed to create sample data: %v", err)
		return
	}

	// 取得統計資訊
	stats, err := database.GetBallFollowTableStats(db)
	if err != nil {
		log.Errorf("Failed to get table stats: %v", err)
		return
	}

	log.Infof("Database stats: %+v", stats)

	// 測試排程服務
	if err := schedulerService.Start(); err != nil {
		log.Errorf("Failed to start scheduler: %v", err)
		return
	}

	// 等待一段時間
	log.Info("Test mode running for 30 seconds...")
	time.Sleep(30 * time.Second)

	// 停止服務
	if err := schedulerService.Stop(); err != nil {
		log.Errorf("Failed to stop scheduler: %v", err)
	}

	// 清理測試資料
	if err := database.CleanupBallFollowTestData(db); err != nil {
		log.Errorf("Failed to cleanup test data: %v", err)
	}

	log.Info("Test mode completed")
}

// createDefaultConfig 建立預設配置檔案
func createDefaultConfig(path string) error {
	cfg := config.GetDefaultConfig()

	// 確保目錄存在
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// 儲存配置
	if err := cfg.SaveConfig(path); err != nil {
		return fmt.Errorf("failed to save config: %w", err)
	}

	log.Infof("Default configuration created at: %s", path)
	return nil
}

// showUsage 顯示使用說明
func showUsage() {
	fmt.Printf(`%s v%s

Usage: %s [options]

Options:
  -config string
        Path to configuration file
  -log-level string
        Log level (debug, info, warn, error)
  -test
        Run in test mode
  -version
        Show version information

Examples:
  # Run with default configuration
  %s

  # Run with custom configuration
  %s -config /path/to/config.json

  # Run in test mode
  %s -test

  # Run with debug logging
  %s -log-level debug

Configuration:
  The scheduler looks for configuration files in the following order:
  1. Path specified by -config flag
  2. ./config/ball_follow_scheduler.json
  3. ../config/ball_follow_scheduler.json
  4. /etc/lottery/ball_follow_scheduler.json
  5. Default configuration (if no file found)

`,
		AppName, AppVersion,
		os.Args[0],
		os.Args[0],
		os.Args[0],
		os.Args[0],
		os.Args[0],
	)
}
