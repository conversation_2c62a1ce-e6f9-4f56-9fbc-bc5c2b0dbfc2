# language: zh-TW
功能: 版路分析效能與擴展性
  作為系統架構師
  我需要確保版路分析系統
  具備良好的效能表現和擴展能力
  以支援大量計算和多彩種分析

  背景:
    假設 系統運行在標準的伺服器環境中
    而且 具備充足的硬體資源進行效能測試

  場景: 記憶體使用效率驗證
    假設 系統開始執行今彩539期數113001的完整計算
    而且 總共需要計算1350個參數組合
    當 計算進行到50%完成度
    那麼 系統記憶體使用量應該不超過2GB
    而且 記憶體使用量應該保持穩定，不出現持續增長
    而且 垃圾回收應該能有效釋放不需要的記憶體
    而且 記憶體使用率應該在70%以下

  場景: 並行計算效能測試
    假設 系統配置為3個並行計算
    當 同時執行3個不同參數組合的計算
    那麼 每個計算任務的執行時間不應該因並行而顯著增加
    而且 CPU使用率應該在80%以下
    而且 系統應該能穩定運行而不出現死鎖或競爭條件
    而且 計算結果的準確性不應該受到並行執行的影響

  場景: 大量資料處理效能
    假設 需要分析今彩539最近300期的歷史資料
    而且 每期包含5個開獎號碼
    當 執行單個參數組合的計算
    那麼 資料載入時間應該在30秒內
    而且 計算執行時間應該在5分鐘內
    而且 結果儲存時間應該在10秒內
    而且 總執行時間應該在6分鐘內完成

  場景: 資料庫查詢效能優化
    假設 版路分析結果表中有100萬筆記錄
    當 查詢特定期數和參數組合的結果
    那麼 查詢回應時間應該在1秒內
    而且 當查詢最近30天的計算任務列表
    那麼 查詢回應時間應該在2秒內
    而且 當執行複雜的統計查詢
    那麼 查詢回應時間應該在5秒內

  場景: 系統擴展性 - 支援多彩種
    假設 系統需要同時支援今彩539、大樂透、六合彩
    當 三種彩種同時有新期數需要計算
    那麼 系統應該能夠獨立管理每種彩種的計算任務
    而且 不同彩種的計算不應該互相影響
    而且 系統資源應該能夠合理分配給不同彩種
    而且 每種彩種的計算進度應該獨立追蹤

  場景: 動態負載調整
    假設 系統檢測到高負載情況
    而且 CPU使用率超過85%
    當 系統負載過高時
    那麼 應該自動降低並行計算數量
    而且 應該延長計算任務之間的間隔時間
    而且 應該優先處理重要的計算任務
    而且 當負載降低後應該恢復正常的處理能力

  場景: 快取機制效能驗證
    假設 系統實作了計算結果快取機制
    當 重複查詢相同參數組合的結果
    那麼 第二次查詢的回應時間應該在100毫秒內
    而且 快取命中率應該達到80%以上
    而且 快取應該能夠自動清理過期的資料
    而且 快取大小應該控制在合理範圍內

  場景: 批次處理效能優化
    假設 需要處理1350個參數組合
    當 使用批次處理模式
    那麼 每批次應該處理10-50個參數組合
    而且 批次之間應該有適當的間隔避免系統過載
    而且 批次處理的總時間應該比逐一處理減少30%以上
    而且 批次處理不應該影響結果的準確性

  場景: 資料壓縮和儲存優化
    假設 計算結果包含大量的JSON資料
    當 儲存到資料庫時
    那麼 應該使用適當的壓縮演算法
    而且 壓縮後的資料大小應該減少50%以上
    而且 解壓縮時間應該在1秒內
    而且 壓縮不應該影響資料的完整性

  場景: 系統監控指標收集
    假設 系統運行版路分析計算
    當 收集效能監控指標
    那麼 應該記錄以下關鍵指標:
      | 指標名稱                  | 收集頻率 | 保留期間 |
      | cpu_usage_percent        | 每分鐘   | 30天     |
      | memory_usage_mb          | 每分鐘   | 30天     |
      | active_calculations      | 即時     | 7天      |
      | calculation_duration_avg | 每小時   | 90天     |
      | database_query_time      | 每次查詢 | 7天      |
      | error_rate_percent       | 每小時   | 30天     |

  場景: 高可用性和容錯能力
    假設 系統運行在多節點環境中
    當 其中一個計算節點發生故障
    那麼 其他節點應該能夠接管失敗節點的任務
    而且 正在執行的計算應該能夠在其他節點上恢復
    而且 系統整體可用性應該保持在99%以上
    而且 故障恢復時間應該在5分鐘內

  場景: 資源使用限制和配額管理
    假設 系統需要限制資源使用
    當 配置資源限制參數
    那麼 單個計算任務的記憶體使用不應該超過500MB
    而且 單個計算任務的執行時間不應該超過30分鐘
    而且 同時執行的計算任務數量應該可配置
    而且 超出限制的任務應該被自動終止

  場景: 效能基準測試
    假設 在標準測試環境中執行效能測試
    當 執行完整的1350個參數組合計算
    那麼 總執行時間應該在8小時內完成
    而且 平均每個參數組合的計算時間應該在20秒內
    而且 系統應該能夠穩定運行不出現崩潰
    而且 記憶體洩漏應該控制在可接受範圍內

  場景: 網路頻寬使用優化
    假設 系統需要從遠端資料庫載入大量歷史資料
    當 載入300期的開獎資料
    那麼 應該使用資料壓縮減少網路傳輸量
    而且 應該實作資料分頁避免一次載入過多資料
    而且 應該使用連接池減少連接建立的開銷
    而且 網路傳輸時間應該在總執行時間的10%以內

  場景: 擴展性測試 - 增加計算複雜度
    假設 未來需要支援更複雜的分析演算法
    而且 計算時間可能增加5倍
    當 系統需要處理更複雜的計算
    那麼 應該能夠透過增加並行度來維持總執行時間
    而且 應該能夠透過分散式計算來處理更大的工作負載
    而且 系統架構應該支援水平擴展

  場景: 效能回歸測試
    假設 系統進行了程式碼更新
    當 執行效能回歸測試
    那麼 新版本的執行時間不應該比舊版本增加20%以上
    而且 記憶體使用量不應該比舊版本增加30%以上
    而且 計算結果的準確性應該保持一致
    而且 系統穩定性不應該下降

  場景: 即時效能監控和告警
    假設 系統配置了效能監控告警
    當 效能指標超出正常範圍
    那麼 應該根據嚴重程度觸發不同級別的告警:
      | 指標異常情況              | 告警級別 | 處理動作                 |
      | 記憶體使用率 > 85%        | WARNING  | 記錄日誌，發送通知       |
      | CPU使用率 > 90%           | ERROR    | 降低並行度，發送告警     |
      | 計算任務執行時間 > 1小時   | WARNING  | 檢查任務狀態             |
      | 資料庫查詢時間 > 10秒     | ERROR    | 檢查資料庫連接和索引     |
      | 系統無回應 > 5分鐘        | CRITICAL | 重啟服務，緊急通知       |
