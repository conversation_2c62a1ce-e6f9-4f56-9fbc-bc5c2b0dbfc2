package config

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// Config 排程服務配置
type Config struct {
	// 資料庫配置
	Database DatabaseConfig `json:"database"`
	
	// 排程配置
	Scheduler SchedulerConfig `json:"scheduler"`
	
	// 計算配置
	Calculation CalculationConfig `json:"calculation"`
	
	// 記憶體管理配置
	Memory MemoryConfig `json:"memory"`
	
	// 日誌配置
	Logging LoggingConfig `json:"logging"`
	
	// 通知配置
	Notification NotificationConfig `json:"notification"`
}

// DatabaseConfig 資料庫配置
type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Database string `json:"database"`
	Charset  string `json:"charset"`
	Timezone string `json:"timezone"`
	
	// 連接池配置
	MaxOpenConns    int           `json:"max_open_conns"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
}

// SchedulerConfig 排程配置
type SchedulerConfig struct {
	// 檢查新開獎號碼的時間 (cron 表達式)
	CheckSchedule string `json:"check_schedule"`
	
	// 時區
	Timezone string `json:"timezone"`
	
	// 是否啟用排程
	Enabled bool `json:"enabled"`
	
	// 支援的彩種列表
	SupportedLotteries []string `json:"supported_lotteries"`
	
	// 任務優先級設定
	TaskPriorities map[string]int `json:"task_priorities"`
}

// CalculationConfig 計算配置
type CalculationConfig struct {
	// 最大並行計算數量
	MaxConcurrentCalculations int `json:"max_concurrent_calculations"`
	
	// 單個計算的超時時間
	CalculationTimeout time.Duration `json:"calculation_timeout"`
	
	// 重試配置
	RetryLimit    int           `json:"retry_limit"`
	RetryInterval time.Duration `json:"retry_interval"`
	
	// 批次處理大小
	BatchSize int `json:"batch_size"`
	
	// 分析期數 (固定50期)
	AnalysisPeriods int `json:"analysis_periods"`
	
	// 參數組合配置
	ParameterCombinations ParameterConfig `json:"parameter_combinations"`
	
	// 工作程序配置
	WorkerConfig WorkerConfig `json:"worker_config"`
}

// ParameterConfig 參數組合配置
type ParameterConfig struct {
	// 拖牌組合範圍
	CombRange []int `json:"comb_range"`
	
	// 推算期數列表
	PeriodNumbers []int `json:"period_numbers"`
	
	// 拖牌區間列表
	MaxRanges []int `json:"max_ranges"`
	
	// 篩選條件
	FilterConditions FilterConfig `json:"filter_conditions"`
}

// FilterConfig 篩選條件配置
type FilterConfig struct {
	ConsecutiveHits           int     `json:"consecutive_hits"`
	ConsecutiveFilterCondition string  `json:"consecutive_filter_condition"`
	TargetProbability         string  `json:"target_probability"`
	ProbabilityFilterCondition string  `json:"probability_filter_condition"`
	StatisticType             string  `json:"statistic_type"`
}

// WorkerConfig 工作程序配置
type WorkerConfig struct {
	// 工作程序數量
	WorkerCount int `json:"worker_count"`
	
	// 工作程序超時時間
	WorkerTimeout time.Duration `json:"worker_timeout"`
	
	// 工作程序重啟間隔
	RestartInterval time.Duration `json:"restart_interval"`
	
	// 工作程序ID前綴
	WorkerIDPrefix string `json:"worker_id_prefix"`
}

// MemoryConfig 記憶體管理配置
type MemoryConfig struct {
	// 記憶體使用閾值 (百分比)
	ThresholdPercent int `json:"threshold_percent"`
	
	// 自動垃圾回收
	AutoGCEnabled bool `json:"auto_gc_enabled"`
	
	// 垃圾回收間隔
	GCInterval time.Duration `json:"gc_interval"`
	
	// 記憶體監控間隔
	MonitorInterval time.Duration `json:"monitor_interval"`
	
	// 最大記憶體使用量 (MB)
	MaxMemoryMB int `json:"max_memory_mb"`
}

// LoggingConfig 日誌配置
type LoggingConfig struct {
	// 日誌級別
	Level string `json:"level"`
	
	// 日誌格式 (json/text)
	Format string `json:"format"`
	
	// 日誌輸出路徑
	OutputPath string `json:"output_path"`
	
	// 日誌檔案最大大小 (MB)
	MaxSizeMB int `json:"max_size_mb"`
	
	// 保留的日誌檔案數量
	MaxBackups int `json:"max_backups"`
	
	// 日誌檔案保留天數
	MaxAgeDays int `json:"max_age_days"`
	
	// 是否壓縮舊日誌
	Compress bool `json:"compress"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	// 是否啟用通知
	Enabled bool `json:"enabled"`
	
	// 郵件通知配置
	Email EmailConfig `json:"email"`
	
	// 通知級別
	NotificationLevels []string `json:"notification_levels"`
	
	// 通知接收者
	Recipients []string `json:"recipients"`
}

// EmailConfig 郵件配置
type EmailConfig struct {
	SMTPHost     string `json:"smtp_host"`
	SMTPPort     int    `json:"smtp_port"`
	Username     string `json:"username"`
	Password     string `json:"password"`
	FromAddress  string `json:"from_address"`
	FromName     string `json:"from_name"`
	UseTLS       bool   `json:"use_tls"`
	UseSSL       bool   `json:"use_ssl"`
}

// LoadConfig 載入配置檔案
func LoadConfig(configPath string) (*Config, error) {
	// 如果沒有指定配置檔案路徑，使用預設路徑
	if configPath == "" {
		configPath = "config/ball_follow_scheduler.json"
	}
	
	// 讀取配置檔案
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}
	
	// 解析配置
	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}
	
	// 驗證配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}
	
	return &config, nil
}

// Validate 驗證配置的有效性
func (c *Config) Validate() error {
	// 驗證資料庫配置
	if c.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}
	if c.Database.Database == "" {
		return fmt.Errorf("database name is required")
	}
	
	// 驗證排程配置
	if c.Scheduler.CheckSchedule == "" {
		return fmt.Errorf("scheduler check_schedule is required")
	}
	
	// 驗證計算配置
	if c.Calculation.MaxConcurrentCalculations <= 0 {
		return fmt.Errorf("max_concurrent_calculations must be greater than 0")
	}
	if c.Calculation.AnalysisPeriods <= 0 {
		return fmt.Errorf("analysis_periods must be greater than 0")
	}
	
	// 驗證記憶體配置
	if c.Memory.ThresholdPercent <= 0 || c.Memory.ThresholdPercent > 100 {
		return fmt.Errorf("memory threshold_percent must be between 1 and 100")
	}
	
	return nil
}

// GetDefaultConfig 取得預設配置
func GetDefaultConfig() *Config {
	return &Config{
		Database: DatabaseConfig{
			Host:            "localhost",
			Port:            3306,
			Username:        "lottery",
			Password:        "password",
			Database:        "lottery",
			Charset:         "utf8mb4",
			Timezone:        "Asia/Taipei",
			MaxOpenConns:    10,
			MaxIdleConns:    5,
			ConnMaxLifetime: time.Hour,
		},
		Scheduler: SchedulerConfig{
			CheckSchedule:      "30 22 * * *", // 每晚 10:30
			Timezone:           "Asia/Taipei",
			Enabled:            true,
			SupportedLotteries: []string{"daily539"},
			TaskPriorities: map[string]int{
				"daily539":       1,
				"lotto649":       2,
				"super_lotto638": 3,
				"lotto_hk":       4,
			},
		},
		Calculation: CalculationConfig{
			MaxConcurrentCalculations: 3,
			CalculationTimeout:        30 * time.Minute,
			RetryLimit:                3,
			RetryInterval:             5 * time.Second,
			BatchSize:                 50,
			AnalysisPeriods:           50,
			ParameterCombinations: ParameterConfig{
				CombRange:     []int{1, 2, 3},
				PeriodNumbers: []int{30, 60, 90, 120, 150, 180, 210, 240, 270, 300},
				MaxRanges:     []int{10, 15, 20, 25, 30},
				FilterConditions: FilterConfig{
					ConsecutiveHits:            0,
					ConsecutiveFilterCondition: "above",
					TargetProbability:          "all",
					ProbabilityFilterCondition: "above",
					StatisticType:              "group",
				},
			},
			WorkerConfig: WorkerConfig{
				WorkerCount:     3,
				WorkerTimeout:   30 * time.Minute,
				RestartInterval: 5 * time.Minute,
				WorkerIDPrefix:  "bf-worker",
			},
		},
		Memory: MemoryConfig{
			ThresholdPercent: 80,
			AutoGCEnabled:    true,
			GCInterval:       5 * time.Minute,
			MonitorInterval:  1 * time.Minute,
			MaxMemoryMB:      2048,
		},
		Logging: LoggingConfig{
			Level:      "info",
			Format:     "json",
			OutputPath: "logs/ball_follow_scheduler.log",
			MaxSizeMB:  100,
			MaxBackups: 10,
			MaxAgeDays: 30,
			Compress:   true,
		},
		Notification: NotificationConfig{
			Enabled:            true,
			NotificationLevels: []string{"error", "critical"},
			Recipients:         []string{"<EMAIL>"},
			Email: EmailConfig{
				SMTPHost:    "smtp.gmail.com",
				SMTPPort:    587,
				UseTLS:      true,
				FromName:    "Ball Follow Scheduler",
			},
		},
	}
}

// SaveConfig 儲存配置到檔案
func (c *Config) SaveConfig(configPath string) error {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}
	
	return os.WriteFile(configPath, data, 0644)
}
