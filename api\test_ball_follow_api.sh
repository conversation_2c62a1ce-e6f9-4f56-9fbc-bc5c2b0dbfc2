#!/bin/bash

# 版路分析API測試腳本

# 設定變數
BASE_URL="http://localhost:8080"
API_BASE="${BASE_URL}/api"
ADMIN_API="${API_BASE}/admin/ball-follow"
USER_API="${API_BASE}/ball-follow"

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 測試函數
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    local expected_status=${5:-200}

    echo ""
    log_info "測試: $description"
    log_debug "URL: $method $url"
    
    if [ -n "$data" ]; then
        log_debug "Data: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
            "$url")
    fi

    # 分離回應內容和狀態碼
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)

    if [ "$http_code" -eq "$expected_status" ]; then
        log_info "✅ 測試通過 (HTTP $http_code)"
        echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
    else
        log_error "❌ 測試失敗 (期望: $expected_status, 實際: $http_code)"
        echo "$response_body"
    fi
}

# 健康檢查測試
test_health_check() {
    echo ""
    echo "=========================================="
    echo "健康檢查測試"
    echo "=========================================="
    
    test_api "GET" "${USER_API}/health" "" "健康檢查"
    test_api "GET" "${USER_API}/version" "" "版本資訊"
}

# 管理員API測試
test_admin_apis() {
    echo ""
    echo "=========================================="
    echo "管理員API測試"
    echo "=========================================="
    
    # 系統狀態
    test_api "GET" "${ADMIN_API}/system-status" "" "取得系統狀態"
    
    # 任務列表
    test_api "GET" "${ADMIN_API}/tasks" "" "取得任務列表"
    test_api "GET" "${ADMIN_API}/tasks?status=completed&limit=5" "" "取得已完成任務列表"
    
    # 手動觸發計算
    trigger_data='{
        "lotto_type": "daily539",
        "period": 113001,
        "force_recalculate": false
    }'
    test_api "POST" "${ADMIN_API}/trigger" "$trigger_data" "手動觸發計算" 201
    
    # 查詢結果
    test_api "GET" "${ADMIN_API}/results?lotto_type=daily539&period=113001" "" "查詢計算結果"
    test_api "GET" "${ADMIN_API}/summary?lotto_type=daily539&period=113001" "" "查詢結果摘要"
    
    # 批量查詢
    batch_data='{
        "lotto_type": "daily539",
        "periods": [113001, 113002, 113003]
    }'
    test_api "POST" "${ADMIN_API}/batch-results" "$batch_data" "批量查詢結果"
    
    # 歷史記錄
    test_api "GET" "${ADMIN_API}/history?lotto_type=daily539&limit=10" "" "查詢歷史記錄"
    
    # 系統配置
    config_data='{
        "max_concurrent_calculations": 3,
        "memory_threshold_percent": 80,
        "auto_gc_enabled": true
    }'
    test_api "PUT" "${ADMIN_API}/config" "$config_data" "更新系統配置"
}

# 用戶API測試
test_user_apis() {
    echo ""
    echo "=========================================="
    echo "用戶API測試"
    echo "=========================================="
    
    # 查詢任務狀態
    test_api "GET" "${USER_API}/task-status?lotto_type=daily539&period=113001" "" "查詢任務狀態"
    
    # 查詢結果
    test_api "GET" "${USER_API}/results?lotto_type=daily539&period=113001" "" "查詢計算結果"
    test_api "GET" "${USER_API}/summary?lotto_type=daily539&period=113001" "" "查詢結果摘要"
}

# 匯出功能測試
test_export_apis() {
    echo ""
    echo "=========================================="
    echo "匯出功能測試"
    echo "=========================================="
    
    # 下載Excel報表
    log_info "測試: 下載Excel報表"
    curl -s -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
        "${ADMIN_API}/export?lotto_type=daily539&period=113001&format=excel" \
        -o "test_export.xlsx"
    
    if [ -f "test_export.xlsx" ]; then
        file_size=$(stat -c%s "test_export.xlsx" 2>/dev/null || stat -f%z "test_export.xlsx" 2>/dev/null)
        if [ "$file_size" -gt 0 ]; then
            log_info "✅ Excel檔案下載成功 (大小: $file_size bytes)"
            rm -f "test_export.xlsx"
        else
            log_error "❌ Excel檔案為空"
        fi
    else
        log_error "❌ Excel檔案下載失敗"
    fi
}

# 錯誤處理測試
test_error_handling() {
    echo ""
    echo "=========================================="
    echo "錯誤處理測試"
    echo "=========================================="
    
    # 無效參數
    test_api "GET" "${ADMIN_API}/results" "" "缺少必要參數" 400
    test_api "GET" "${ADMIN_API}/results?lotto_type=invalid&period=123" "" "無效彩種" 400
    
    # 不存在的資源
    test_api "GET" "${ADMIN_API}/tasks/999999" "" "不存在的任務" 404
    
    # 無效的JSON
    test_api "POST" "${ADMIN_API}/trigger" '{"invalid": json}' "無效JSON格式" 400
}

# 效能測試
test_performance() {
    echo ""
    echo "=========================================="
    echo "效能測試"
    echo "=========================================="
    
    log_info "測試: API回應時間"
    
    start_time=$(date +%s%N)
    test_api "GET" "${USER_API}/health" "" "健康檢查效能測試"
    end_time=$(date +%s%N)
    
    duration=$((($end_time - $start_time) / 1000000))
    log_info "回應時間: ${duration}ms"
    
    if [ $duration -lt 1000 ]; then
        log_info "✅ 回應時間良好 (<1秒)"
    else
        log_warn "⚠️ 回應時間較慢 (>1秒)"
    fi
}

# 主函數
main() {
    echo "版路分析API測試腳本"
    echo "===================="
    echo "測試目標: $BASE_URL"
    echo ""
    
    # 檢查服務是否運行
    log_info "檢查服務狀態..."
    if ! curl -s "$BASE_URL" > /dev/null; then
        log_error "無法連接到服務，請確認服務已啟動"
        exit 1
    fi
    log_info "服務連接正常"
    
    # 檢查必要工具
    if ! command -v jq &> /dev/null; then
        log_warn "未安裝jq，JSON輸出將不會格式化"
    fi
    
    # 執行測試
    test_health_check
    test_admin_apis
    test_user_apis
    test_export_apis
    test_error_handling
    test_performance
    
    echo ""
    echo "=========================================="
    echo "測試完成"
    echo "=========================================="
    log_info "所有測試已執行完畢"
    log_warn "注意: 某些測試需要有效的JWT token才能正常運行"
    log_warn "請將 'YOUR_JWT_TOKEN_HERE' 替換為實際的JWT token"
}

# 顯示幫助
show_help() {
    echo "版路分析API測試腳本"
    echo ""
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  -h, --help     顯示此幫助訊息"
    echo "  -u, --url URL  設定API基礎URL (預設: http://localhost:8080)"
    echo ""
    echo "範例:"
    echo "  $0                           # 使用預設URL測試"
    echo "  $0 -u http://example.com     # 使用自定義URL測試"
    echo ""
}

# 解析命令列參數
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            BASE_URL="$2"
            API_BASE="${BASE_URL}/api"
            ADMIN_API="${API_BASE}/admin/ball-follow"
            USER_API="${API_BASE}/ball-follow"
            shift 2
            ;;
        *)
            log_error "未知選項: $1"
            show_help
            exit 1
            ;;
    esac
done

# 執行主函數
main
