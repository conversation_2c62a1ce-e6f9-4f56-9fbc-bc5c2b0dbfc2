{"database": {"host": "localhost", "port": 3306, "username": "lottery", "password": "password", "database": "lottery", "charset": "utf8mb4", "timezone": "Asia/Taipei", "max_open_conns": 10, "max_idle_conns": 5, "conn_max_lifetime": "1h"}, "scheduler": {"check_schedule": "30 22 * * *", "timezone": "Asia/Taipei", "enabled": true, "supported_lotteries": ["daily539"], "task_priorities": {"daily539": 1, "lotto649": 2, "super_lotto638": 3, "lotto_hk": 4}}, "calculation": {"max_concurrent_calculations": 3, "calculation_timeout": "30m", "retry_limit": 3, "retry_interval": "5s", "batch_size": 50, "analysis_periods": 50, "parameter_combinations": {"comb_range": [1, 2, 3], "period_numbers": [30, 60, 90, 120, 150, 180, 210, 240, 270, 300], "max_ranges": [10, 15, 20, 25, 30], "filter_conditions": {"consecutive_hits": 0, "consecutive_filter_condition": "above", "target_probability": "all", "probability_filter_condition": "above", "statistic_type": "group"}}, "worker_config": {"worker_count": 3, "worker_timeout": "30m", "restart_interval": "5m", "worker_id_prefix": "bf-worker"}}, "memory": {"threshold_percent": 80, "auto_gc_enabled": true, "gc_interval": "5m", "monitor_interval": "1m", "max_memory_mb": 2048}, "logging": {"level": "info", "format": "json", "output_path": "logs/ball_follow_scheduler.log", "max_size_mb": 100, "max_backups": 10, "max_age_days": 30, "compress": true}, "notification": {"enabled": false, "notification_levels": ["error", "critical"], "recipients": ["<EMAIL>"], "email": {"smtp_host": "smtp.gmail.com", "smtp_port": 587, "username": "", "password": "", "from_address": "", "from_name": "Ball Follow Scheduler", "use_tls": true, "use_ssl": false}}}