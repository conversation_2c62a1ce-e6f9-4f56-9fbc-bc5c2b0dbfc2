package worker

import (
	"context"
	"fmt"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"

	"lottery/ball_follow_scheduler/config"
	"lottery/ball_follow_scheduler/engine"
	. "lottery/models"
)

// Pool 工作程序池
type Pool struct {
	config     config.WorkerConfig
	engine     *engine.CalculationEngine
	repository *BallFollowRepository
	
	// 工作程序管理
	workers    []*Worker
	taskQueue  chan uint64
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	
	// 狀態管理
	mu      sync.RWMutex
	running bool
	
	// 統計資訊
	stats *PoolStats
}

// PoolStats 工作程序池統計資訊
type PoolStats struct {
	TotalTasksProcessed   int64     `json:"total_tasks_processed"`
	TotalTasksSucceeded   int64     `json:"total_tasks_succeeded"`
	TotalTasksFailed      int64     `json:"total_tasks_failed"`
	ActiveWorkers         int       `json:"active_workers"`
	QueueLength           int       `json:"queue_length"`
	AverageProcessingTime time.Duration `json:"average_processing_time"`
	LastTaskTime          time.Time `json:"last_task_time"`
	mu                    sync.RWMutex
}

// Worker 工作程序
type Worker struct {
	id         string
	pool       *Pool
	engine     *engine.CalculationEngine
	repository *BallFollowRepository
	
	// 狀態
	mu           sync.RWMutex
	busy         bool
	currentTask  uint64
	startTime    time.Time
	totalTasks   int64
	successTasks int64
	failedTasks  int64
}

// NewPool 建立新的工作程序池
func NewPool(cfg config.WorkerConfig, engine *engine.CalculationEngine) *Pool {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &Pool{
		config:     cfg,
		engine:     engine,
		taskQueue:  make(chan uint64, cfg.WorkerCount*10), // 緩衝區大小
		ctx:        ctx,
		cancel:     cancel,
		stats:      &PoolStats{},
	}
}

// Start 啟動工作程序池
func (p *Pool) Start(ctx context.Context) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	if p.running {
		return fmt.Errorf("worker pool is already running")
	}
	
	log.Infof("Starting worker pool with %d workers", p.config.WorkerCount)
	
	// 建立工作程序
	p.workers = make([]*Worker, p.config.WorkerCount)
	for i := 0; i < p.config.WorkerCount; i++ {
		workerID := fmt.Sprintf("%s-%03d", p.config.WorkerIDPrefix, i+1)
		p.workers[i] = NewWorker(workerID, p, p.engine, p.repository)
		
		// 啟動工作程序
		p.wg.Add(1)
		go p.workers[i].Run(p.ctx)
	}
	
	// 啟動統計監控
	p.wg.Add(1)
	go p.statsMonitor()
	
	p.running = true
	
	log.Info("Worker pool started successfully")
	return nil
}

// Stop 停止工作程序池
func (p *Pool) Stop() error {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	if !p.running {
		return nil
	}
	
	log.Info("Stopping worker pool...")
	
	// 關閉任務佇列
	close(p.taskQueue)
	
	// 取消上下文
	p.cancel()
	
	// 等待所有工作程序結束
	p.wg.Wait()
	
	p.running = false
	
	log.Info("Worker pool stopped")
	return nil
}

// SubmitTask 提交任務到工作程序池
func (p *Pool) SubmitTask(taskID uint64) error {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	if !p.running {
		return fmt.Errorf("worker pool is not running")
	}
	
	select {
	case p.taskQueue <- taskID:
		log.Debugf("Task %d submitted to worker pool", taskID)
		return nil
	default:
		return fmt.Errorf("task queue is full")
	}
}

// GetStats 取得工作程序池統計資訊
func (p *Pool) GetStats() *PoolStats {
	p.stats.mu.RLock()
	defer p.stats.mu.RUnlock()
	
	// 更新即時統計
	p.stats.QueueLength = len(p.taskQueue)
	p.stats.ActiveWorkers = p.getActiveWorkerCount()
	
	// 返回副本
	statsCopy := *p.stats
	return &statsCopy
}

// GetWorkerStats 取得所有工作程序的統計資訊
func (p *Pool) GetWorkerStats() []*WorkerStats {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	var stats []*WorkerStats
	for _, worker := range p.workers {
		stats = append(stats, worker.GetStats())
	}
	
	return stats
}

// statsMonitor 統計監控協程
func (p *Pool) statsMonitor() {
	defer p.wg.Done()
	
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-p.ctx.Done():
			return
		case <-ticker.C:
			p.updatePoolStats()
		}
	}
}

// updatePoolStats 更新池統計資訊
func (p *Pool) updatePoolStats() {
	p.stats.mu.Lock()
	defer p.stats.mu.Unlock()
	
	var totalTasks, successTasks, failedTasks int64
	var totalProcessingTime time.Duration
	
	p.mu.RLock()
	for _, worker := range p.workers {
		workerStats := worker.GetStats()
		totalTasks += workerStats.TotalTasks
		successTasks += workerStats.SuccessTasks
		failedTasks += workerStats.FailedTasks
		totalProcessingTime += workerStats.AverageProcessingTime
	}
	p.mu.RUnlock()
	
	p.stats.TotalTasksProcessed = totalTasks
	p.stats.TotalTasksSucceeded = successTasks
	p.stats.TotalTasksFailed = failedTasks
	
	if len(p.workers) > 0 {
		p.stats.AverageProcessingTime = totalProcessingTime / time.Duration(len(p.workers))
	}
}

// getActiveWorkerCount 取得活躍工作程序數量
func (p *Pool) getActiveWorkerCount() int {
	count := 0
	p.mu.RLock()
	for _, worker := range p.workers {
		if worker.IsBusy() {
			count++
		}
	}
	p.mu.RUnlock()
	return count
}

// WorkerStats 工作程序統計資訊
type WorkerStats struct {
	ID                    string        `json:"id"`
	Busy                  bool          `json:"busy"`
	CurrentTask           uint64        `json:"current_task"`
	TotalTasks            int64         `json:"total_tasks"`
	SuccessTasks          int64         `json:"success_tasks"`
	FailedTasks           int64         `json:"failed_tasks"`
	AverageProcessingTime time.Duration `json:"average_processing_time"`
	LastTaskTime          time.Time     `json:"last_task_time"`
}

// NewWorker 建立新的工作程序
func NewWorker(id string, pool *Pool, engine *engine.CalculationEngine, repo *BallFollowRepository) *Worker {
	return &Worker{
		id:         id,
		pool:       pool,
		engine:     engine,
		repository: repo,
	}
}

// Run 運行工作程序
func (w *Worker) Run(ctx context.Context) {
	defer w.pool.wg.Done()
	
	log.Infof("Worker %s started", w.id)
	
	for {
		select {
		case <-ctx.Done():
			log.Infof("Worker %s stopped", w.id)
			return
		case taskID, ok := <-w.pool.taskQueue:
			if !ok {
				log.Infof("Worker %s: task queue closed", w.id)
				return
			}
			
			w.processTask(ctx, taskID)
		}
	}
}

// processTask 處理任務
func (w *Worker) processTask(ctx context.Context, taskID uint64) {
	w.setBusy(true, taskID)
	defer w.setBusy(false, 0)
	
	startTime := time.Now()
	
	log.Infof("Worker %s: processing task %d", w.id, taskID)
	
	// 取得待處理的子任務
	subtasks, err := w.repository.GetPendingSubtasks(taskID, w.pool.config.WorkerCount)
	if err != nil {
		log.Errorf("Worker %s: failed to get pending subtasks for task %d: %v", w.id, taskID, err)
		w.incrementFailedTasks()
		return
	}
	
	if len(subtasks) == 0 {
		log.Infof("Worker %s: no pending subtasks for task %d", w.id, taskID)
		return
	}
	
	// 處理每個子任務
	successCount := 0
	for _, subtask := range subtasks {
		select {
		case <-ctx.Done():
			log.Infof("Worker %s: context cancelled while processing subtask %d", w.id, subtask.ID)
			return
		default:
		}
		
		// 執行子任務計算
		result := w.engine.CalculateSubtask(ctx, subtask.ID, w.id)
		
		if result.Success {
			successCount++
			log.Debugf("Worker %s: subtask %d completed successfully", w.id, subtask.ID)
		} else {
			log.Errorf("Worker %s: subtask %d failed: %v", w.id, subtask.ID, result.Error)
		}
	}
	
	duration := time.Since(startTime)
	
	// 更新統計資訊
	w.updateStats(len(subtasks), successCount, duration)
	
	log.Infof("Worker %s: completed task %d with %d/%d successful subtasks in %v",
		w.id, taskID, successCount, len(subtasks), duration)
	
	// 檢查任務是否還有待處理的子任務
	remainingSubtasks, err := w.repository.GetPendingSubtasks(taskID, 1)
	if err == nil && len(remainingSubtasks) > 0 {
		// 重新提交任務到佇列
		if err := w.pool.SubmitTask(taskID); err != nil {
			log.Errorf("Worker %s: failed to resubmit task %d: %v", w.id, taskID, err)
		}
	}
}

// setBusy 設定工作程序忙碌狀態
func (w *Worker) setBusy(busy bool, taskID uint64) {
	w.mu.Lock()
	defer w.mu.Unlock()
	
	w.busy = busy
	w.currentTask = taskID
	if busy {
		w.startTime = time.Now()
	}
}

// IsBusy 檢查工作程序是否忙碌
func (w *Worker) IsBusy() bool {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.busy
}

// updateStats 更新工作程序統計資訊
func (w *Worker) updateStats(totalSubtasks, successSubtasks int, duration time.Duration) {
	w.mu.Lock()
	defer w.mu.Unlock()
	
	w.totalTasks += int64(totalSubtasks)
	w.successTasks += int64(successSubtasks)
	w.failedTasks += int64(totalSubtasks - successSubtasks)
	
	// 更新平均處理時間
	if w.totalTasks > 0 {
		totalTime := time.Duration(w.totalTasks-int64(totalSubtasks)) * w.getAverageProcessingTime()
		w.setAverageProcessingTime((totalTime + duration) / time.Duration(w.totalTasks))
	} else {
		w.setAverageProcessingTime(duration)
	}
}

// incrementFailedTasks 增加失敗任務計數
func (w *Worker) incrementFailedTasks() {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.failedTasks++
}

// GetStats 取得工作程序統計資訊
func (w *Worker) GetStats() *WorkerStats {
	w.mu.RLock()
	defer w.mu.RUnlock()
	
	return &WorkerStats{
		ID:                    w.id,
		Busy:                  w.busy,
		CurrentTask:           w.currentTask,
		TotalTasks:            w.totalTasks,
		SuccessTasks:          w.successTasks,
		FailedTasks:           w.failedTasks,
		AverageProcessingTime: w.getAverageProcessingTime(),
	}
}

// 私有方法（需要在鎖內調用）
func (w *Worker) getAverageProcessingTime() time.Duration {
	// 這個方法假設已經在鎖內調用
	return time.Duration(0) // 簡化實現，實際應該計算平均時間
}

func (w *Worker) setAverageProcessingTime(duration time.Duration) {
	// 這個方法假設已經在鎖內調用
	// 簡化實現，實際應該存儲平均時間
}
