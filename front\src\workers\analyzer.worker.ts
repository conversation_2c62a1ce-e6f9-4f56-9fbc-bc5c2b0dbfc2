import {
  AnalysisConfig,
  DrawResult,
  StatResult,
  ProgressInfo,
  Occurrence,
} from '@/models/types';

// 組合 Key 生成器 - 使用字串 key 確保唯一性，避免 hash 衝突
class CombinationKeyGenerator {
  static generateKey(
    firstGroup: number[] | Uint8Array | Uint16Array,
    secondGroup: number[] | Uint8Array | Uint16Array,
    gap: number,
    targetGap: number
  ): string {
    // 確保組合內部排序一致，避免 [1,2] 和 [2,1] 產生不同 key
    const sortedFirst = Array.from(firstGroup).sort((a, b) => a - b);
    const sortedSecond = Array.from(secondGroup).sort((a, b) => a - b);

    return `${sortedFirst.join(',')}-${sortedSecond.join(',')}-${gap}-${targetGap}`;
  }

  static generateFullKey(
    firstGroup: number[] | Uint8Array | Uint16Array,
    secondGroup: number[] | Uint8Array | Uint16Array,
    targetGroup: number[] | Uint8Array | Uint16Array,
    gap: number,
    targetGap: number
  ): string {
    // 確保組合內部排序一致
    const sortedFirst = Array.from(firstGroup).sort((a, b) => a - b);
    const sortedSecond = Array.from(secondGroup).sort((a, b) => a - b);
    const sortedTarget = Array.from(targetGroup).sort((a, b) => a - b);

    return `${sortedFirst.join(',')}-${sortedSecond.join(',')}-${sortedTarget.join(',')}-${gap}-${targetGap}`;
  }
}

// 擴展 Performance 接口以包含 memory 屬性
interface ExtendedPerformance extends Performance {
  memory?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

// 動態批次大小調整器
class DynamicBatchSizer {
  private currentBatchSize = 10;
  private lastMemoryUsage = 0;
  private memoryThreshold = 100 * 1024 * 1024; // 100MB 閾值
  private minBatchSize = 1;
  private maxBatchSize = 50;

  adjustBatchSize(currentMemory: number): number {
    if (currentMemory > 0) {
      // 記憶體使用增加超過 50%，減少批次大小
      if (currentMemory > this.lastMemoryUsage * 1.5) {
        this.currentBatchSize = Math.max(this.minBatchSize, Math.floor(this.currentBatchSize * 0.8));
      }
      // 記憶體使用減少超過 20%，增加批次大小
      else if (currentMemory < this.lastMemoryUsage * 0.8) {
        this.currentBatchSize = Math.min(this.maxBatchSize, Math.floor(this.currentBatchSize * 1.2));
      }

      // 記憶體使用超過閾值，強制減少批次大小
      if (currentMemory > this.memoryThreshold) {
        this.currentBatchSize = Math.max(this.minBatchSize, Math.floor(this.currentBatchSize * 0.5));
      }

      this.lastMemoryUsage = currentMemory;
    }

    return this.currentBatchSize;
  }

  getCurrentBatchSize(): number {
    return this.currentBatchSize;
  }
}

// 性能監控工具
class PerformanceMonitor {
  private startTime = 0;
  private memorySnapshots: Array<{time: number, memory: number}> = [];
  private isMemorySupported = false;

  start(): void {
    this.startTime = performance.now();
    this.memorySnapshots = [];
    this.isMemorySupported = this.checkMemorySupport();
    this.recordMemory();
  }

  private checkMemorySupport(): boolean {
    const extendedPerformance = performance as ExtendedPerformance;
    return extendedPerformance.memory !== undefined;
  }

  recordMemory(): void {
    if (this.isMemorySupported) {
      const extendedPerformance = performance as ExtendedPerformance;
      const currentTime = performance.now() - this.startTime;
      this.memorySnapshots.push({
        time: currentTime,
        memory: extendedPerformance.memory?.usedJSHeapSize || 0
      });
    }
  }

  getStats(): {duration: number, peakMemory: number, currentMemory: number, memorySupported: boolean} {
    const duration = performance.now() - this.startTime;

    if (this.isMemorySupported) {
      const extendedPerformance = performance as ExtendedPerformance;
      const memories = this.memorySnapshots.map(s => s.memory);
      const peakMemory = memories.length > 0 ? Math.max(...memories) : 0;
      const currentMemory = extendedPerformance.memory?.usedJSHeapSize || 0;

      return { duration, peakMemory, currentMemory, memorySupported: true };
    } else {
      return { duration, peakMemory: 0, currentMemory: 0, memorySupported: false };
    }
  }
}

// Worker 事件處理
self.onmessage = (e) => {
  const { type, data } = e.data;

  if (type === 'init') {
    const results: DrawResult[] = JSON.parse(data.results);
    const config: AnalysisConfig = JSON.parse(data.config);
    analyzeLotto(results, config);
  }
};

function analyzeLotto(results: DrawResult[], config: AnalysisConfig) {
  // 使用分層結果管理器和各種優化技術
  const resultManager = new ResultLayerManager();
  const statResults = new Map<string, OptimizedStatResult>(); // 使用字串 key 避免 hash 衝突
  const occurrenceResults = new Map<string, Occurrence>(); // 使用字串 key 避免 hash 衝突
  const hitDetails = new Map<string, Set<string>>(); // 使用字串 key 避免 hash 衝突

  const predictIndex = results.length - 1 + config.lookAheadCount;
  const totalProgress = estimateTotalProgress(results, config);
  let progress = 0;

  // 性能監控和動態批次調整
  const monitor = new PerformanceMonitor();
  const batchSizer = new DynamicBatchSizer();
  monitor.start();

  // 先一次性計算完所有 Occurrence 統計
  function calculateOccurrences() {
    postMessage({ type: 'progress', data: { stage: 'calculating_occurrences', progress: 0, total: 100 } });

    for (let i = 0; i < results.length - config.lookAheadCount; i++) {
      // 第一組號碼組合 - 需要轉為陣列因為會被重複使用
      const firstGroupsGen = getCombinationsGenerator(
        results[i].numbers,
        config.firstGroupSize
      );
      const firstGroups = Array.from(firstGroupsGen);

      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        // 第二組號碼組合 - 需要轉為陣列因為會被重複使用
        const secondGroupsGen = getCombinationsGenerator(
          results[j].numbers,
          config.secondGroupSize
        );
        const secondGroups = Array.from(secondGroupsGen);
        const gap = j - i;

        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k !== predictIndex) continue;
          if (k > predictIndex) break;

          const targetGap = k - j;

          // 計算 Occurrence 統計
          for (const firstGroup of firstGroups) {
            for (const secondGroup of secondGroups) {
              const key = CombinationKeyGenerator.generateKey(
                firstGroup,
                secondGroup,
                gap,
                targetGap
              );

              const occurrence: Occurrence = {
                count: 0,
                periods: [],
                isPredict: true,
              };

              occurrenceResults.set(key, occurrence);
            }
          }
        }
      }
    }

    for (let i = 0; i < results.length - config.lookAheadCount; i++) {
      // 第一組號碼組合 - 需要轉為陣列因為會被重複使用
      const firstGroupsGen = getCombinationsGenerator(
        results[i].numbers,
        config.firstGroupSize
      );
      const firstGroups = Array.from(firstGroupsGen);

      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        // 第二組號碼組合 - 需要轉為陣列因為會被重複使用
        const secondGroupsGen = getCombinationsGenerator(
          results[j].numbers,
          config.secondGroupSize
        );
        const secondGroups = Array.from(secondGroupsGen);
        const gap = j - i;

        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;

          const targetGap = k - j;

          // 計算 Occurrence 統計
          for (const firstGroup of firstGroups) {
            for (const secondGroup of secondGroups) {
              const key = CombinationKeyGenerator.generateKey(
                firstGroup,
                secondGroup,
                gap,
                targetGap
              );

              const occurrence = occurrenceResults.get(key);

              if (!occurrence) continue;

              if (k < results.length) {
                occurrence.count++;
                occurrence.periods.push({
                  firstPeriod: results[i].period,
                  secondPeriod: results[j].period,
                  targetPeriod: results[k].period,
                });
              } else if (k === predictIndex) {
                occurrence.periods.push({
                  firstPeriod: results[i].period,
                  secondPeriod: results[j].period,
                });
              }
              occurrenceResults.set(key, occurrence);
            }
          }
        }
      }
    }
  }

  function processBatch(startIndex: number) {
    // 動態調整批次大小
    monitor.recordMemory();
    const stats = monitor.getStats();
    const batchSize = batchSizer.adjustBatchSize(stats.currentMemory);
    const endIndex = Math.min(results.length - config.lookAheadCount, startIndex + batchSize);

    // 發送批次信息
    if (startIndex % 50 === 0) { // 每50個批次報告一次
      postMessage({
        type: 'batch_info',
        data: {
          batchSize,
          memoryMB: Math.round(stats.currentMemory / 1024 / 1024),
          progress: Math.round((startIndex / (results.length - config.lookAheadCount)) * 100)
        }
      });
    }

    // 處理預測結果 - 只處理當前批次的 i 值
    for (let i = startIndex; i < endIndex; i++) {
      // 第一組號碼組合 - 需要轉為陣列因為會被重複使用
      const firstGroupsGen = getCombinationsGenerator(
        results[i].numbers,
        config.firstGroupSize
      );
      const firstGroups = Array.from(firstGroupsGen);

      // j 迴圈需要處理完整範圍，不受批次限制
      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        // 第二組號碼組合 - 需要轉為陣列因為會被重複使用
        const secondGroupsGen = getCombinationsGenerator(
          results[j].numbers,
          config.secondGroupSize
        );
        const secondGroups = Array.from(secondGroupsGen);
        const gap = j - i;

        // k 迴圈也需要處理完整範圍，不受批次限制
        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;

          const targetGap = k - j;

          let targetGroups: number[][] = [];
          if (k < results.length) {
            const targetGroupsGen = getCombinationsGenerator(
              results[k].numbers,
              config.targetGroupSize
            );
            targetGroups = Array.from(targetGroupsGen);
          }

          // 處理預測結果
          for (const firstGroup of firstGroups) {
            for (const secondGroup of secondGroups) {
              const key = CombinationKeyGenerator.generateKey(
                firstGroup,
                secondGroup,
                gap,
                targetGap
              );

              const occurrence = occurrenceResults.get(key);

              // 只處理已經在 calculateOccurrences 中標記為預測的組合
              if (!occurrence?.isPredict) continue;

              if (k < results.length) {
                for (const targetGroup of targetGroups) {
                  const fullKey = CombinationKeyGenerator.generateFullKey(
                    firstGroup,
                    secondGroup,
                    targetGroup,
                    gap,
                    targetGap
                  );

                  const stat = statResults.get(fullKey);

                  if (stat) {
                    stat.targetMatches++;
                  } else {
                    const newStat: OptimizedStatResult = {
                      firstNumbers: TypedArrayUtils.toTypedArray(firstGroup),
                      secondNumbers: TypedArrayUtils.toTypedArray(secondGroup),
                      targetNumbers: TypedArrayUtils.toTypedArray(targetGroup),
                      gap: gap,
                      targetGap: targetGap,
                      targetMatches: 1,
                      targetProbability: 0,
                      rank: 0,
                      consecutiveHits: 0, // 初始化為0，稍後計算連續拖出次數
                    };

                    statResults.set(fullKey, newStat);
                  }

                  // 追蹤這個組合命中的期號 - 使用 Set 優化查找性能
                  const targetPeriod = results[k].period;

                  if (!hitDetails.has(fullKey)) {
                    hitDetails.set(fullKey, new Set<string>());
                  }

                  const hitSet = hitDetails.get(fullKey);
                  if (hitSet && !hitSet.has(targetPeriod)) {
                    hitSet.add(targetPeriod);
                  }
                }
              }
            }
          }

          progress++;
          postProgress(progress, totalProgress);
        }
      }
    }

    postProgress(progress, totalProgress);

    // 檢查是否還有更多批次需要處理
    if (endIndex < results.length - config.lookAheadCount) {
      setTimeout(() => processBatch(endIndex), 0); // 讓 Worker 釋放資源
    } else {
      finalizeResults();
    }
  }

  function finalizeResults() {
    const results: StatResult[] = [];

    // 記錄性能統計
    monitor.recordMemory();
    const stats = monitor.getStats();

    postMessage({
      type: 'performance',
      data: {
        stage: 'finalizing',
        duration: stats.duration,
        peakMemory: stats.memorySupported ? Math.round(stats.peakMemory / 1024 / 1024) : 0,
        currentMemory: stats.memorySupported ? Math.round(stats.currentMemory / 1024 / 1024) : 0,
        memorySupported: stats.memorySupported
      }
    });

    for (const optimizedStat of statResults.values()) {
      const key = CombinationKeyGenerator.generateKey(
        optimizedStat.firstNumbers,
        optimizedStat.secondNumbers,
        optimizedStat.gap,
        optimizedStat.targetGap
      );

      const occurrence = occurrenceResults.get(key);

      if (!occurrence?.isPredict) continue;
      if (optimizedStat.targetMatches == 0 || occurrence.count == 0) continue;

      // 準確率
      optimizedStat.targetProbability = optimizedStat.targetMatches / occurrence.count;
      // 計算連續拖出次數
      optimizedStat.consecutiveHits = calculateConsecutiveHitsOptimized(optimizedStat, occurrence, hitDetails);

      // 將結果添加到分層管理器
      resultManager.addResult(key, optimizedStat);

      // if (optimizedStat.consecutiveHits < 1) continue;

      // 轉換為標準 StatResult 格式用於輸出
      const stat: StatResult = {
        firstNumbers: TypedArrayUtils.fromTypedArray(optimizedStat.firstNumbers),
        secondNumbers: TypedArrayUtils.fromTypedArray(optimizedStat.secondNumbers),
        targetNumbers: TypedArrayUtils.fromTypedArray(optimizedStat.targetNumbers),
        gap: optimizedStat.gap,
        targetGap: optimizedStat.targetGap,
        targetMatches: optimizedStat.targetMatches,
        targetProbability: optimizedStat.targetProbability,
        rank: optimizedStat.rank,
        consecutiveHits: optimizedStat.consecutiveHits
      };

      results.push(stat);
    }

    // 獲取分層統計信息
    const layerStats = resultManager.getStats();

    // 如果記憶體使用過高，清理低價值結果
    const finalStats = monitor.getStats();
    if (finalStats.memorySupported && finalStats.currentMemory > 200 * 1024 * 1024) { // 200MB 閾值
      const removed = resultManager.cleanupLowValue();
      postMessage({
        type: 'memory_cleanup',
        data: {
          removedLowValueResults: removed,
          memoryMB: Math.round(finalStats.currentMemory / 1024 / 1024)
        }
      });
    }

    const sortedResults = sortAndRankResults(results);

    postMessage({
      type: 'complete',
      data: sortedResults,
      occurrences: occurrenceResults,
      layerStats: layerStats,
      optimizationInfo: {
        version: 'full_phase1-3_v2.0',
        features: ['hash_keys', 'direct_generators', 'set_optimization', 'dynamic_batching', 'typed_arrays', 'layered_storage'],
        memoryOptimized: true
      }
    });
  }

  // 先計算所有 Occurrence 統計，然後開始批次處理
  calculateOccurrences();
  setTimeout(() => processBatch(0), 0);
}

// 計算連續拖出次數 - 優化版本，修復邏輯錯誤
function calculateConsecutiveHitsOptimized(stat: OptimizedStatResult, occurrence: Occurrence, hitDetails: Map<string, Set<string>>): number {
  // 獲取這個特定組合的命中期號列表 - 使用數字 hash 和 Set
  const fullKey = CombinationKeyGenerator.generateFullKey(
    stat.firstNumbers,
    stat.secondNumbers,
    stat.targetNumbers,
    stat.gap,
    stat.targetGap
  );
  const hitPeriodsSet = hitDetails.get(fullKey) || new Set<string>();

  if (hitPeriodsSet.size === 0) {
    return 0;
  }

  // 獲取所有有效的期號（從 occurrence 中獲取，這些是實際發生的期號）
  const validPeriods = occurrence.periods
    .filter(p => p.targetPeriod !== undefined && p.targetPeriod !== null && p.targetPeriod.trim() !== '')
    .map(p => p.targetPeriod as string);

  if (validPeriods.length === 0) {
    return 0;
  }

  // 將期號按時間順序排序（假設期號是數字格式，數字越大越新）
  const sortedPeriods = validPeriods.sort((a, b) => {
    const numA = parseInt(a);
    const numB = parseInt(b);
    // 檢查解析是否成功
    if (isNaN(numA) || isNaN(numB)) {
      return a.localeCompare(b); // 如果不是數字，使用字符串比較
    }
    return numB - numA; // 降序排列，最新的在前面
  });

  // 從最新期號開始計算連續拖出次數
  let consecutiveHits = 0;
  for (const period of sortedPeriods) {
    if (hitPeriodsSet.has(period)) {
      consecutiveHits++;
    } else {
      break; // 遇到未拖出就停止計算
    }
  }

  return consecutiveHits;
}

// 優化的組合生成器 - 使用固定陣列避免重複分配記憶體
function* generateCombinationsGenerator(
  numbers: number[],
  choose: number,
  start = 0,
  current: number[] = [],
  indices: number[] = []
): Generator<number[]> {
  if (current.length === choose) {
    // 只在需要時創建新陣列，並重用 indices 陣列
    yield current.slice();
    return;
  }

  for (let i = start; i < numbers.length; i++) {
    current.push(numbers[i]);
    indices.push(i);
    yield* generateCombinationsGenerator(numbers, choose, i + 1, current, indices);
    current.pop();
    indices.pop();
  }
}

// TypedArray 工具類
class TypedArrayUtils {
  // 將普通數字陣列轉換為 TypedArray
  static toTypedArray(numbers: number[]): Uint8Array | Uint16Array {
    const maxNumber = Math.max(...numbers);
    if (maxNumber <= 255) {
      return new Uint8Array(numbers);
    } else {
      return new Uint16Array(numbers);
    }
  }

  // 將 TypedArray 轉換回普通陣列（用於輸出）
  static fromTypedArray(typedArray: Uint8Array | Uint16Array): number[] {
    return Array.from(typedArray);
  }

  // 檢查是否需要使用 Uint16Array
  static needsUint16(numbers: number[]): boolean {
    return Math.max(...numbers) > 255;
  }
}

// 分層結果儲存結構
interface LayeredResults {
  highValue: Map<string, OptimizedStatResult>;    // 高命中率結果 (>= 80%)
  mediumValue: Map<string, OptimizedStatResult>;  // 中等命中率結果 (50-79%)
  lowValue: Map<string, OptimizedStatResult>;     // 低命中率結果 (< 50%)
}

// 結果分層管理器
class ResultLayerManager {
  private layers: LayeredResults;

  constructor() {
    this.layers = {
      highValue: new Map(),
      mediumValue: new Map(),
      lowValue: new Map()
    };
  }

  // 根據命中率將結果分層儲存
  addResult(key: string, result: OptimizedStatResult): void {
    const probability = result.targetProbability;

    if (probability >= 0.8) {
      this.layers.highValue.set(key, result);
    } else if (probability >= 0.5) {
      this.layers.mediumValue.set(key, result);
    } else {
      this.layers.lowValue.set(key, result);
    }
  }

  // 獲取所有結果（按優先級排序）
  getAllResults(): OptimizedStatResult[] {
    const results: OptimizedStatResult[] = [];

    // 優先返回高價值結果
    results.push(...this.layers.highValue.values());
    results.push(...this.layers.mediumValue.values());
    results.push(...this.layers.lowValue.values());

    return results;
  }

  // 獲取統計信息
  getStats(): {high: number, medium: number, low: number, total: number} {
    return {
      high: this.layers.highValue.size,
      medium: this.layers.mediumValue.size,
      low: this.layers.lowValue.size,
      total: this.layers.highValue.size + this.layers.mediumValue.size + this.layers.lowValue.size
    };
  }

  // 清理低價值結果以節省記憶體
  cleanupLowValue(): number {
    const removed = this.layers.lowValue.size;
    this.layers.lowValue.clear();
    return removed;
  }
}

// 優化的內部 StatResult 結構
interface OptimizedStatResult {
  firstNumbers: Uint8Array | Uint16Array;
  secondNumbers: Uint8Array | Uint16Array;
  targetNumbers: Uint8Array | Uint16Array;
  gap: number;
  targetGap: number;
  targetMatches: number;
  targetProbability: number;
  rank: number;
  consecutiveHits: number;
}

// 組合生成器入口函數 - 直接回傳 generator，不做全量緩存
function getCombinationsGenerator(
  numbers: number[],
  size: number
): Generator<number[]> {
  // 直接回傳 generator，避免記憶體預分配
  return generateCombinationsGenerator(numbers, size);
}

function estimateTotalProgress(results: DrawResult[], config: AnalysisConfig) {
  const predictIndex = results.length - 1 + config.lookAheadCount;
  let total = 0;

  for (let i = 0; i < results.length - config.lookAheadCount; i++) {
    for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
      for (let k = j + 1; k < results.length && k - j <= config.maxRange; k++) {
        if (k > predictIndex) break;

        total++;
      }
    }
  }
  return total;
}

function sortAndRankResults(
  results: StatResult[]
): StatResult[] {
  results.sort((a, b) => {
    const aFirst =
      a.firstNumbers && a.firstNumbers.length > 0
        ? a.firstNumbers[0]
        : Infinity;
    const bFirst =
      b.firstNumbers && b.firstNumbers.length > 0
        ? b.firstNumbers[0]
        : Infinity;

    if (aFirst !== bFirst) return aFirst - bFirst;

    const aSecond =
      a.secondNumbers && a.secondNumbers.length > 0
        ? a.secondNumbers[0]
        : Infinity;
    const bSecond =
      b.secondNumbers && b.secondNumbers.length > 0
        ? b.secondNumbers[0]
        : Infinity;

    if (aSecond !== bSecond) return aSecond - bSecond;

    const aTarget =
      a.targetNumbers && a.targetNumbers.length > 0
        ? a.targetNumbers[0]
        : Infinity;
    const bTarget =
      b.targetNumbers && b.targetNumbers.length > 0
        ? b.targetNumbers[0]
        : Infinity;

    if (aTarget !== bTarget) return aTarget - bTarget;

    if (a.targetProbability !== b.targetProbability) {
      return b.targetProbability - a.targetProbability;
    }

    if (a.consecutiveHits !== b.consecutiveHits) {
      return b.consecutiveHits - a.consecutiveHits;
    }

    if (a.targetMatches !== b.targetMatches) {
      return b.targetMatches - a.targetMatches;
    }

    return a.targetGap - b.targetGap;
  });

  // 加上 rank 屬性 (依照排序後的索引位置)
  return results.map((result, index) => {
    result.rank = index + 1;
    return result;
  });
}

function postProgress(progress: number, total: number) {
  const info: ProgressInfo = {
    stage: 'processing',
    progress,
    total,
  };
  postMessage({ type: 'progress', data: info });
}
