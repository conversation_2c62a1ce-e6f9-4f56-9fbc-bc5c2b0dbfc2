package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// BallFollowRepository 版路分析資料庫操作倉庫
type BallFollowRepository struct {
	db *gorm.DB
}

// NewBallFollowRepository 建立新的版路分析倉庫實例
func NewBallFollowRepository(db *gorm.DB) *BallFollowRepository {
	return &BallFollowRepository{db: db}
}

// CreateAnalysisResult 建立版路分析結果
func (r *BallFollowRepository) CreateAnalysisResult(result *BallFollowAnalysisResult) error {
	return r.db.Create(result).Error
}

// GetAnalysisResult 根據計算雜湊值取得分析結果
func (r *BallFollowRepository) GetAnalysisResult(calculationHash string) (*BallFollowAnalysisResult, error) {
	var result BallFollowAnalysisResult
	err := r.db.Where("calculation_hash = ?", calculationHash).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// GetAnalysisResultsByPeriod 取得特定期數的所有分析結果
func (r *BallFollowRepository) GetAnalysisResultsByPeriod(lottoType LottoTypeStr, period int) ([]BallFollowAnalysisResult, error) {
	var results []BallFollowAnalysisResult
	err := r.db.Where("lotto_type = ? AND period = ?", lottoType, period).
		Order("comb1, comb2, comb3, period_num, max_range").
		Find(&results).Error
	return results, err
}

// GetAnalysisResultsByParameters 根據參數組合取得分析結果
func (r *BallFollowRepository) GetAnalysisResultsByParameters(
	lottoType LottoTypeStr, period int, comb1, comb2, comb3 uint8, periodNum uint16, maxRange uint8) (*BallFollowAnalysisResult, error) {
	var result BallFollowAnalysisResult
	err := r.db.Where("lotto_type = ? AND period = ? AND comb1 = ? AND comb2 = ? AND comb3 = ? AND period_num = ? AND max_range = ?",
		lottoType, period, comb1, comb2, comb3, periodNum, maxRange).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// CheckAnalysisResultExists 檢查分析結果是否已存在
func (r *BallFollowRepository) CheckAnalysisResultExists(calculationHash string) (bool, error) {
	var count int64
	err := r.db.Model(&BallFollowAnalysisResult{}).Where("calculation_hash = ?", calculationHash).Count(&count).Error
	return count > 0, err
}

// CreateCalculationTask 建立計算任務
func (r *BallFollowRepository) CreateCalculationTask(task *BallFollowCalculationTask) error {
	return r.db.Create(task).Error
}

// GetCalculationTask 取得計算任務
func (r *BallFollowRepository) GetCalculationTask(id uint64) (*BallFollowCalculationTask, error) {
	var task BallFollowCalculationTask
	err := r.db.Preload("Subtasks").First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetCalculationTaskByPeriod 根據期數取得計算任務
func (r *BallFollowRepository) GetCalculationTaskByPeriod(lottoType LottoTypeStr, period int) (*BallFollowCalculationTask, error) {
	var task BallFollowCalculationTask
	err := r.db.Where("lotto_type = ? AND period = ?", lottoType, period).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// UpdateCalculationTask 更新計算任務
func (r *BallFollowRepository) UpdateCalculationTask(task *BallFollowCalculationTask) error {
	return r.db.Save(task).Error
}

// GetPendingTasks 取得待處理的任務列表
func (r *BallFollowRepository) GetPendingTasks(limit int) ([]BallFollowCalculationTask, error) {
	var tasks []BallFollowCalculationTask
	err := r.db.Where("task_status = ?", TaskStatusPending).
		Order("priority ASC, created_at ASC").
		Limit(limit).
		Find(&tasks).Error
	return tasks, err
}

// GetRunningTasks 取得正在執行的任務列表
func (r *BallFollowRepository) GetRunningTasks() ([]BallFollowCalculationTask, error) {
	var tasks []BallFollowCalculationTask
	err := r.db.Where("task_status = ?", TaskStatusRunning).Find(&tasks).Error
	return tasks, err
}

// GetTasksByStatus 根據狀態取得任務列表
func (r *BallFollowRepository) GetTasksByStatus(status TaskStatus, limit, offset int) ([]BallFollowCalculationTask, error) {
	var tasks []BallFollowCalculationTask
	query := r.db.Where("task_status = ?", status)
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}
	err := query.Order("created_at DESC").Find(&tasks).Error
	return tasks, err
}

// CreateSubtask 建立子任務
func (r *BallFollowRepository) CreateSubtask(subtask *BallFollowSubtask) error {
	return r.db.Create(subtask).Error
}

// CreateSubtasks 批量建立子任務
func (r *BallFollowRepository) CreateSubtasks(subtasks []BallFollowSubtask) error {
	return r.db.CreateInBatches(subtasks, 100).Error
}

// UpdateSubtask 更新子任務
func (r *BallFollowRepository) UpdateSubtask(subtask *BallFollowSubtask) error {
	return r.db.Save(subtask).Error
}

// GetPendingSubtasks 取得待處理的子任務
func (r *BallFollowRepository) GetPendingSubtasks(taskID uint64, limit int) ([]BallFollowSubtask, error) {
	var subtasks []BallFollowSubtask
	query := r.db.Where("task_id = ? AND subtask_status = ?", taskID, SubtaskStatusPending)
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&subtasks).Error
	return subtasks, err
}

// GetSubtasksByStatus 根據狀態取得子任務
func (r *BallFollowRepository) GetSubtasksByStatus(taskID uint64, status SubtaskStatus) ([]BallFollowSubtask, error) {
	var subtasks []BallFollowSubtask
	err := r.db.Where("task_id = ? AND subtask_status = ?", taskID, status).Find(&subtasks).Error
	return subtasks, err
}

// GetTaskProgress 取得任務進度資訊
func (r *BallFollowRepository) GetTaskProgress(taskID uint64) (*BallFollowCalculationTask, error) {
	var task BallFollowCalculationTask
	err := r.db.Select("id, lotto_type, period, task_status, total_combinations, completed_combinations, failed_combinations, start_time, end_time, estimated_duration, actual_duration").
		First(&task, taskID).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetTaskStatistics 取得任務統計資訊
func (r *BallFollowRepository) GetTaskStatistics(lottoType LottoTypeStr, days int) (map[string]interface{}, error) {
	var stats map[string]interface{}

	// 計算日期範圍
	startDate := time.Now().AddDate(0, 0, -days)

	// 執行統計查詢
	var totalTasks, completedTasks, failedTasks, runningTasks int64

	baseQuery := r.db.Model(&BallFollowCalculationTask{}).Where("lotto_type = ? AND created_at >= ?", lottoType, startDate)

	baseQuery.Count(&totalTasks)
	baseQuery.Where("task_status = ?", TaskStatusCompleted).Count(&completedTasks)
	baseQuery.Where("task_status = ?", TaskStatusFailed).Count(&failedTasks)
	baseQuery.Where("task_status = ?", TaskStatusRunning).Count(&runningTasks)

	// 計算平均執行時間
	var avgDuration float64
	r.db.Model(&BallFollowCalculationTask{}).
		Where("lotto_type = ? AND task_status = ? AND actual_duration IS NOT NULL AND created_at >= ?",
			lottoType, TaskStatusCompleted, startDate).
		Select("AVG(actual_duration)").Scan(&avgDuration)

	stats = map[string]interface{}{
		"total_tasks":     totalTasks,
		"completed_tasks": completedTasks,
		"failed_tasks":    failedTasks,
		"running_tasks":   runningTasks,
		"success_rate":    float64(completedTasks) / float64(totalTasks) * 100,
		"avg_duration":    avgDuration,
	}

	return stats, nil
}

// CleanupOldResults 清理舊的分析結果
func (r *BallFollowRepository) CleanupOldResults(beforeDate time.Time) error {
	return r.db.Where("created_at < ?", beforeDate).Delete(&BallFollowAnalysisResult{}).Error
}

// CleanupOldTasks 清理舊的任務記錄
func (r *BallFollowRepository) CleanupOldTasks(beforeDate time.Time) error {
	// 先刪除子任務
	err := r.db.Where("created_at < ?", beforeDate).Delete(&BallFollowSubtask{}).Error
	if err != nil {
		return err
	}

	// 再刪除主任務
	return r.db.Where("created_at < ?", beforeDate).Delete(&BallFollowCalculationTask{}).Error
}

// GetLatestPeriodByLottoType 取得特定彩種的最新期數
func (r *BallFollowRepository) GetLatestPeriodByLottoType(lottoType LottoTypeStr) (int, error) {
	var period int

	// 根據彩種選擇對應的表格
	var tableName string
	switch lottoType {
	case Daily539Str:
		tableName = "lotto539_results"
	case Lotto649Str:
		tableName = "lotto649_results"
	case SuperLotto638Str:
		tableName = "super_lotto_results"
	case LottoHKStr:
		tableName = "lotto_hk_results"
	case CaLottoStr:
		tableName = "lotto_california_results"
	default:
		return 0, fmt.Errorf("unsupported lotto type: %s", lottoType)
	}

	err := r.db.Table(tableName).Select("period").Order("draw_date DESC, period DESC").Limit(1).Scan(&period).Error
	return period, err
}

// CheckPeriodCalculated 檢查特定期數是否已經計算過
func (r *BallFollowRepository) CheckPeriodCalculated(lottoType LottoTypeStr, period int) (bool, error) {
	var count int64
	err := r.db.Model(&BallFollowCalculationTask{}).
		Where("lotto_type = ? AND period = ? AND task_status IN (?)",
			lottoType, period, []TaskStatus{TaskStatusCompleted, TaskStatusRunning}).
		Count(&count).Error
	return count > 0, err
}

// GetCalculationHistory 取得計算歷史記錄
func (r *BallFollowRepository) GetCalculationHistory(lottoType LottoTypeStr, limit int) ([]BallFollowCalculationTask, error) {
	var tasks []BallFollowCalculationTask
	query := r.db.Where("lotto_type = ?", lottoType).Order("created_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&tasks).Error
	return tasks, err
}

// GetDB 取得資料庫連接（供計算引擎使用）
func (r *BallFollowRepository) GetDB() *gorm.DB {
	return r.db
}

// UpdateSubtaskStatus 更新子任務狀態
func (r *BallFollowRepository) UpdateSubtaskStatus(subtaskID uint64, updates map[string]interface{}) error {
	return r.db.Model(&BallFollowSubtask{}).Where("id = ?", subtaskID).Updates(updates).Error
}

// GetHistoryDataForAnalysis 取得用於分析的歷史資料
func (r *BallFollowRepository) GetHistoryDataForAnalysis(lottoType LottoTypeStr, period int, analysisPeriods int) ([]LottoResult, error) {
	// 根據彩種選擇對應的表格
	var tableName string
	switch lottoType {
	case Daily539Str:
		tableName = "lotto539_results"
	case Lotto649Str:
		tableName = "lotto649_results"
	case SuperLotto638Str:
		tableName = "super_lotto_results"
	case LottoHKStr:
		tableName = "lotto_hk_results"
	case CaLottoStr:
		tableName = "lotto_california_results"
	default:
		return nil, fmt.Errorf("unsupported lotto type: %s", lottoType)
	}

	var results []LottoResult

	// 取得指定期數之前的N期資料
	query := r.db.Table(tableName).
		Select("period, DATE_FORMAT(draw_date, '%Y/%m/%d') AS draw_date, draw_number_size, draw_number_appear").
		Where("period < ?", period).
		Order("period DESC").
		Limit(analysisPeriods)

	// 如果彩種有特別號，也要選取
	if lottoType != Daily539Str && lottoType != CaLottoStr {
		query = query.Select("period, DATE_FORMAT(draw_date, '%Y/%m/%d') AS draw_date, draw_number_size, draw_number_appear, special_number")
	}

	if err := query.Find(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get history data: %w", err)
	}

	// 反轉順序，使其按時間正序排列
	for i, j := 0, len(results)-1; i < j; i, j = i+1, j-1 {
		results[i], results[j] = results[j], results[i]
	}

	return results, nil
}
