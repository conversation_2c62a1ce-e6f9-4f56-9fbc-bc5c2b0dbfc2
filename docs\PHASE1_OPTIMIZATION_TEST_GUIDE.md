# Phase 1 優化測試指南

## 已完成的優化

### ✅ 數字 Hash Key 優化 (Phase 1.1)

**優化內容**:
- 使用數字 hash 替代字串 key，減少記憶體使用
- 添加性能監控功能
- 保持計算邏輯完全不變

### ✅ 避免 Generator 轉陣列 (Phase 1.2)

**優化內容**:
- 移除所有 `Array.from(generator)` 調用
- 直接使用 Generator 迭代，避免一次性載入所有組合
- 重構迴圈結構，Generator 在內層創建
- 大幅減少記憶體峰值使用

## 如何測試優化效果

### 1. 訪問測試頁面

在瀏覽器中訪問：
```
http://localhost:9000/analyzer-optimization-test
```

### 2. 測試配置建議

#### 小型測試 (快速驗證)
- 測試期數: 100
- 第一組大小: 2
- 第二組大小: 2  
- 目標組大小: 2
- 最大範圍: 10

#### 中型測試 (效果明顯)
- 測試期數: 300
- 第一組大小: 3
- 第二組大小: 3
- 目標組大小: 3
- 最大範圍: 15

#### 大型測試 (壓力測試)
- 測試期數: 500
- 第一組大小: 4
- 第二組大小: 4
- 目標組大小: 4
- 最大範圍: 20

#### 極限測試 (驗證 Phase 1.2 效果)
- 測試期數: 300
- 第一組大小: 5
- 第二組大小: 5
- 目標組大小: 5
- 最大範圍: 15
- **注意**: 這個配置在優化前可能會導致瀏覽器崩潰

### 3. 觀察指標

#### 主要改善指標
1. **執行時間**: 應該有所減少
2. **峰值記憶體**: 應該明顯減少
3. **當前記憶體**: 測試完成後記憶體釋放更好
4. **結果數量**: 應該與原版本完全相同

#### 預期改善程度 (Phase 1.1 + 1.2)
- **記憶體使用**: 減少 60-80% (主要來自 Phase 1.2)
- **執行速度**: 提升 15-30% (兩個階段累積)
- **穩定性**: 大參數下顯著更穩定，可處理更大的配置
- **記憶體峰值**: 在大組合數情況下減少 80-90%

### 4. 測試步驟

1. **開啟瀏覽器開發者工具**
   - 按 F12 打開
   - 切換到 "Performance" 或"效能" 標籤

2. **執行測試**
   - 設定測試參數
   - 點擊 "開始比較測試"
   - 觀察進度和記憶體使用

3. **記錄結果**
   - 記錄執行時間
   - 記錄峰值記憶體
   - 檢查結果數量

### 5. 如何比較原版本

如果您想比較原版本，需要：

1. **備份當前優化版本**
```bash
cp front/src/workers/analyzer.worker.ts front/src/workers/analyzer.worker.optimized.ts
```

2. **恢復原版本** (如果有備份)
```bash
cp front/src/workers/analyzer.worker.original.ts front/src/workers/analyzer.worker.ts
```

3. **執行測試並記錄結果**

4. **切換回優化版本**
```bash
cp front/src/workers/analyzer.worker.optimized.ts front/src/workers/analyzer.worker.ts
```

5. **再次執行測試並比較**

### 6. 問題排查

#### 如果測試頁面無法訪問
- 確認路由已正確添加
- 檢查是否已登入系統
- 確認開發服務器正在運行

#### 如果測試失敗
- 檢查瀏覽器控制台錯誤信息
- 確認 Worker 文件路徑正確
- 檢查是否有 TypeScript 編譯錯誤

#### 如果記憶體監控不工作
- 某些瀏覽器可能不支援 `performance.memory` API
- 這是 Chrome 特有的非標準 API，Firefox 和 Safari 通常不支援
- 測試頁面會顯示警告訊息："當前瀏覽器不支援記憶體監控"
- 可以使用瀏覽器開發者工具的記憶體標籤手動監控：
  1. 開啟開發者工具 (F12)
  2. 切換到 "Memory" 或"記憶體" 標籤
  3. 在測試前後手動記錄記憶體使用量

#### 瀏覽器支援情況
- ✅ **Chrome/Chromium**: 完全支援 `performance.memory`
- ❌ **Firefox**: 不支援 `performance.memory`
- ❌ **Safari**: 不支援 `performance.memory`
- ✅ **Edge**: 支援 `performance.memory`

#### 替代測試方法
如果記憶體監控不可用，可以：
1. 使用 Chrome 瀏覽器進行測試
2. 觀察執行時間的改善（這個指標在所有瀏覽器都可用）
3. 測試大參數配置下的穩定性（是否會崩潰）

### 7. 測試報告模板

```
## Phase 1.1 測試結果

**測試環境**:
- 瀏覽器: [Chrome/Firefox/Safari] [版本]
- 作業系統: [Windows/Mac/Linux]
- 測試時間: [日期時間]

**測試配置**:
- 期數: [數量]
- 組合大小: [first-second-target]
- 最大範圍: [數量]

**測試結果**:
- 執行時間: [毫秒]
- 峰值記憶體: [MB]
- 結果數量: [數量]
- 狀態: [成功/失敗]

**改善程度**:
- 速度改善: [%] (如果有比較)
- 記憶體減少: [%] (如果有比較)

**備註**:
[任何觀察到的問題或特殊情況]
```

## 下一步計劃

根據測試結果，我們將決定：

1. **如果效果良好**: 繼續 Phase 1.2 - 避免 Generator 轉陣列
2. **如果效果一般**: 調整優化策略或參數
3. **如果有問題**: 修復問題後重新測試

請執行測試並分享結果，這將幫助我們決定下一步的優化方向！
