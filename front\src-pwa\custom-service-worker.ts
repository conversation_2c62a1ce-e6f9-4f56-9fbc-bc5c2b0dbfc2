/*
 * This file (which will be your service worker)
 * is picked up by the build system ONLY if
 * quasar.config.js > pwa > workboxMode is set to "injectManifest"
 */

// 定義 Service Worker 相關的類型
interface ServiceWorkerMessage {
  type: string;
  [key: string]: unknown;
}

interface ServiceWorkerClient {
  postMessage(message: ServiceWorkerMessage): void;
  id: string;
  url: string;
}

interface ServiceWorkerClients {
  matchAll(): Promise<readonly ServiceWorkerClient[]>;
  claim(): Promise<void>;
}

interface ExtendableEvent extends Event {
  waitUntil(promise: Promise<unknown>): void;
}

declare const self: ServiceWorkerGlobalScope &
  typeof globalThis & {
    skipWaiting: () => void;
    clients: ServiceWorkerClients;
    addEventListener(type: 'activate', listener: (event: ExtendableEvent) => void): void;
    addEventListener(type: 'install', listener: (event: ExtendableEvent) => void): void;
    addEventListener(type: 'message', listener: (event: MessageEvent) => void): void;
    addEventListener(type: string, listener: EventListenerOrEventListenerObject): void;
  };

import { clientsClaim } from 'workbox-core';
import {
  precacheAndRoute,
  cleanupOutdatedCaches,
} from 'workbox-precaching';

// 立即跳過等待並接管所有客戶端
self.skipWaiting();
clientsClaim();

// 監聽來自主線程的消息
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  if (event.data && event.data.type === 'RELOAD_PAGE') {
    // 通知所有客戶端重新載入頁面
    self.clients.matchAll().then((clients: readonly ServiceWorkerClient[]) => {
      clients.forEach((client: ServiceWorkerClient) => {
        client.postMessage({ type: 'RELOAD_PAGE' });
      });
    });
  }
});

// 監聽安裝事件，強制更新
self.addEventListener('install', () => {
  // 立即跳過等待，不等待舊版本的 Service Worker 關閉
  self.skipWaiting();
});

// 監聽啟動事件，立即接管所有客戶端
self.addEventListener('activate', (event: ExtendableEvent) => {
  event.waitUntil(
    Promise.all([
      // 清理舊緩存
      cleanupOutdatedCaches(),
      // 立即接管所有客戶端
      self.clients.claim(),
      // 通知所有客戶端重新載入
      self.clients.matchAll().then((clients: readonly ServiceWorkerClient[]) => {
        clients.forEach((client: ServiceWorkerClient) => {
          client.postMessage({ type: 'FORCE_RELOAD' });
        });
      })
    ])
  );
});

// Use with precache injection
precacheAndRoute(self.__WB_MANIFEST);

cleanupOutdatedCaches();
