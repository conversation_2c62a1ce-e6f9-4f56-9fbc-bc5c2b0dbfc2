package memory

import (
	"context"
	"runtime"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"

	"lottery/ball_follow_scheduler/config"
)

// Manager 記憶體管理器
type Manager struct {
	config config.MemoryConfig
	
	// 狀態管理
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
	running bool
	mu      sync.RWMutex
	
	// 統計資訊
	stats *MemoryStats
	
	// 回調函數
	thresholdCallback func(usage int64, percent int)
	gcCallback        func()
}

// MemoryStats 記憶體統計資訊
type MemoryStats struct {
	CurrentMemoryMB    int64     `json:"current_memory_mb"`
	PeakMemoryMB       int64     `json:"peak_memory_mb"`
	UsagePercent       int       `json:"usage_percent"`
	GCCount            int64     `json:"gc_count"`
	LastGCTime         time.Time `json:"last_gc_time"`
	TotalGCPauseNS     uint64    `json:"total_gc_pause_ns"`
	LastMonitorTime    time.Time `json:"last_monitor_time"`
	ThresholdExceeded  bool      `json:"threshold_exceeded"`
	mu                 sync.RWMutex
}

// NewManager 建立新的記憶體管理器
func NewManager(cfg config.MemoryConfig) *Manager {
	return &Manager{
		config: cfg,
		stats: &MemoryStats{
			LastMonitorTime: time.Now(),
		},
	}
}

// Start 啟動記憶體管理器
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if m.running {
		return nil
	}
	
	m.ctx, m.cancel = context.WithCancel(ctx)
	
	log.Info("Starting memory manager...")
	
	// 啟動記憶體監控協程
	m.wg.Add(1)
	go m.monitorLoop()
	
	// 如果啟用自動垃圾回收，啟動GC協程
	if m.config.AutoGCEnabled {
		m.wg.Add(1)
		go m.gcLoop()
	}
	
	m.running = true
	
	log.Info("Memory manager started")
	return nil
}

// Stop 停止記憶體管理器
func (m *Manager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if !m.running {
		return nil
	}
	
	log.Info("Stopping memory manager...")
	
	m.cancel()
	m.wg.Wait()
	
	m.running = false
	
	log.Info("Memory manager stopped")
	return nil
}

// monitorLoop 記憶體監控循環
func (m *Manager) monitorLoop() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(m.config.MonitorInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.updateMemoryStats()
		}
	}
}

// gcLoop 垃圾回收循環
func (m *Manager) gcLoop() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(m.config.GCInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.performGCIfNeeded()
		}
	}
}

// updateMemoryStats 更新記憶體統計資訊
func (m *Manager) updateMemoryStats() {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	currentMB := int64(memStats.Alloc / 1024 / 1024)
	usagePercent := m.calculateUsagePercent(currentMB)
	
	m.stats.mu.Lock()
	defer m.stats.mu.Unlock()
	
	m.stats.CurrentMemoryMB = currentMB
	m.stats.UsagePercent = usagePercent
	m.stats.GCCount = int64(memStats.NumGC)
	m.stats.TotalGCPauseNS = memStats.PauseTotalNs
	m.stats.LastMonitorTime = time.Now()
	
	// 更新峰值記憶體
	if currentMB > m.stats.PeakMemoryMB {
		m.stats.PeakMemoryMB = currentMB
	}
	
	// 檢查是否超過閾值
	thresholdExceeded := usagePercent >= m.config.ThresholdPercent
	if thresholdExceeded != m.stats.ThresholdExceeded {
		m.stats.ThresholdExceeded = thresholdExceeded
		
		// 觸發閾值回調
		if m.thresholdCallback != nil {
			go m.thresholdCallback(currentMB, usagePercent)
		}
		
		if thresholdExceeded {
			log.Warnf("Memory usage exceeded threshold: %d%% (%dMB)", usagePercent, currentMB)
		} else {
			log.Infof("Memory usage back to normal: %d%% (%dMB)", usagePercent, currentMB)
		}
	}
	
	// 記錄詳細的記憶體資訊（調試模式）
	log.Debugf("Memory stats: Current=%dMB, Usage=%d%%, GC=%d, HeapObjects=%d",
		currentMB, usagePercent, memStats.NumGC, memStats.HeapObjects)
}

// performGCIfNeeded 根據需要執行垃圾回收
func (m *Manager) performGCIfNeeded() {
	m.stats.mu.RLock()
	shouldGC := m.stats.ThresholdExceeded
	m.stats.mu.RUnlock()
	
	if shouldGC {
		m.ForceGC()
	}
}

// ForceGC 強制執行垃圾回收
func (m *Manager) ForceGC() {
	log.Info("Performing garbage collection...")
	
	beforeMB := m.GetCurrentMemoryMB()
	startTime := time.Now()
	
	runtime.GC()
	runtime.GC() // 執行兩次確保徹底清理
	
	duration := time.Since(startTime)
	afterMB := m.GetCurrentMemoryMB()
	freedMB := beforeMB - afterMB
	
	m.stats.mu.Lock()
	m.stats.LastGCTime = time.Now()
	m.stats.mu.Unlock()
	
	// 觸發GC回調
	if m.gcCallback != nil {
		go m.gcCallback()
	}
	
	log.Infof("Garbage collection completed: freed %dMB in %v (before: %dMB, after: %dMB)",
		freedMB, duration, beforeMB, afterMB)
}

// calculateUsagePercent 計算記憶體使用百分比
func (m *Manager) calculateUsagePercent(currentMB int64) int {
	if m.config.MaxMemoryMB <= 0 {
		// 如果沒有設定最大記憶體，使用系統記憶體
		var memStats runtime.MemStats
		runtime.ReadMemStats(&memStats)
		systemMemoryMB := int64(memStats.Sys / 1024 / 1024)
		if systemMemoryMB > 0 {
			return int((currentMB * 100) / systemMemoryMB)
		}
		return 0
	}
	
	return int((currentMB * 100) / int64(m.config.MaxMemoryMB))
}

// GetCurrentMemoryMB 取得當前記憶體使用量（MB）
func (m *Manager) GetCurrentMemoryMB() int64 {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	return int64(memStats.Alloc / 1024 / 1024)
}

// GetStats 取得記憶體統計資訊
func (m *Manager) GetStats() *MemoryStats {
	m.stats.mu.RLock()
	defer m.stats.mu.RUnlock()
	
	// 返回副本
	statsCopy := *m.stats
	return &statsCopy
}

// IsThresholdExceeded 檢查是否超過記憶體閾值
func (m *Manager) IsThresholdExceeded() bool {
	m.stats.mu.RLock()
	defer m.stats.mu.RUnlock()
	return m.stats.ThresholdExceeded
}

// SetThresholdCallback 設定閾值回調函數
func (m *Manager) SetThresholdCallback(callback func(usage int64, percent int)) {
	m.thresholdCallback = callback
}

// SetGCCallback 設定垃圾回收回調函數
func (m *Manager) SetGCCallback(callback func()) {
	m.gcCallback = callback
}

// GetDetailedMemoryInfo 取得詳細的記憶體資訊
func (m *Manager) GetDetailedMemoryInfo() map[string]interface{} {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	return map[string]interface{}{
		"alloc_mb":          int64(memStats.Alloc / 1024 / 1024),
		"total_alloc_mb":    int64(memStats.TotalAlloc / 1024 / 1024),
		"sys_mb":            int64(memStats.Sys / 1024 / 1024),
		"heap_alloc_mb":     int64(memStats.HeapAlloc / 1024 / 1024),
		"heap_sys_mb":       int64(memStats.HeapSys / 1024 / 1024),
		"heap_idle_mb":      int64(memStats.HeapIdle / 1024 / 1024),
		"heap_inuse_mb":     int64(memStats.HeapInuse / 1024 / 1024),
		"heap_released_mb":  int64(memStats.HeapReleased / 1024 / 1024),
		"heap_objects":      memStats.HeapObjects,
		"stack_inuse_mb":    int64(memStats.StackInuse / 1024 / 1024),
		"stack_sys_mb":      int64(memStats.StackSys / 1024 / 1024),
		"gc_count":          memStats.NumGC,
		"gc_pause_total_ns": memStats.PauseTotalNs,
		"gc_pause_avg_ns":   m.calculateAverageGCPause(memStats),
		"next_gc_mb":        int64(memStats.NextGC / 1024 / 1024),
		"last_gc_time":      time.Unix(0, int64(memStats.LastGC)),
	}
}

// calculateAverageGCPause 計算平均GC暫停時間
func (m *Manager) calculateAverageGCPause(memStats runtime.MemStats) uint64 {
	if memStats.NumGC == 0 {
		return 0
	}
	return memStats.PauseTotalNs / uint64(memStats.NumGC)
}

// WaitForMemoryAvailable 等待記憶體可用
func (m *Manager) WaitForMemoryAvailable(ctx context.Context, maxWaitTime time.Duration) error {
	if !m.IsThresholdExceeded() {
		return nil
	}
	
	log.Info("Waiting for memory to become available...")
	
	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-timeout:
			return fmt.Errorf("timeout waiting for memory to become available")
		case <-ticker.C:
			if !m.IsThresholdExceeded() {
				log.Info("Memory is now available")
				return nil
			}
		}
	}
}

// TriggerEmergencyCleanup 觸發緊急清理
func (m *Manager) TriggerEmergencyCleanup() {
	log.Warn("Triggering emergency memory cleanup...")
	
	// 執行多次垃圾回收
	for i := 0; i < 3; i++ {
		runtime.GC()
		time.Sleep(100 * time.Millisecond)
	}
	
	// 釋放未使用的記憶體回系統
	runtime.FreeOSMemory()
	
	log.Info("Emergency memory cleanup completed")
}
