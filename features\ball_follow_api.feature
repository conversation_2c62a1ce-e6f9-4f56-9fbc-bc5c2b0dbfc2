# language: zh-TW
功能: 版路分析API介面
  作為管理員用戶
  我需要透過API介面
  查詢計算任務的進度和下載計算結果
  以便監控和管理版路分析系統

  背景:
    假設 我已通過管理員身份驗證
    而且 系統中有版路分析的計算任務和結果

  場景: 查詢所有計算任務列表
    當 我發送GET請求到 "/api/admin/ball-follow/tasks"
    那麼 回應狀態碼應該是200
    而且 回應應該包含任務列表
    而且 每個任務應該包含以下資訊:
      | 欄位名稱              | 資料類型 | 說明           |
      | id                   | number   | 任務ID         |
      | lotto_type           | string   | 彩種類型       |
      | period               | number   | 期數           |
      | task_status          | string   | 任務狀態       |
      | total_combinations   | number   | 總組合數       |
      | completed_combinations| number  | 已完成組合數   |
      | failed_combinations  | number   | 失敗組合數     |
      | progress_percentage  | number   | 進度百分比     |
      | start_time           | string   | 開始時間       |
      | estimated_duration   | number   | 預估執行時間   |
      | remaining_time       | number   | 預估剩餘時間   |

  場景: 查詢特定任務的詳細資訊
    假設 存在任務ID為123的版路分析任務
    當 我發送GET請求到 "/api/admin/ball-follow/tasks/123"
    那麼 回應狀態碼應該是200
    而且 回應應該包含任務的詳細資訊
    而且 應該包含所有子任務的狀態列表
    而且 每個子任務應該包含參數組合和執行狀態

  場景: 查詢特定期數的計算結果
    假設 今彩539期數113001的版路分析已完成
    當 我發送GET請求到 "/api/admin/ball-follow/results?lotto_type=daily539&period=113001"
    那麼 回應狀態碼應該是200
    而且 回應應該包含該期數的所有計算結果
    而且 結果應該按參數組合分組
    而且 每個結果應該包含預測號碼統計、未出現號碼統計、尾數統計

  場景: 下載特定期數的Excel報表
    假設 今彩539期數113001的版路分析已完成
    當 我發送GET請求到 "/api/admin/ball-follow/export?lotto_type=daily539&period=113001&format=excel"
    那麼 回應狀態碼應該是200
    而且 回應的Content-Type應該是 "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    而且 回應應該包含Excel檔案的二進位資料
    而且 檔案名稱應該包含彩種和期數資訊

  場景: 查詢計算結果的統計摘要
    假設 今彩539期數113001的版路分析已完成
    當 我發送GET請求到 "/api/admin/ball-follow/summary?lotto_type=daily539&period=113001"
    那麼 回應狀態碼應該是200
    而且 回應應該包含以下統計資訊:
      | 欄位名稱                    | 說明                 |
      | total_combinations         | 總參數組合數         |
      | successful_combinations    | 成功計算的組合數     |
      | failed_combinations        | 失敗的組合數         |
      | average_calculation_time   | 平均計算時間         |
      | top_predict_numbers        | 最常出現的預測號碼   |
      | calculation_completion_time| 計算完成時間         |

  場景: 手動觸發特定期數的計算
    假設 今彩539有新期數113002尚未計算
    當 我發送POST請求到 "/api/admin/ball-follow/trigger" 包含:
      """
      {
        "lotto_type": "daily539",
        "period": 113002,
        "force_recalculate": false
      }
      """
    那麼 回應狀態碼應該是201
    而且 回應應該包含新建立的任務ID
    而且 系統應該開始執行該期數的版路分析計算

  場景: 強制重新計算已存在的結果
    假設 今彩539期數113001已完成計算
    當 我發送POST請求到 "/api/admin/ball-follow/trigger" 包含:
      """
      {
        "lotto_type": "daily539",
        "period": 113001,
        "force_recalculate": true
      }
      """
    那麼 回應狀態碼應該是201
    而且 系統應該清除舊的計算結果
    而且 應該重新開始計算該期數的版路分析

  場景: 停止正在執行的計算任務
    假設 存在正在執行的任務ID為123
    當 我發送POST請求到 "/api/admin/ball-follow/tasks/123/stop"
    那麼 回應狀態碼應該是200
    而且 任務狀態應該更新為 "stopped"
    而且 正在執行的子任務應該被中止
    而且 已完成的結果應該保留

  場景: 查詢系統資源使用狀況
    當 我發送GET請求到 "/api/admin/ball-follow/system-status"
    那麼 回應狀態碼應該是200
    而且 回應應該包含以下系統資訊:
      | 欄位名稱              | 說明                 |
      | memory_usage_mb      | 記憶體使用量(MB)     |
      | memory_usage_percent | 記憶體使用百分比     |
      | active_tasks         | 活躍任務數量         |
      | concurrent_calculations| 並行計算數量       |
      | queue_length         | 待處理任務佇列長度   |
      | last_gc_time         | 最後垃圾回收時間     |

  場景: 配置並行計算數量
    當 我發送PUT請求到 "/api/admin/ball-follow/config" 包含:
      """
      {
        "max_concurrent_calculations": 5,
        "memory_threshold_percent": 85,
        "auto_gc_enabled": true
      }
      """
    那麼 回應狀態碼應該是200
    而且 系統應該更新並行計算的配置
    而且 新的配置應該立即生效

  場景: 查詢計算歷史記錄
    當 我發送GET請求到 "/api/admin/ball-follow/history?lotto_type=daily539&limit=10"
    那麼 回應狀態碼應該是200
    而且 回應應該包含最近10次的計算記錄
    而且 每筆記錄應該包含期數、計算時間、成功率、錯誤訊息等資訊
    而且 記錄應該按時間倒序排列

  場景: 非管理員用戶嘗試存取API
    假設 我是一般用戶(非管理員)
    當 我發送GET請求到 "/api/admin/ball-follow/tasks"
    那麼 回應狀態碼應該是403
    而且 回應應該包含 "權限不足" 的錯誤訊息

  場景: 查詢不存在的任務
    當 我發送GET請求到 "/api/admin/ball-follow/tasks/99999"
    那麼 回應狀態碼應該是404
    而且 回應應該包含 "任務不存在" 的錯誤訊息

  場景: 下載尚未完成計算的結果
    假設 今彩539期數113003的計算正在進行中
    當 我發送GET請求到 "/api/admin/ball-follow/export?lotto_type=daily539&period=113003&format=excel"
    那麼 回應狀態碼應該是202
    而且 回應應該包含 "計算尚未完成，請稍後再試" 的訊息
    而且 應該提供當前計算進度資訊

  場景: API回應時間效能要求
    當 我發送GET請求到 "/api/admin/ball-follow/tasks"
    那麼 回應時間應該在2秒內
    而且 當我發送GET請求到 "/api/admin/ball-follow/results" 查詢特定期數
    那麼 回應時間應該在5秒內

  場景: 批量查詢多個期數的結果
    當 我發送POST請求到 "/api/admin/ball-follow/batch-results" 包含:
      """
      {
        "lotto_type": "daily539",
        "periods": [113001, 113002, 113003],
        "parameters": {
          "comb1": 1,
          "comb2": 1,
          "comb3": 1,
          "period_num": 120,
          "max_range": 15
        }
      }
      """
    那麼 回應狀態碼應該是200
    而且 回應應該包含所有指定期數和參數的計算結果
    而且 結果應該按期數排序
