package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"

	. "lottery/models"
)

// BallFollowAdminController 版路分析管理員控制器
type BallFollowAdminController struct {
	repository *BallFollowRepository
}

// NewBallFollowAdminController 建立新的管理員控制器
func NewBallFollowAdminController(repo *BallFollowRepository) *BallFollowAdminController {
	return &BallFollowAdminController{
		repository: repo,
	}
}

// AdminMiddleware 管理員權限中介軟體
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 從 JWT token 或 session 中取得用戶資訊
		userInterface, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "未授權",
				"message": "請先登入",
			})
			c.Abort()
			return
		}

		user, ok := userInterface.(*User)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "用戶資訊錯誤",
				"message": "無法取得用戶資訊",
			})
			c.Abort()
			return
		}

		// 檢查是否為管理員
		if !user.IsAdmin {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "權限不足",
				"message": "需要管理員權限才能存取此功能",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetTasks 取得所有計算任務列表
func (ctrl *BallFollowAdminController) GetTasks(c *gin.Context) {
	// 解析查詢參數
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	lottoType := c.Query("lotto_type")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 建立查詢條件
	query := ctrl.repository.GetDB().Model(&BallFollowCalculationTask{})

	if status != "" {
		query = query.Where("task_status = ?", status)
	}
	if lottoType != "" {
		query = query.Where("lotto_type = ?", lottoType)
	}

	// 取得總數
	var total int64
	if err := query.Count(&total).Error; err != nil {
		log.Errorf("Failed to count tasks: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查詢失敗",
			"message": "無法取得任務總數",
		})
		return
	}

	// 取得任務列表
	var tasks []BallFollowCalculationTask
	if err := query.Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&tasks).Error; err != nil {
		log.Errorf("Failed to get tasks: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查詢失敗",
			"message": "無法取得任務列表",
		})
		return
	}

	// 計算進度百分比和剩餘時間
	var taskList []map[string]interface{}
	for _, task := range tasks {
		progressPercentage := task.GetProgressPercentage()
		remainingCombinations := task.GetRemainingCombinations()

		// 估算剩餘時間
		var estimatedRemainingTime int64
		if task.IsRunning() && task.StartTime != nil {
			elapsed := time.Since(*task.StartTime)
			if task.CompletedCombinations > 0 {
				avgTimePerCombination := elapsed / time.Duration(task.CompletedCombinations)
				estimatedRemainingTime = int64(avgTimePerCombination * time.Duration(remainingCombinations) / time.Second)
			}
		}

		taskInfo := map[string]interface{}{
			"id":                     task.ID,
			"lotto_type":             task.LottoType,
			"period":                 task.Period,
			"task_status":            task.TaskStatus,
			"total_combinations":     task.TotalCombinations,
			"completed_combinations": task.CompletedCombinations,
			"failed_combinations":    task.FailedCombinations,
			"progress_percentage":    fmt.Sprintf("%.2f", progressPercentage),
			"start_time":             task.StartTime,
			"end_time":               task.EndTime,
			"estimated_duration":     task.EstimatedDuration,
			"actual_duration":        task.ActualDuration,
			"remaining_time":         estimatedRemainingTime,
			"priority":               task.Priority,
			"triggered_by":           task.TriggeredBy,
			"created_at":             task.CreatedAt,
			"updated_at":             task.UpdatedAt,
		}

		taskList = append(taskList, taskInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"tasks": taskList,
			"pagination": gin.H{
				"page":        page,
				"limit":       limit,
				"total":       total,
				"total_pages": (total + int64(limit) - 1) / int64(limit),
			},
		},
	})
}

// GetTaskDetail 取得特定任務的詳細資訊
func (ctrl *BallFollowAdminController) GetTaskDetail(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "無效的任務ID",
		})
		return
	}

	// 取得任務詳細資訊
	task, err := ctrl.repository.GetCalculationTask(taskID)
	if err != nil {
		log.Errorf("Failed to get task detail: %v", err)
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "任務不存在",
			"message": "找不到指定的任務",
		})
		return
	}

	// 取得子任務統計
	var subtaskStats []map[string]interface{}
	statusCounts := make(map[string]int)

	for _, subtask := range task.Subtasks {
		statusCounts[string(subtask.SubtaskStatus)]++
	}

	for status, count := range statusCounts {
		subtaskStats = append(subtaskStats, map[string]interface{}{
			"status": status,
			"count":  count,
		})
	}

	// 計算詳細統計
	progressPercentage := task.GetProgressPercentage()
	remainingCombinations := task.GetRemainingCombinations()

	taskDetail := map[string]interface{}{
		"id":                     task.ID,
		"lotto_type":             task.LottoType,
		"period":                 task.Period,
		"task_status":            task.TaskStatus,
		"total_combinations":     task.TotalCombinations,
		"completed_combinations": task.CompletedCombinations,
		"failed_combinations":    task.FailedCombinations,
		"remaining_combinations": remainingCombinations,
		"progress_percentage":    fmt.Sprintf("%.2f", progressPercentage),
		"start_time":             task.StartTime,
		"end_time":               task.EndTime,
		"estimated_duration":     task.EstimatedDuration,
		"actual_duration":        task.ActualDuration,
		"error_message":          task.ErrorMessage,
		"retry_count":            task.RetryCount,
		"priority":               task.Priority,
		"triggered_by":           task.TriggeredBy,
		"config_snapshot":        task.ConfigSnapshot,
		"progress_details":       task.ProgressDetails,
		"subtask_stats":          subtaskStats,
		"created_at":             task.CreatedAt,
		"updated_at":             task.UpdatedAt,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    taskDetail,
	})
}

// GetResults 取得特定期數的計算結果
func (ctrl *BallFollowAdminController) GetResults(c *gin.Context) {
	lottoType := c.Query("lotto_type")
	periodStr := c.Query("period")

	if lottoType == "" || periodStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "需要提供 lotto_type 和 period 參數",
		})
		return
	}

	period, err := strconv.Atoi(periodStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "無效的期數",
		})
		return
	}

	// 取得計算結果
	results, err := ctrl.repository.GetAnalysisResultsByPeriod(LottoTypeStr(lottoType), period)
	if err != nil {
		log.Errorf("Failed to get analysis results: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查詢失敗",
			"message": "無法取得計算結果",
		})
		return
	}

	if len(results) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "結果不存在",
			"message": "找不到指定期數的計算結果",
		})
		return
	}

	// 按參數組合分組結果
	groupedResults := make(map[string][]BallFollowAnalysisResult)
	for _, result := range results {
		key := fmt.Sprintf("(%d,%d,%d)-P%d-R%d",
			result.Comb1, result.Comb2, result.Comb3,
			result.PeriodNum, result.MaxRange)
		groupedResults[key] = append(groupedResults[key], result)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"lotto_type":      lottoType,
			"period":          period,
			"total_results":   len(results),
			"grouped_results": groupedResults,
		},
	})
}

// GetSummary 取得計算結果的統計摘要
func (ctrl *BallFollowAdminController) GetSummary(c *gin.Context) {
	lottoType := c.Query("lotto_type")
	periodStr := c.Query("period")

	if lottoType == "" || periodStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "需要提供 lotto_type 和 period 參數",
		})
		return
	}

	period, err := strconv.Atoi(periodStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "無效的期數",
		})
		return
	}

	// 取得計算結果
	results, err := ctrl.repository.GetAnalysisResultsByPeriod(LottoTypeStr(lottoType), period)
	if err != nil {
		log.Errorf("Failed to get analysis results: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查詢失敗",
			"message": "無法取得計算結果",
		})
		return
	}

	// 計算統計摘要
	totalCombinations := len(results)
	successfulCombinations := 0
	failedCombinations := 0
	var totalCalculationTime uint32
	var calculationCompletionTime *time.Time

	topPredictNumbers := make(map[string]int)

	for _, result := range results {
		if result.CalculationDuration != nil {
			successfulCombinations++
			totalCalculationTime += *result.CalculationDuration

			// 找到最晚的計算完成時間
			if calculationCompletionTime == nil || result.CreatedAt.After(*calculationCompletionTime) {
				calculationCompletionTime = &result.CreatedAt
			}

			// 統計最常出現的預測號碼
			if predictData, ok := result.PredictNumbers["numbers"].([]interface{}); ok {
				for _, num := range predictData {
					if numInt, ok := num.(float64); ok {
						key := fmt.Sprintf("%.0f", numInt)
						topPredictNumbers[key]++
					}
				}
			}
		} else {
			failedCombinations++
		}
	}

	var averageCalculationTime float64
	if successfulCombinations > 0 {
		averageCalculationTime = float64(totalCalculationTime) / float64(successfulCombinations)
	}

	summary := map[string]interface{}{
		"total_combinations":          totalCombinations,
		"successful_combinations":     successfulCombinations,
		"failed_combinations":         failedCombinations,
		"average_calculation_time":    averageCalculationTime,
		"top_predict_numbers":         topPredictNumbers,
		"calculation_completion_time": calculationCompletionTime,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    summary,
	})
}

// TriggerCalculation 手動觸發特定期數的計算
func (ctrl *BallFollowAdminController) TriggerCalculation(c *gin.Context) {
	var request struct {
		LottoType        string `json:"lotto_type" binding:"required"`
		Period           int    `json:"period" binding:"required"`
		ForceRecalculate bool   `json:"force_recalculate"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": err.Error(),
		})
		return
	}

	// 檢查是否已經計算過
	if !request.ForceRecalculate {
		calculated, err := ctrl.repository.CheckPeriodCalculated(LottoTypeStr(request.LottoType), request.Period)
		if err != nil {
			log.Errorf("Failed to check if period is calculated: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "檢查失敗",
				"message": "無法檢查期數計算狀態",
			})
			return
		}

		if calculated {
			c.JSON(http.StatusConflict, gin.H{
				"error":   "已存在計算結果",
				"message": "該期數已完成計算，如需重新計算請設定 force_recalculate 為 true",
			})
			return
		}
	} else {
		// 強制重新計算：清除舊的計算結果
		if err := ctrl.clearExistingResults(request.LottoType, request.Period); err != nil {
			log.Errorf("Failed to clear existing results: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "清除失敗",
				"message": "無法清除舊的計算結果",
			})
			return
		}
	}

	// 建立新的計算任務
	task := &BallFollowCalculationTask{
		LottoType:         LottoTypeStr(request.LottoType),
		Period:            request.Period,
		TaskStatus:        TaskStatusPending,
		TotalCombinations: 1350, // 27 * 10 * 5
		Priority:          1,    // 手動觸發的任務優先級較高
		TriggeredBy:       TriggerTypeAPI,
	}

	if err := ctrl.repository.CreateCalculationTask(task); err != nil {
		log.Errorf("Failed to create calculation task: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "建立失敗",
			"message": "無法建立計算任務",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data": gin.H{
			"task_id":    task.ID,
			"lotto_type": task.LottoType,
			"period":     task.Period,
			"status":     task.TaskStatus,
			"message":    "計算任務已建立",
		},
	})
}

// StopTask 停止正在執行的計算任務
func (ctrl *BallFollowAdminController) StopTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "無效的任務ID",
		})
		return
	}

	// 取得任務
	task, err := ctrl.repository.GetCalculationTask(taskID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "任務不存在",
			"message": "找不到指定的任務",
		})
		return
	}

	// 檢查任務狀態
	if !task.IsRunning() {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "任務狀態錯誤",
			"message": "只能停止正在執行的任務",
		})
		return
	}

	// 更新任務狀態為停止
	task.TaskStatus = TaskStatusStopped
	task.EndTime = timePtr(time.Now())
	if task.StartTime != nil {
		duration := uint32(time.Since(*task.StartTime).Seconds())
		task.ActualDuration = &duration
	}

	if err := ctrl.repository.UpdateCalculationTask(task); err != nil {
		log.Errorf("Failed to update task status: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "更新失敗",
			"message": "無法停止任務",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"task_id": task.ID,
			"status":  task.TaskStatus,
			"message": "任務已停止",
		},
	})
}

// GetSystemStatus 取得系統資源使用狀況
func (ctrl *BallFollowAdminController) GetSystemStatus(c *gin.Context) {
	// 這裡需要與排程服務整合，暫時返回模擬資料
	// 實際實作時需要從排程服務取得即時狀態

	// 取得活躍任務數量
	runningTasks, err := ctrl.repository.GetRunningTasks()
	if err != nil {
		log.Errorf("Failed to get running tasks: %v", err)
	}

	// 取得待處理任務數量
	pendingTasks, err := ctrl.repository.GetPendingTasks(100)
	if err != nil {
		log.Errorf("Failed to get pending tasks: %v", err)
	}

	systemStatus := map[string]interface{}{
		"memory_usage_mb":         512, // 需要從記憶體管理器取得
		"memory_usage_percent":    65,  // 需要從記憶體管理器取得
		"active_tasks":            len(runningTasks),
		"concurrent_calculations": 3, // 需要從工作程序池取得
		"queue_length":            len(pendingTasks),
		"last_gc_time":            time.Now().Add(-5 * time.Minute),
		"uptime_seconds":          3600, // 需要從排程服務取得
		"status":                  "running",
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    systemStatus,
	})
}

// UpdateConfig 配置並行計算數量等設定
func (ctrl *BallFollowAdminController) UpdateConfig(c *gin.Context) {
	var request struct {
		MaxConcurrentCalculations *int  `json:"max_concurrent_calculations"`
		MemoryThresholdPercent    *int  `json:"memory_threshold_percent"`
		AutoGCEnabled             *bool `json:"auto_gc_enabled"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": err.Error(),
		})
		return
	}

	// 驗證參數
	if request.MaxConcurrentCalculations != nil {
		if *request.MaxConcurrentCalculations < 1 || *request.MaxConcurrentCalculations > 10 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "參數錯誤",
				"message": "並行計算數量必須在 1-10 之間",
			})
			return
		}
	}

	if request.MemoryThresholdPercent != nil {
		if *request.MemoryThresholdPercent < 50 || *request.MemoryThresholdPercent > 95 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "參數錯誤",
				"message": "記憶體閾值必須在 50-95% 之間",
			})
			return
		}
	}

	// 這裡需要與排程服務整合來實際更新配置
	// 暫時只返回成功訊息

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"message": "配置已更新",
			"config":  request,
		},
	})
}

// GetHistory 取得計算歷史記錄
func (ctrl *BallFollowAdminController) GetHistory(c *gin.Context) {
	lottoType := c.Query("lotto_type")
	limitStr := c.DefaultQuery("limit", "10")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	if lottoType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "需要提供 lotto_type 參數",
		})
		return
	}

	// 取得計算歷史
	history, err := ctrl.repository.GetCalculationHistory(LottoTypeStr(lottoType), limit)
	if err != nil {
		log.Errorf("Failed to get calculation history: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查詢失敗",
			"message": "無法取得計算歷史",
		})
		return
	}

	// 處理歷史記錄，添加額外資訊
	var historyList []map[string]interface{}
	for _, task := range history {
		var successRate float64
		if task.TotalCombinations > 0 {
			successRate = float64(task.CompletedCombinations) / float64(task.TotalCombinations) * 100
		}

		historyInfo := map[string]interface{}{
			"id":            task.ID,
			"period":        task.Period,
			"task_status":   task.TaskStatus,
			"success_rate":  fmt.Sprintf("%.2f", successRate),
			"start_time":    task.StartTime,
			"end_time":      task.EndTime,
			"duration":      task.ActualDuration,
			"error_message": task.ErrorMessage,
			"triggered_by":  task.TriggeredBy,
			"created_at":    task.CreatedAt,
		}

		historyList = append(historyList, historyInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"lotto_type": lottoType,
			"history":    historyList,
			"total":      len(historyList),
		},
	})
}

// 輔助方法
func (ctrl *BallFollowAdminController) clearExistingResults(lottoType string, period int) error {
	// 刪除現有的分析結果
	if err := ctrl.repository.GetDB().Where("lotto_type = ? AND period = ?", lottoType, period).
		Delete(&BallFollowAnalysisResult{}).Error; err != nil {
		return err
	}

	// 刪除現有的任務和子任務
	if err := ctrl.repository.GetDB().Where("lotto_type = ? AND period = ?", lottoType, period).
		Delete(&BallFollowCalculationTask{}).Error; err != nil {
		return err
	}

	return nil
}

// BatchQueryResults 批量查詢多個期數的結果
func (ctrl *BallFollowAdminController) BatchQueryResults(c *gin.Context) {
	var request struct {
		LottoType  string `json:"lotto_type" binding:"required"`
		Periods    []int  `json:"periods" binding:"required"`
		Parameters *struct {
			Comb1     uint8  `json:"comb1"`
			Comb2     uint8  `json:"comb2"`
			Comb3     uint8  `json:"comb3"`
			PeriodNum uint16 `json:"period_num"`
			MaxRange  uint8  `json:"max_range"`
		} `json:"parameters"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": err.Error(),
		})
		return
	}

	if len(request.Periods) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "期數列表不能為空",
		})
		return
	}

	if len(request.Periods) > 50 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "參數錯誤",
			"message": "一次最多查詢50個期數",
		})
		return
	}

	var batchResults []map[string]interface{}

	for _, period := range request.Periods {
		periodResult := map[string]interface{}{
			"period": period,
			"status": "success",
		}

		if request.Parameters != nil {
			// 查詢特定參數組合的結果
			result, err := ctrl.repository.GetAnalysisResultsByParameters(
				LottoTypeStr(request.LottoType),
				period,
				request.Parameters.Comb1,
				request.Parameters.Comb2,
				request.Parameters.Comb3,
				request.Parameters.PeriodNum,
				request.Parameters.MaxRange,
			)

			if err != nil {
				periodResult["status"] = "not_found"
				periodResult["error"] = "找不到計算結果"
			} else {
				periodResult["result"] = result
			}
		} else {
			// 查詢該期數的所有結果
			results, err := ctrl.repository.GetAnalysisResultsByPeriod(
				LottoTypeStr(request.LottoType),
				period,
			)

			if err != nil {
				periodResult["status"] = "error"
				periodResult["error"] = "查詢失敗"
			} else if len(results) == 0 {
				periodResult["status"] = "not_found"
				periodResult["error"] = "找不到計算結果"
			} else {
				periodResult["results"] = results
				periodResult["total_results"] = len(results)
			}
		}

		batchResults = append(batchResults, periodResult)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"lotto_type":    request.LottoType,
			"total_periods": len(request.Periods),
			"results":       batchResults,
		},
	})
}

func timePtr(t time.Time) *time.Time {
	return &t
}
