# language: zh-TW
功能: 後台版路分析排程服務
  作為系統管理員
  我希望有一個自動化的後台排程服務
  能夠在新開獎號碼出現後自動執行版路分析計算
  並將結果存儲到資料庫供用戶查詢

  背景:
    假設 系統中已有今彩539的歷史開獎數據
    而且 資料庫中已建立版路分析結果表和任務狀態表
    而且 排程服務已正常運行

  場景: 檢測到新的今彩539開獎號碼時觸發計算
    假設 今彩539最新期數為 "113001"
    而且 該期數尚未進行版路分析計算
    當 排程服務在晚上10:30執行檢查
    那麼 系統應該創建新的計算任務
    而且 任務狀態應該為 "pending"
    而且 應該開始執行版路分析計算

  場景: 當天沒有新開獎號碼時跳過計算
    假設 今彩539最新期數為 "113001"
    而且 該期數已完成版路分析計算
    當 排程服務在晚上10:30執行檢查
    那麼 系統應該跳過計算
    而且 應該記錄 "無新開獎號碼，跳過計算" 的日誌

  場景: 執行完整的參數組合計算
    假設 今彩539最新期數為 "113001"
    而且 該期數尚未進行版路分析計算
    當 開始執行版路分析計算
    那麼 系統應該計算27種拖牌組合
    而且 每種拖牌組合應該計算10種推算期數 (30,60,90,120,150,180,210,240,270,300)
    而且 每種推算期數應該計算5種拖牌區間 (10,15,20,25,30)
    而且 總共應該創建1350個計算子任務

  場景: 分析期數固定為50期
    假設 正在執行版路分析計算
    而且 當前參數為拖牌組合(1,1,1)、推算期數120、拖牌區間15
    當 執行該參數組合的計算
    那麼 系統應該分析最新50期的歷史數據
    而且 計算結果應該包含這50期的統計資訊

  場景: 用戶可以查看部分完成的結果
    假設 版路分析計算正在進行中
    而且 已完成100個參數組合的計算
    而且 還有1250個參數組合待計算
    當 用戶查詢版路分析結果
    那麼 系統應該返回已完成的100個組合結果
    而且 應該標示計算進度為 "100/1350 (7.4%)"

  場景: 計算過程中發生非嚴重錯誤
    假設 正在執行版路分析計算
    而且 當前參數組合為(2,1,3)、推算期數180、拖牌區間20
    當 該參數組合計算時發生記憶體不足錯誤
    那麼 系統應該記錄錯誤訊息
    而且 應該重試該參數組合最多3次
    而且 如果3次都失敗，應該標記該組合為失敗
    而且 應該繼續計算下一個參數組合
    而且 不應該中斷整個計算流程

  場景: 計算過程中發生嚴重錯誤
    假設 正在執行版路分析計算
    當 資料庫連接完全中斷
    那麼 系統應該記錄嚴重錯誤訊息
    而且 應該中斷所有計算
    而且 任務狀態應該更新為 "failed"
    而且 應該發送錯誤通知給管理員

  場景: 避免重複計算同一期數
    假設 今彩539期數 "113001" 已完成版路分析計算
    當 排程服務再次檢查該期數
    那麼 系統應該跳過該期數的計算
    而且 應該記錄 "期數113001已計算完成，跳過" 的日誌

  場景: 管理員查詢計算任務進度
    假設 我是管理員用戶
    而且 系統中有正在進行的版路分析任務
    當 我查詢計算任務狀態
    那麼 系統應該返回任務列表
    而且 每個任務應該包含期數、開始時間、進度百分比、狀態
    而且 應該顯示已完成/總計算組合數
    而且 應該顯示預估剩餘時間

  場景: 管理員下載計算結果
    假設 我是管理員用戶
    而且 期數 "113001" 的版路分析已完成
    當 我請求下載該期數的計算結果
    那麼 系統應該生成Excel報表
    而且 報表應該包含所有1350種參數組合的結果
    而且 報表格式應該與BatchAnalysisPage相同

  場景: 記憶體管理和垃圾回收
    假設 正在執行大量參數組合計算
    當 系統記憶體使用率超過80%
    那麼 系統應該觸發垃圾回收
    而且 應該暫停新的計算任務
    而且 應該等待記憶體使用率降到60%以下
    而且 然後繼續執行剩餘的計算任務

  場景: 支援其他彩種的擴展性
    假設 系統需要支援大樂透的版路分析
    當 配置大樂透的排程任務
    那麼 系統應該能夠使用相同的計算引擎
    而且 應該能夠配置不同的參數範圍
    而且 應該能夠獨立管理不同彩種的計算任務

  場景: 可配置的並行計算數量
    假設 管理員設定並行計算數量為3
    當 執行版路分析計算
    那麼 系統應該同時執行最多3個參數組合的計算
    而且 當一個計算完成時，應該自動開始下一個待計算的組合
    而且 應該維持3個並行計算的上限

  場景: 歷史統計資料的重複使用
    假設 期數 "113001" 使用參數(1,1,1)、推算期數120、拖牌區間15已計算完成
    而且 期數 "113002" 需要使用相同參數進行計算
    當 執行期數 "113002" 的計算
    那麼 系統應該重複使用期數 "113001" 的部分統計資料
    而且 只需要計算新增期數的增量資料
    而且 應該顯著減少計算時間

  場景大綱: 不同參數組合的計算驗證
    假設 今彩539最新期數為 "113001"
    當 執行拖牌組合<comb1>,<comb2>,<comb3>、推算期數<period>、拖牌區間<range>的計算
    那麼 系統應該成功完成計算
    而且 結果應該包含預測號碼統計
    而且 結果應該包含未出現號碼統計
    而且 結果應該包含尾數統計
    而且 結果應該儲存到資料庫

    例子:
      | comb1 | comb2 | comb3 | period | range |
      | 1     | 1     | 1     | 30     | 10    |
      | 1     | 2     | 3     | 120    | 20    |
      | 3     | 3     | 3     | 300    | 30    |
