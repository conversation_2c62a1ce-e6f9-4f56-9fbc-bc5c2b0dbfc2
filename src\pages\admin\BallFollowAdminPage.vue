<template>
  <q-page class="q-pa-md">
    <!-- 頁面標題 -->
    <div class="row items-center q-mb-lg">
      <div class="col">
        <h4 class="q-my-none text-weight-bold">版路分析管理</h4>
        <p class="text-grey-6 q-mb-none">監控和管理版路分析計算任務</p>
      </div>
      <div class="col-auto">
        <q-btn
          color="primary"
          icon="refresh"
          label="重新整理"
          @click="refreshData"
          :loading="loading"
        />
      </div>
    </div>

    <!-- 系統狀態卡片 -->
    <div class="row q-gutter-md q-mb-lg">
      <div class="col-12 col-md-3">
        <q-card class="bg-blue-1">
          <q-card-section>
            <div class="text-h6 text-blue-8">活躍任務</div>
            <div class="text-h4 text-blue-9">{{ systemStatus.active_tasks || 0 }}</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="bg-green-1">
          <q-card-section>
            <div class="text-h6 text-green-8">並行計算</div>
            <div class="text-h4 text-green-9">{{ systemStatus.concurrent_calculations || 0 }}</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="bg-orange-1">
          <q-card-section>
            <div class="text-h6 text-orange-8">記憶體使用</div>
            <div class="text-h4 text-orange-9">{{ systemStatus.memory_usage_percent || 0 }}%</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-3">
        <q-card class="bg-purple-1">
          <q-card-section>
            <div class="text-h6 text-purple-8">佇列長度</div>
            <div class="text-h4 text-purple-9">{{ systemStatus.queue_length || 0 }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 功能選項卡 -->
    <q-tabs v-model="activeTab" class="text-grey-7" active-color="primary" indicator-color="primary">
      <q-tab name="tasks" label="任務管理" icon="assignment" />
      <q-tab name="results" label="結果查詢" icon="search" />
      <q-tab name="system" label="系統監控" icon="monitor" />
      <q-tab name="history" label="歷史記錄" icon="history" />
    </q-tabs>

    <q-separator />

    <q-tab-panels v-model="activeTab" animated>
      <!-- 任務管理面板 -->
      <q-tab-panel name="tasks">
        <div class="row q-gutter-md q-mb-md">
          <div class="col-auto">
            <q-select
              v-model="taskFilters.status"
              :options="taskStatusOptions"
              label="任務狀態"
              clearable
              style="min-width: 150px"
              @update:model-value="loadTasks"
            />
          </div>
          <div class="col-auto">
            <q-select
              v-model="taskFilters.lottoType"
              :options="lottoTypeOptions"
              label="彩種"
              clearable
              style="min-width: 150px"
              @update:model-value="loadTasks"
            />
          </div>
          <div class="col-auto">
            <q-btn
              color="positive"
              icon="add"
              label="手動觸發計算"
              @click="showTriggerDialog = true"
            />
          </div>
        </div>

        <!-- 任務列表 -->
        <q-table
          :rows="tasks"
          :columns="taskColumns"
          :loading="loading"
          :pagination="taskPagination"
          @request="onTaskRequest"
          row-key="id"
          binary-state-sort
        >
          <template v-slot:body-cell-task_status="props">
            <q-td :props="props">
              <q-badge
                :color="getStatusColor(props.value)"
                :label="getStatusLabel(props.value)"
              />
            </q-td>
          </template>

          <template v-slot:body-cell-progress_percentage="props">
            <q-td :props="props">
              <q-linear-progress
                :value="parseFloat(props.value) / 100"
                color="primary"
                size="md"
                class="q-mt-sm"
              />
              <div class="text-center text-caption">{{ props.value }}%</div>
            </q-td>
          </template>

          <template v-slot:body-cell-actions="props">
            <q-td :props="props">
              <q-btn
                flat
                round
                color="primary"
                icon="visibility"
                size="sm"
                @click="viewTaskDetail(props.row)"
              >
                <q-tooltip>查看詳情</q-tooltip>
              </q-btn>
              <q-btn
                v-if="props.row.task_status === 'running'"
                flat
                round
                color="negative"
                icon="stop"
                size="sm"
                @click="stopTask(props.row)"
              >
                <q-tooltip>停止任務</q-tooltip>
              </q-btn>
            </q-td>
          </template>
        </q-table>
      </q-tab-panel>

      <!-- 結果查詢面板 -->
      <q-tab-panel name="results">
        <div class="row q-gutter-md q-mb-md">
          <div class="col-auto">
            <q-select
              v-model="resultQuery.lottoType"
              :options="lottoTypeOptions"
              label="彩種"
              style="min-width: 150px"
            />
          </div>
          <div class="col-auto">
            <q-input
              v-model="resultQuery.period"
              label="期數"
              type="number"
              style="min-width: 150px"
            />
          </div>
          <div class="col-auto">
            <q-btn
              color="primary"
              icon="search"
              label="查詢結果"
              @click="queryAnalysisResults"
              :disable="!resultQuery.lottoType || !resultQuery.period"
            />
          </div>
          <div class="col-auto">
            <q-btn
              color="secondary"
              icon="download"
              label="下載Excel"
              @click="downloadExcel"
              :disable="!resultQuery.lottoType || !resultQuery.period"
            />
          </div>
        </div>

        <!-- 結果摘要 -->
        <q-card v-if="resultSummary" class="q-mb-md">
          <q-card-section>
            <div class="text-h6">計算結果摘要</div>
            <div class="row q-gutter-md q-mt-sm">
              <div class="col-auto">
                <div class="text-caption text-grey-6">總組合數</div>
                <div class="text-h6">{{ resultSummary.total_combinations }}</div>
              </div>
              <div class="col-auto">
                <div class="text-caption text-grey-6">成功組合數</div>
                <div class="text-h6 text-positive">{{ resultSummary.successful_combinations }}</div>
              </div>
              <div class="col-auto">
                <div class="text-caption text-grey-6">失敗組合數</div>
                <div class="text-h6 text-negative">{{ resultSummary.failed_combinations }}</div>
              </div>
              <div class="col-auto">
                <div class="text-caption text-grey-6">平均計算時間</div>
                <div class="text-h6">{{ resultSummary.average_calculation_time?.toFixed(2) }}秒</div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- 結果列表 -->
        <q-card v-if="analysisResults.length > 0">
          <q-card-section>
            <div class="text-h6">計算結果 ({{ analysisResults.length }} 筆)</div>
          </q-card-section>
          <q-separator />
          <q-card-section>
            <q-table
              :rows="analysisResults"
              :columns="resultColumns"
              :pagination="{ rowsPerPage: 20 }"
              row-key="id"
              dense
            >
              <template v-slot:body-cell-parameters="props">
                <q-td :props="props">
                  ({{ props.row.comb1 }},{{ props.row.comb2 }},{{ props.row.comb3 }})-P{{ props.row.period_num }}-R{{ props.row.max_range }}
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </q-tab-panel>

      <!-- 系統監控面板 -->
      <q-tab-panel name="system">
        <div class="row q-gutter-md">
          <div class="col-12 col-md-6">
            <q-card>
              <q-card-section>
                <div class="text-h6">系統配置</div>
              </q-card-section>
              <q-separator />
              <q-card-section>
                <q-form @submit="updateConfig">
                  <q-input
                    v-model.number="systemConfig.max_concurrent_calculations"
                    label="最大並行計算數量"
                    type="number"
                    :min="1"
                    :max="10"
                    class="q-mb-md"
                  />
                  <q-input
                    v-model.number="systemConfig.memory_threshold_percent"
                    label="記憶體閾值 (%)"
                    type="number"
                    :min="50"
                    :max="95"
                    class="q-mb-md"
                  />
                  <q-toggle
                    v-model="systemConfig.auto_gc_enabled"
                    label="自動垃圾回收"
                    class="q-mb-md"
                  />
                  <q-btn
                    type="submit"
                    color="primary"
                    label="更新配置"
                    :loading="configLoading"
                  />
                </q-form>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-6">
            <q-card>
              <q-card-section>
                <div class="text-h6">系統狀態詳情</div>
              </q-card-section>
              <q-separator />
              <q-card-section>
                <div class="q-gutter-sm">
                  <div><strong>記憶體使用:</strong> {{ systemStatus.memory_usage_mb }}MB ({{ systemStatus.memory_usage_percent }}%)</div>
                  <div><strong>活躍任務:</strong> {{ systemStatus.active_tasks }}</div>
                  <div><strong>並行計算:</strong> {{ systemStatus.concurrent_calculations }}</div>
                  <div><strong>佇列長度:</strong> {{ systemStatus.queue_length }}</div>
                  <div><strong>最後GC時間:</strong> {{ formatDate(systemStatus.last_gc_time) }}</div>
                  <div><strong>運行時間:</strong> {{ formatDuration(systemStatus.uptime_seconds) }}</div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-tab-panel>

      <!-- 歷史記錄面板 -->
      <q-tab-panel name="history">
        <div class="row q-gutter-md q-mb-md">
          <div class="col-auto">
            <q-select
              v-model="historyQuery.lottoType"
              :options="lottoTypeOptions"
              label="彩種"
              style="min-width: 150px"
            />
          </div>
          <div class="col-auto">
            <q-input
              v-model.number="historyQuery.limit"
              label="顯示筆數"
              type="number"
              :min="1"
              :max="100"
              style="min-width: 150px"
            />
          </div>
          <div class="col-auto">
            <q-btn
              color="primary"
              icon="search"
              label="查詢歷史"
              @click="loadHistory"
              :disable="!historyQuery.lottoType"
            />
          </div>
        </div>

        <!-- 歷史記錄列表 -->
        <q-table
          :rows="historyRecords"
          :columns="historyColumns"
          :loading="historyLoading"
          row-key="id"
          :pagination="{ rowsPerPage: 20 }"
        >
          <template v-slot:body-cell-task_status="props">
            <q-td :props="props">
              <q-badge
                :color="getStatusColor(props.value)"
                :label="getStatusLabel(props.value)"
              />
            </q-td>
          </template>

          <template v-slot:body-cell-success_rate="props">
            <q-td :props="props">
              <q-linear-progress
                :value="parseFloat(props.value) / 100"
                :color="parseFloat(props.value) > 90 ? 'positive' : parseFloat(props.value) > 70 ? 'warning' : 'negative'"
                size="md"
                class="q-mt-sm"
              />
              <div class="text-center text-caption">{{ props.value }}%</div>
            </q-td>
          </template>
        </q-table>
      </q-tab-panel>
    </q-tab-panels>

    <!-- 手動觸發計算對話框 -->
    <q-dialog v-model="showTriggerDialog">
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">手動觸發計算</div>
        </q-card-section>
        <q-separator />
        <q-card-section>
          <q-form @submit="triggerCalculation">
            <q-select
              v-model="triggerForm.lottoType"
              :options="lottoTypeOptions"
              label="彩種"
              class="q-mb-md"
              :rules="[val => !!val || '請選擇彩種']"
            />
            <q-input
              v-model.number="triggerForm.period"
              label="期數"
              type="number"
              class="q-mb-md"
              :rules="[val => !!val || '請輸入期數']"
            />
            <q-toggle
              v-model="triggerForm.forceRecalculate"
              label="強制重新計算"
              class="q-mb-md"
            />
            <div class="row justify-end q-gutter-sm">
              <q-btn flat label="取消" v-close-popup />
              <q-btn
                type="submit"
                color="primary"
                label="開始計算"
                :loading="triggerLoading"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 任務詳情對話框 -->
    <q-dialog v-model="showTaskDetail" maximized>
      <q-card v-if="selectedTask">
        <q-card-section class="row items-center">
          <div class="text-h6">任務詳情 - {{ selectedTask.lotto_type }} 期數 {{ selectedTask.period }}</div>
          <q-space />
          <q-btn flat round icon="close" v-close-popup />
        </q-card-section>
        <q-separator />
        <q-card-section>
          <!-- 任務詳情內容 -->
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-card flat bordered>
                <q-card-section>
                  <div class="text-subtitle1">基本資訊</div>
                  <div class="q-mt-sm">
                    <div><strong>任務ID:</strong> {{ selectedTask.id }}</div>
                    <div><strong>彩種:</strong> {{ selectedTask.lotto_type }}</div>
                    <div><strong>期數:</strong> {{ selectedTask.period }}</div>
                    <div><strong>狀態:</strong> 
                      <q-badge :color="getStatusColor(selectedTask.task_status)" :label="getStatusLabel(selectedTask.task_status)" />
                    </div>
                    <div><strong>優先級:</strong> {{ selectedTask.priority }}</div>
                    <div><strong>觸發方式:</strong> {{ selectedTask.triggered_by }}</div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
            <div class="col-12 col-md-6">
              <q-card flat bordered>
                <q-card-section>
                  <div class="text-subtitle1">進度資訊</div>
                  <div class="q-mt-sm">
                    <div><strong>總組合數:</strong> {{ selectedTask.total_combinations }}</div>
                    <div><strong>已完成:</strong> {{ selectedTask.completed_combinations }}</div>
                    <div><strong>失敗數:</strong> {{ selectedTask.failed_combinations }}</div>
                    <div><strong>剩餘數:</strong> {{ selectedTask.total_combinations - selectedTask.completed_combinations - selectedTask.failed_combinations }}</div>
                    <div class="q-mt-sm">
                      <q-linear-progress
                        :value="parseFloat(selectedTask.progress_percentage) / 100"
                        color="primary"
                        size="lg"
                      />
                      <div class="text-center text-caption q-mt-xs">{{ selectedTask.progress_percentage }}%</div>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'src/boot/axios'

// Quasar 實例
const $q = useQuasar()

// 響應式資料
const loading = ref(false)
const activeTab = ref('tasks')

// 系統狀態
const systemStatus = ref({
  memory_usage_mb: 0,
  memory_usage_percent: 0,
  active_tasks: 0,
  concurrent_calculations: 0,
  queue_length: 0,
  last_gc_time: null,
  uptime_seconds: 0,
  status: 'unknown'
})

// 任務管理
const tasks = ref([])
const taskPagination = ref({
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0
})
const taskFilters = ref({
  status: null,
  lottoType: null
})

// 結果查詢
const resultQuery = ref({
  lottoType: null,
  period: null
})
const analysisResults = ref([])
const resultSummary = ref(null)

// 系統配置
const systemConfig = ref({
  max_concurrent_calculations: 3,
  memory_threshold_percent: 80,
  auto_gc_enabled: true
})
const configLoading = ref(false)

// 歷史記錄
const historyQuery = ref({
  lottoType: null,
  limit: 10
})
const historyRecords = ref([])
const historyLoading = ref(false)

// 對話框
const showTriggerDialog = ref(false)
const showTaskDetail = ref(false)
const selectedTask = ref(null)

// 觸發計算表單
const triggerForm = ref({
  lottoType: null,
  period: null,
  forceRecalculate: false
})
const triggerLoading = ref(false)

// 選項
const taskStatusOptions = [
  { label: '待處理', value: 'pending' },
  { label: '執行中', value: 'running' },
  { label: '已完成', value: 'completed' },
  { label: '失敗', value: 'failed' },
  { label: '已停止', value: 'stopped' }
]

const lottoTypeOptions = [
  { label: '今彩539', value: 'daily539' },
  { label: '大樂透', value: 'lotto649' },
  { label: '威力彩', value: 'super_lotto638' },
  { label: '六合彩', value: 'lotto_hk' }
]

// 表格欄位定義
const taskColumns = [
  { name: 'id', label: 'ID', field: 'id', align: 'left', sortable: true },
  { name: 'lotto_type', label: '彩種', field: 'lotto_type', align: 'left' },
  { name: 'period', label: '期數', field: 'period', align: 'left', sortable: true },
  { name: 'task_status', label: '狀態', field: 'task_status', align: 'center' },
  { name: 'progress_percentage', label: '進度', field: 'progress_percentage', align: 'center' },
  { name: 'start_time', label: '開始時間', field: 'start_time', align: 'left', format: (val: string) => formatDate(val) },
  { name: 'created_at', label: '建立時間', field: 'created_at', align: 'left', format: (val: string) => formatDate(val) },
  { name: 'actions', label: '操作', field: '', align: 'center' }
]

const resultColumns = [
  { name: 'parameters', label: '參數組合', field: '', align: 'left' },
  { name: 'analysis_periods', label: '分析期數', field: 'analysis_periods', align: 'center' },
  { name: 'calculation_duration', label: '計算時間(秒)', field: 'calculation_duration', align: 'center' },
  { name: 'created_at', label: '建立時間', field: 'created_at', align: 'left', format: (val: string) => formatDate(val) }
]

const historyColumns = [
  { name: 'id', label: 'ID', field: 'id', align: 'left' },
  { name: 'period', label: '期數', field: 'period', align: 'left', sortable: true },
  { name: 'task_status', label: '狀態', field: 'task_status', align: 'center' },
  { name: 'success_rate', label: '成功率', field: 'success_rate', align: 'center' },
  { name: 'duration', label: '執行時間(秒)', field: 'duration', align: 'center' },
  { name: 'triggered_by', label: '觸發方式', field: 'triggered_by', align: 'center' },
  { name: 'created_at', label: '建立時間', field: 'created_at', align: 'left', format: (val: string) => formatDate(val) }
]

// 計算屬性
const isAdmin = computed(() => {
  // 這裡應該從用戶狀態中取得管理員權限
  return true // 暫時設為 true
})

// 方法
const refreshData = async () => {
  await Promise.all([
    loadSystemStatus(),
    loadTasks(),
  ])
}

const loadSystemStatus = async () => {
  try {
    const response = await api.get('/api/admin/ball-follow/system-status')
    if (response.data.success) {
      systemStatus.value = response.data.data
    }
  } catch (error) {
    console.error('載入系統狀態失敗:', error)
  }
}

const loadTasks = async () => {
  loading.value = true
  try {
    const params: any = {
      page: taskPagination.value.page,
      limit: taskPagination.value.rowsPerPage
    }

    if (taskFilters.value.status) {
      params.status = taskFilters.value.status
    }
    if (taskFilters.value.lottoType) {
      params.lotto_type = taskFilters.value.lottoType
    }

    const response = await api.get('/api/admin/ball-follow/tasks', { params })
    if (response.data.success) {
      tasks.value = response.data.data.tasks
      taskPagination.value.rowsNumber = response.data.data.pagination.total
    }
  } catch (error) {
    console.error('載入任務列表失敗:', error)
    $q.notify({
      type: 'negative',
      message: '載入任務列表失敗'
    })
  } finally {
    loading.value = false
  }
}

const onTaskRequest = (props: any) => {
  taskPagination.value.page = props.pagination.page
  taskPagination.value.rowsPerPage = props.pagination.rowsPerPage
  loadTasks()
}

const viewTaskDetail = async (task: any) => {
  try {
    const response = await api.get(`/api/admin/ball-follow/tasks/${task.id}`)
    if (response.data.success) {
      selectedTask.value = response.data.data
      showTaskDetail.value = true
    }
  } catch (error) {
    console.error('載入任務詳情失敗:', error)
    $q.notify({
      type: 'negative',
      message: '載入任務詳情失敗'
    })
  }
}

const stopTask = async (task: any) => {
  $q.dialog({
    title: '確認停止',
    message: `確定要停止任務 ${task.id} 嗎？`,
    cancel: true,
    persistent: true
  }).onOk(async () => {
    try {
      const response = await api.post(`/api/admin/ball-follow/tasks/${task.id}/stop`)
      if (response.data.success) {
        $q.notify({
          type: 'positive',
          message: '任務已停止'
        })
        loadTasks()
      }
    } catch (error) {
      console.error('停止任務失敗:', error)
      $q.notify({
        type: 'negative',
        message: '停止任務失敗'
      })
    }
  })
}

const triggerCalculation = async () => {
  triggerLoading.value = true
  try {
    const response = await api.post('/api/admin/ball-follow/trigger', {
      lotto_type: triggerForm.value.lottoType,
      period: triggerForm.value.period,
      force_recalculate: triggerForm.value.forceRecalculate
    })

    if (response.data.success) {
      $q.notify({
        type: 'positive',
        message: '計算任務已建立'
      })
      showTriggerDialog.value = false
      triggerForm.value = {
        lottoType: null,
        period: null,
        forceRecalculate: false
      }
      loadTasks()
    }
  } catch (error: any) {
    console.error('觸發計算失敗:', error)
    $q.notify({
      type: 'negative',
      message: error.response?.data?.message || '觸發計算失敗'
    })
  } finally {
    triggerLoading.value = false
  }
}

const queryAnalysisResults = async () => {
  loading.value = true
  try {
    const [resultsResponse, summaryResponse] = await Promise.all([
      api.get('/api/admin/ball-follow/results', {
        params: {
          lotto_type: resultQuery.value.lottoType,
          period: resultQuery.value.period
        }
      }),
      api.get('/api/admin/ball-follow/summary', {
        params: {
          lotto_type: resultQuery.value.lottoType,
          period: resultQuery.value.period
        }
      })
    ])

    if (resultsResponse.data.success) {
      analysisResults.value = resultsResponse.data.data.grouped_results ?
        Object.values(resultsResponse.data.data.grouped_results).flat() : []
    }

    if (summaryResponse.data.success) {
      resultSummary.value = summaryResponse.data.data
    }
  } catch (error) {
    console.error('查詢結果失敗:', error)
    $q.notify({
      type: 'negative',
      message: '查詢結果失敗'
    })
  } finally {
    loading.value = false
  }
}

const downloadExcel = async () => {
  try {
    const response = await api.get('/api/admin/ball-follow/export', {
      params: {
        lotto_type: resultQuery.value.lottoType,
        period: resultQuery.value.period,
        format: 'excel'
      },
      responseType: 'blob'
    })

    // 建立下載連結
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `ball_follow_analysis_${resultQuery.value.lottoType}_${resultQuery.value.period}.xlsx`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

    $q.notify({
      type: 'positive',
      message: 'Excel檔案下載完成'
    })
  } catch (error: any) {
    console.error('下載Excel失敗:', error)
    if (error.response?.status === 202) {
      $q.notify({
        type: 'warning',
        message: '計算尚未完成，請稍後再試'
      })
    } else {
      $q.notify({
        type: 'negative',
        message: '下載Excel失敗'
      })
    }
  }
}

const updateConfig = async () => {
  configLoading.value = true
  try {
    const response = await api.put('/api/admin/ball-follow/config', systemConfig.value)
    if (response.data.success) {
      $q.notify({
        type: 'positive',
        message: '配置已更新'
      })
    }
  } catch (error) {
    console.error('更新配置失敗:', error)
    $q.notify({
      type: 'negative',
      message: '更新配置失敗'
    })
  } finally {
    configLoading.value = false
  }
}

const loadHistory = async () => {
  historyLoading.value = true
  try {
    const response = await api.get('/api/admin/ball-follow/history', {
      params: {
        lotto_type: historyQuery.value.lottoType,
        limit: historyQuery.value.limit
      }
    })

    if (response.data.success) {
      historyRecords.value = response.data.data.history
    }
  } catch (error) {
    console.error('載入歷史記錄失敗:', error)
    $q.notify({
      type: 'negative',
      message: '載入歷史記錄失敗'
    })
  } finally {
    historyLoading.value = false
  }
}

// 輔助函數
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'grey',
    running: 'blue',
    completed: 'green',
    failed: 'red',
    stopped: 'orange'
  }
  return colors[status] || 'grey'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待處理',
    running: '執行中',
    completed: '已完成',
    failed: '失敗',
    stopped: '已停止'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-TW')
}

const formatDuration = (seconds: number) => {
  if (!seconds) return '-'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}小時${minutes}分鐘`
}

// 生命週期
onMounted(() => {
  if (!isAdmin.value) {
    $q.notify({
      type: 'negative',
      message: '需要管理員權限才能存取此頁面'
    })
    return
  }

  refreshData()

  // 設定自動重新整理
  const interval = setInterval(() => {
    if (activeTab.value === 'tasks') {
      loadSystemStatus()
      loadTasks()
    }
  }, 30000) // 每30秒重新整理一次

  // 清理定時器
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style scoped>
.q-page {
  max-width: 1400px;
  margin: 0 auto;
}

.q-card {
  border-radius: 8px;
}

.q-table {
  border-radius: 8px;
}

.text-h4 {
  font-weight: 600;
}

.text-h6 {
  font-weight: 500;
}
</style>
