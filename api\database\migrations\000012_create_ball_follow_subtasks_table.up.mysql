CREATE TABLE IF NOT EXISTS `ball_follow_subtasks` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `task_id` BIGINT UNSIGNED NOT NULL COMMENT '關聯主任務ID',
    `comb1` TINYINT UNSIGNED NOT NULL COMMENT '拖牌組合第一組數量',
    `comb2` TINYINT UNSIGNED NOT NULL COMMENT '拖牌組合第二組數量',
    `comb3` TINYINT UNSIGNED NOT NULL COMMENT '拖牌組合第三組數量',
    `period_num` SMALLINT UNSIGNED NOT NULL COMMENT '推算期數',
    `max_range` TINYINT UNSIGNED NOT NULL COMMENT '拖牌區間',
    `subtask_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '子任務狀態(pending/running/completed/failed)',
    `start_time` TIMESTAMP NULL COMMENT '開始時間',
    `end_time` TIMESTAMP NULL COMMENT '結束時間',
    `duration` INT UNSIGNED NULL COMMENT '執行時間(秒)',
    `error_message` TEXT NULL COMMENT '錯誤訊息',
    `retry_count` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重試次數',
    `result_id` BIGINT UNSIGNED NULL COMMENT '關聯結果表ID',
    `memory_usage_mb` INT UNSIGNED NULL COMMENT '記憶體使用量(MB)',
    `calculation_hash` VARCHAR(64) NULL COMMENT '計算參數雜湊值',
    `worker_id` VARCHAR(50) NULL COMMENT '執行該任務的工作程序ID',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_subtask_status` (`subtask_status`),
    INDEX `idx_parameters` (`comb1`, `comb2`, `comb3`, `period_num`, `max_range`),
    INDEX `idx_result_id` (`result_id`),
    INDEX `idx_calculation_hash` (`calculation_hash`),
    INDEX `idx_task_status` (`task_id`, `subtask_status`),
    INDEX `idx_start_time` (`start_time`),
    
    -- 外鍵約束
    FOREIGN KEY (`task_id`) REFERENCES `ball_follow_calculation_tasks`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`result_id`) REFERENCES `ball_follow_analysis_results`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='版路分析參數組合子任務表';
