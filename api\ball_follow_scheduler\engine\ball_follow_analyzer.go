package engine

import (
	"context"
	"fmt"
	"sort"
	"time"
)

// DrawResult 開獎結果
type DrawResult struct {
	Period  string `json:"period"`
	Numbers []int  `json:"numbers"`
}

// AnalysisConfig 分析配置
type AnalysisConfig struct {
	FirstGroupSize  int `json:"first_group_size"`
	SecondGroupSize int `json:"second_group_size"`
	TargetGroupSize int `json:"target_group_size"`
	MaxRange        int `json:"max_range"`
	LookAheadCount  int `json:"look_ahead_count"`
}

// StatResult 統計結果
type StatResult struct {
	Period        string  `json:"period"`
	TargetNumbers []int   `json:"target_numbers"`
	Frequency     int     `json:"frequency"`
	Probability   float64 `json:"probability"`
	TailNumbers   []int   `json:"tail_numbers"`
}

// AnalysisResults 分析結果
type AnalysisResults struct {
	StatResults     []StatResult `json:"stat_results"`
	SourcePeriods   []string     `json:"source_periods"`
	StartPeriod     string       `json:"start_period"`
	EndPeriod       string       `json:"end_period"`
	TotalAnalyzed   int          `json:"total_analyzed"`
	AnalysisTime    time.Time    `json:"analysis_time"`
}

// BallFollowAnalyzer 版路分析器
type BallFollowAnalyzer struct {
	config          AnalysisConfig
	drawResults     []DrawResult
	progressCallback func(float64)
}

// NewBallFollowAnalyzer 建立新的版路分析器
func NewBallFollowAnalyzer(config AnalysisConfig, drawResults []DrawResult) *BallFollowAnalyzer {
	return &BallFollowAnalyzer{
		config:      config,
		drawResults: drawResults,
	}
}

// SetProgressCallback 設定進度回調函數
func (a *BallFollowAnalyzer) SetProgressCallback(callback func(float64)) {
	a.progressCallback = callback
}

// Analyze 執行版路分析
func (a *BallFollowAnalyzer) Analyze(ctx context.Context) (*AnalysisResults, error) {
	if len(a.drawResults) == 0 {
		return nil, fmt.Errorf("no draw results provided")
	}
	
	// 初始化結果
	results := &AnalysisResults{
		AnalysisTime:  time.Now(),
		TotalAnalyzed: len(a.drawResults),
		StartPeriod:   a.drawResults[0].Period,
		EndPeriod:     a.drawResults[len(a.drawResults)-1].Period,
	}
	
	// 收集所有期數
	for _, draw := range a.drawResults {
		results.SourcePeriods = append(results.SourcePeriods, draw.Period)
	}
	
	// 執行版路分析計算
	statResults, err := a.performBallFollowAnalysis(ctx)
	if err != nil {
		return nil, fmt.Errorf("ball follow analysis failed: %w", err)
	}
	
	results.StatResults = statResults
	
	return results, nil
}

// performBallFollowAnalysis 執行版路分析核心邏輯
func (a *BallFollowAnalyzer) performBallFollowAnalysis(ctx context.Context) ([]StatResult, error) {
	var statResults []StatResult
	totalSteps := len(a.drawResults) - a.config.LookAheadCount
	
	// 遍歷每一期進行分析
	for i := 0; i < totalSteps; i++ {
		// 檢查上下文是否已取消
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}
		
		// 更新進度
		if a.progressCallback != nil {
			progress := float64(i) / float64(totalSteps)
			a.progressCallback(progress)
		}
		
		// 分析當前期數
		result, err := a.analyzePeriod(i)
		if err != nil {
			// 記錄錯誤但繼續處理其他期數
			continue
		}
		
		if result != nil {
			statResults = append(statResults, *result)
		}
	}
	
	// 完成進度
	if a.progressCallback != nil {
		a.progressCallback(1.0)
	}
	
	return statResults, nil
}

// analyzePeriod 分析特定期數
func (a *BallFollowAnalyzer) analyzePeriod(periodIndex int) (*StatResult, error) {
	if periodIndex >= len(a.drawResults)-a.config.LookAheadCount {
		return nil, fmt.Errorf("period index out of range")
	}
	
	currentDraw := a.drawResults[periodIndex]
	
	// 取得目標期數（下一期或指定的前瞻期數）
	targetIndex := periodIndex + a.config.LookAheadCount
	if targetIndex >= len(a.drawResults) {
		return nil, fmt.Errorf("target period index out of range")
	}
	
	targetDraw := a.drawResults[targetIndex]
	
	// 執行版路分析邏輯
	targetNumbers := a.calculateTargetNumbers(currentDraw, targetDraw)
	if len(targetNumbers) == 0 {
		return nil, nil // 沒有符合條件的號碼
	}
	
	// 計算頻率和機率
	frequency := len(targetNumbers)
	probability := a.calculateProbability(targetNumbers, targetDraw.Numbers)
	
	// 計算尾數
	tailNumbers := a.calculateTailNumbers(targetNumbers)
	
	return &StatResult{
		Period:        currentDraw.Period,
		TargetNumbers: targetNumbers,
		Frequency:     frequency,
		Probability:   probability,
		TailNumbers:   tailNumbers,
	}, nil
}

// calculateTargetNumbers 計算目標號碼
func (a *BallFollowAnalyzer) calculateTargetNumbers(currentDraw, targetDraw DrawResult) []int {
	// 根據配置的拖牌組合進行分析
	currentNumbers := currentDraw.Numbers
	
	// 確保號碼已排序
	sort.Ints(currentNumbers)
	
	var targetNumbers []int
	
	// 根據拖牌組合配置選擇號碼
	if a.config.FirstGroupSize > 0 && len(currentNumbers) >= a.config.FirstGroupSize {
		// 取前N個號碼
		for i := 0; i < a.config.FirstGroupSize && i < len(currentNumbers); i++ {
			targetNumbers = append(targetNumbers, currentNumbers[i])
		}
	}
	
	if a.config.SecondGroupSize > 0 && len(currentNumbers) >= a.config.SecondGroupSize {
		// 取中間N個號碼
		start := (len(currentNumbers) - a.config.SecondGroupSize) / 2
		for i := 0; i < a.config.SecondGroupSize && start+i < len(currentNumbers); i++ {
			num := currentNumbers[start+i]
			if !contains(targetNumbers, num) {
				targetNumbers = append(targetNumbers, num)
			}
		}
	}
	
	if a.config.TargetGroupSize > 0 && len(currentNumbers) >= a.config.TargetGroupSize {
		// 取後N個號碼
		start := len(currentNumbers) - a.config.TargetGroupSize
		for i := 0; i < a.config.TargetGroupSize && start+i < len(currentNumbers); i++ {
			num := currentNumbers[start+i]
			if !contains(targetNumbers, num) {
				targetNumbers = append(targetNumbers, num)
			}
		}
	}
	
	// 應用拖牌區間篩選
	if a.config.MaxRange > 0 {
		targetNumbers = a.applyRangeFilter(targetNumbers)
	}
	
	return targetNumbers
}

// applyRangeFilter 應用拖牌區間篩選
func (a *BallFollowAnalyzer) applyRangeFilter(numbers []int) []int {
	if len(numbers) == 0 {
		return numbers
	}
	
	sort.Ints(numbers)
	
	var filtered []int
	
	// 檢查號碼範圍是否在指定區間內
	for i := 0; i < len(numbers); i++ {
		for j := i + 1; j < len(numbers); j++ {
			if numbers[j]-numbers[i] <= a.config.MaxRange {
				if !contains(filtered, numbers[i]) {
					filtered = append(filtered, numbers[i])
				}
				if !contains(filtered, numbers[j]) {
					filtered = append(filtered, numbers[j])
				}
			}
		}
	}
	
	// 如果沒有符合區間條件的號碼，返回原始號碼
	if len(filtered) == 0 {
		return numbers
	}
	
	return filtered
}

// calculateProbability 計算機率
func (a *BallFollowAnalyzer) calculateProbability(predictNumbers, actualNumbers []int) float64 {
	if len(predictNumbers) == 0 {
		return 0.0
	}
	
	hits := 0
	for _, predicted := range predictNumbers {
		if contains(actualNumbers, predicted) {
			hits++
		}
	}
	
	return float64(hits) / float64(len(predictNumbers))
}

// calculateTailNumbers 計算尾數
func (a *BallFollowAnalyzer) calculateTailNumbers(numbers []int) []int {
	var tails []int
	seen := make(map[int]bool)
	
	for _, num := range numbers {
		tail := num % 10
		if !seen[tail] {
			seen[tail] = true
			tails = append(tails, tail)
		}
	}
	
	sort.Ints(tails)
	return tails
}

// 輔助函數
func contains(slice []int, item int) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetAnalysisStatistics 取得分析統計資訊
func (a *BallFollowAnalyzer) GetAnalysisStatistics(results []StatResult) map[string]interface{} {
	if len(results) == 0 {
		return map[string]interface{}{
			"total_periods":      0,
			"average_frequency":  0.0,
			"average_probability": 0.0,
			"max_frequency":      0,
			"min_frequency":      0,
		}
	}
	
	totalFreq := 0
	totalProb := 0.0
	maxFreq := results[0].Frequency
	minFreq := results[0].Frequency
	
	for _, result := range results {
		totalFreq += result.Frequency
		totalProb += result.Probability
		
		if result.Frequency > maxFreq {
			maxFreq = result.Frequency
		}
		if result.Frequency < minFreq {
			minFreq = result.Frequency
		}
	}
	
	return map[string]interface{}{
		"total_periods":       len(results),
		"average_frequency":   float64(totalFreq) / float64(len(results)),
		"average_probability": totalProb / float64(len(results)),
		"max_frequency":       maxFreq,
		"min_frequency":       minFreq,
		"total_frequency":     totalFreq,
	}
}

// ValidateConfig 驗證分析配置
func (a *BallFollowAnalyzer) ValidateConfig() error {
	if a.config.FirstGroupSize < 0 || a.config.SecondGroupSize < 0 || a.config.TargetGroupSize < 0 {
		return fmt.Errorf("group sizes cannot be negative")
	}
	
	if a.config.MaxRange < 0 {
		return fmt.Errorf("max range cannot be negative")
	}
	
	if a.config.LookAheadCount <= 0 {
		return fmt.Errorf("look ahead count must be positive")
	}
	
	totalGroupSize := a.config.FirstGroupSize + a.config.SecondGroupSize + a.config.TargetGroupSize
	if totalGroupSize == 0 {
		return fmt.Errorf("at least one group size must be greater than 0")
	}
	
	return nil
}
