package scheduler

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/robfig/cron/v3"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"lottery/ball_follow_scheduler/config"
	"lottery/ball_follow_scheduler/engine"
	"lottery/ball_follow_scheduler/memory"
	"lottery/ball_follow_scheduler/notification"
	"lottery/ball_follow_scheduler/worker"
	. "lottery/models"
)

// Scheduler 版路分析排程服務
type Scheduler struct {
	config       *config.Config
	db           *gorm.DB
	cron         *cron.Cron
	engine       *engine.CalculationEngine
	memoryMgr    *memory.Manager
	notifier     *notification.Notifier
	workerPool   *worker.Pool
	repository   *BallFollowRepository
	
	// 控制相關
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
	running      bool
	mu           sync.RWMutex
	
	// 統計資訊
	stats        *SchedulerStats
}

// SchedulerStats 排程服務統計資訊
type SchedulerStats struct {
	StartTime           time.Time `json:"start_time"`
	TotalTasksCreated   int64     `json:"total_tasks_created"`
	TotalTasksCompleted int64     `json:"total_tasks_completed"`
	TotalTasksFailed    int64     `json:"total_tasks_failed"`
	LastCheckTime       time.Time `json:"last_check_time"`
	LastTaskTime        time.Time `json:"last_task_time"`
	CurrentMemoryMB     int64     `json:"current_memory_mb"`
	PeakMemoryMB        int64     `json:"peak_memory_mb"`
	GCCount             int64     `json:"gc_count"`
	ErrorCount          int64     `json:"error_count"`
	mu                  sync.RWMutex
}

// NewScheduler 建立新的排程服務實例
func NewScheduler(cfg *config.Config, db *gorm.DB) (*Scheduler, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	// 建立 cron 排程器
	cronScheduler := cron.New(
		cron.WithLocation(getTimezone(cfg.Scheduler.Timezone)),
		cron.WithChain(
			cron.Recover(cron.DefaultLogger),
		),
	)
	
	// 建立資料庫倉庫
	repository := NewBallFollowRepository(db)
	
	// 建立記憶體管理器
	memoryMgr := memory.NewManager(cfg.Memory)
	
	// 建立通知器
	notifier := notification.NewNotifier(cfg.Notification)
	
	// 建立計算引擎
	calculationEngine := engine.NewCalculationEngine(cfg.Calculation, repository)
	
	// 建立工作程序池
	workerPool := worker.NewPool(cfg.Calculation.WorkerConfig, calculationEngine)
	
	scheduler := &Scheduler{
		config:     cfg,
		db:         db,
		cron:       cronScheduler,
		engine:     calculationEngine,
		memoryMgr:  memoryMgr,
		notifier:   notifier,
		workerPool: workerPool,
		repository: repository,
		ctx:        ctx,
		cancel:     cancel,
		stats: &SchedulerStats{
			StartTime: time.Now(),
		},
	}
	
	// 設定排程任務
	if err := scheduler.setupScheduledTasks(); err != nil {
		return nil, fmt.Errorf("failed to setup scheduled tasks: %w", err)
	}
	
	return scheduler, nil
}

// Start 啟動排程服務
func (s *Scheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if s.running {
		return fmt.Errorf("scheduler is already running")
	}
	
	log.Info("Starting Ball Follow Scheduler...")
	
	// 啟動記憶體管理器
	if err := s.memoryMgr.Start(s.ctx); err != nil {
		return fmt.Errorf("failed to start memory manager: %w", err)
	}
	
	// 啟動工作程序池
	if err := s.workerPool.Start(s.ctx); err != nil {
		return fmt.Errorf("failed to start worker pool: %w", err)
	}
	
	// 啟動 cron 排程器
	s.cron.Start()
	
	// 啟動監控協程
	s.wg.Add(1)
	go s.monitoringLoop()
	
	s.running = true
	
	log.Info("Ball Follow Scheduler started successfully")
	
	// 立即執行一次檢查
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		s.checkAndCreateTasks()
	}()
	
	return nil
}

// Stop 停止排程服務
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if !s.running {
		return nil
	}
	
	log.Info("Stopping Ball Follow Scheduler...")
	
	// 停止 cron 排程器
	cronCtx := s.cron.Stop()
	<-cronCtx.Done()
	
	// 取消上下文
	s.cancel()
	
	// 等待所有協程結束
	s.wg.Wait()
	
	// 停止工作程序池
	if err := s.workerPool.Stop(); err != nil {
		log.Errorf("Error stopping worker pool: %v", err)
	}
	
	// 停止記憶體管理器
	if err := s.memoryMgr.Stop(); err != nil {
		log.Errorf("Error stopping memory manager: %v", err)
	}
	
	s.running = false
	
	log.Info("Ball Follow Scheduler stopped")
	return nil
}

// Run 運行排程服務（阻塞直到收到停止信號）
func (s *Scheduler) Run() error {
	if err := s.Start(); err != nil {
		return err
	}
	
	// 等待停止信號
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	
	select {
	case sig := <-sigChan:
		log.Infof("Received signal %v, shutting down...", sig)
	case <-s.ctx.Done():
		log.Info("Context cancelled, shutting down...")
	}
	
	return s.Stop()
}

// setupScheduledTasks 設定排程任務
func (s *Scheduler) setupScheduledTasks() error {
	if !s.config.Scheduler.Enabled {
		log.Info("Scheduler is disabled in config")
		return nil
	}
	
	// 新增檢查新開獎號碼的排程任務
	_, err := s.cron.AddFunc(s.config.Scheduler.CheckSchedule, func() {
		s.wg.Add(1)
		go func() {
			defer s.wg.Done()
			s.checkAndCreateTasks()
		}()
	})
	
	if err != nil {
		return fmt.Errorf("failed to add scheduled task: %w", err)
	}
	
	log.Infof("Scheduled task added with cron expression: %s", s.config.Scheduler.CheckSchedule)
	return nil
}

// checkAndCreateTasks 檢查新開獎號碼並建立計算任務
func (s *Scheduler) checkAndCreateTasks() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic in checkAndCreateTasks: %v", r)
			s.incrementErrorCount()
		}
	}()
	
	s.updateLastCheckTime()
	
	log.Info("Checking for new lottery results...")
	
	// 檢查每個支援的彩種
	for _, lottoType := range s.config.Scheduler.SupportedLotteries {
		if err := s.checkLotteryType(LottoTypeStr(lottoType)); err != nil {
			log.Errorf("Error checking lottery type %s: %v", lottoType, err)
			s.incrementErrorCount()
			
			// 發送錯誤通知
			s.notifier.SendNotification(notification.NotificationLevelError,
				fmt.Sprintf("Error checking lottery type %s", lottoType),
				err.Error())
		}
	}
}

// checkLotteryType 檢查特定彩種的新開獎號碼
func (s *Scheduler) checkLotteryType(lottoType LottoTypeStr) error {
	// 取得最新期數
	latestPeriod, err := s.repository.GetLatestPeriodByLottoType(lottoType)
	if err != nil {
		return fmt.Errorf("failed to get latest period for %s: %w", lottoType, err)
	}
	
	if latestPeriod == 0 {
		log.Warnf("No lottery results found for %s", lottoType)
		return nil
	}
	
	// 檢查是否已經計算過
	calculated, err := s.repository.CheckPeriodCalculated(lottoType, latestPeriod)
	if err != nil {
		return fmt.Errorf("failed to check if period is calculated: %w", err)
	}
	
	if calculated {
		log.Infof("Period %d for %s already calculated, skipping", latestPeriod, lottoType)
		return nil
	}
	
	// 建立新的計算任務
	return s.createCalculationTask(lottoType, latestPeriod)
}

// createCalculationTask 建立計算任務
func (s *Scheduler) createCalculationTask(lottoType LottoTypeStr, period int) error {
	log.Infof("Creating calculation task for %s period %d", lottoType, period)
	
	// 計算總組合數
	totalCombinations := s.calculateTotalCombinations()
	
	// 建立主任務
	task := &BallFollowCalculationTask{
		LottoType:         lottoType,
		Period:            period,
		TaskStatus:        TaskStatusPending,
		TotalCombinations: uint16(totalCombinations),
		Priority:          uint8(s.config.Scheduler.TaskPriorities[string(lottoType)]),
		TriggeredBy:       TriggerTypeScheduler,
		ConfigSnapshot: BallFollowJSON{
			"max_concurrent_calculations": s.config.Calculation.MaxConcurrentCalculations,
			"memory_threshold_percent":    s.config.Memory.ThresholdPercent,
			"auto_gc_enabled":             s.config.Memory.AutoGCEnabled,
			"retry_limit":                 s.config.Calculation.RetryLimit,
			"calculation_timeout_minutes": int(s.config.Calculation.CalculationTimeout.Minutes()),
		},
	}
	
	// 儲存任務到資料庫
	if err := s.repository.CreateCalculationTask(task); err != nil {
		return fmt.Errorf("failed to create calculation task: %w", err)
	}
	
	// 建立子任務
	if err := s.createSubtasks(task.ID); err != nil {
		return fmt.Errorf("failed to create subtasks: %w", err)
	}
	
	// 將任務加入工作程序池
	if err := s.workerPool.SubmitTask(task.ID); err != nil {
		log.Errorf("Failed to submit task to worker pool: %v", err)
		// 不返回錯誤，任務已經建立，可以稍後重試
	}
	
	s.incrementTasksCreated()
	s.updateLastTaskTime()
	
	log.Infof("Successfully created calculation task %d for %s period %d with %d combinations",
		task.ID, lottoType, period, totalCombinations)
	
	return nil
}

// createSubtasks 建立子任務
func (s *Scheduler) createSubtasks(taskID uint64) error {
	var subtasks []BallFollowSubtask
	
	// 生成所有參數組合
	combRange := s.config.Calculation.ParameterCombinations.CombRange
	periodNumbers := s.config.Calculation.ParameterCombinations.PeriodNumbers
	maxRanges := s.config.Calculation.ParameterCombinations.MaxRanges
	
	for _, comb1 := range combRange {
		for _, comb2 := range combRange {
			for _, comb3 := range combRange {
				for _, periodNum := range periodNumbers {
					for _, maxRange := range maxRanges {
						subtask := BallFollowSubtask{
							TaskID:        taskID,
							Comb1:         uint8(comb1),
							Comb2:         uint8(comb2),
							Comb3:         uint8(comb3),
							PeriodNum:     uint16(periodNum),
							MaxRange:      uint8(maxRange),
							SubtaskStatus: SubtaskStatusPending,
						}
						subtasks = append(subtasks, subtask)
					}
				}
			}
		}
	}
	
	// 批量建立子任務
	return s.repository.CreateSubtasks(subtasks)
}

// calculateTotalCombinations 計算總組合數
func (s *Scheduler) calculateTotalCombinations() int {
	combRange := len(s.config.Calculation.ParameterCombinations.CombRange)
	periodNumbers := len(s.config.Calculation.ParameterCombinations.PeriodNumbers)
	maxRanges := len(s.config.Calculation.ParameterCombinations.MaxRanges)
	
	return combRange * combRange * combRange * periodNumbers * maxRanges
}

// monitoringLoop 監控循環
func (s *Scheduler) monitoringLoop() {
	defer s.wg.Done()
	
	ticker := time.NewTicker(s.config.Memory.MonitorInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.updateMemoryStats()
		}
	}
}

// 統計相關方法
func (s *Scheduler) updateLastCheckTime() {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()
	s.stats.LastCheckTime = time.Now()
}

func (s *Scheduler) updateLastTaskTime() {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()
	s.stats.LastTaskTime = time.Now()
}

func (s *Scheduler) incrementTasksCreated() {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()
	s.stats.TotalTasksCreated++
}

func (s *Scheduler) incrementErrorCount() {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()
	s.stats.ErrorCount++
}

func (s *Scheduler) updateMemoryStats() {
	currentMB := s.memoryMgr.GetCurrentMemoryMB()
	
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()
	
	s.stats.CurrentMemoryMB = currentMB
	if currentMB > s.stats.PeakMemoryMB {
		s.stats.PeakMemoryMB = currentMB
	}
}

// GetStats 取得統計資訊
func (s *Scheduler) GetStats() *SchedulerStats {
	s.stats.mu.RLock()
	defer s.stats.mu.RUnlock()
	
	// 返回副本避免併發問題
	statsCopy := *s.stats
	return &statsCopy
}

// IsRunning 檢查服務是否正在運行
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// 輔助函數
func getTimezone(tz string) *time.Location {
	if tz == "" {
		tz = "Asia/Taipei"
	}
	
	location, err := time.LoadLocation(tz)
	if err != nil {
		log.Warnf("Failed to load timezone %s, using UTC: %v", tz, err)
		return time.UTC
	}
	
	return location
}
